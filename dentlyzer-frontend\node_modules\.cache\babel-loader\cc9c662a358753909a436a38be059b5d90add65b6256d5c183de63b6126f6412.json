{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\assistant\\\\ProcedureRequests.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\nimport Navbar from '../student/Navbar';\nimport AssistantSidebar from './AssistantSidebar';\nimport Loader from '../components/Loader';\nimport { motion } from 'framer-motion';\nimport { FaCheckCircle, FaTimesCircle, FaSearch, FaFilter, FaClipboardList, FaExclamationTriangle, FaInfoCircle } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProcedureRequests = () => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [procedureRequests, setProcedureRequests] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [selectedRequest, setSelectedRequest] = useState(null);\n  const [showResponseModal, setShowResponseModal] = useState(false);\n  const [responseData, setResponseData] = useState({\n    status: 'approved',\n    responseNotes: ''\n  });\n  const navigate = useNavigate();\n  const {\n    user,\n    token\n  } = useAuth();\n  useEffect(() => {\n    const fetchProcedureRequests = async () => {\n      if (!user || !token) {\n        setError('Please log in to view procedure requests.');\n        setLoading(false);\n        return;\n      }\n      try {\n        const config = {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        };\n        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/procedure-requests`, config);\n        if (Array.isArray(response.data)) {\n          setProcedureRequests(response.data);\n        } else {\n          setError('Invalid data received from server');\n          setProcedureRequests([]);\n        }\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response3$data;\n        console.error('Fetch error:', (_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status, (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data);\n        setError(((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || 'Failed to load procedure requests');\n        setProcedureRequests([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchProcedureRequests();\n  }, [user, token]);\n  const handleSearch = e => setSearchTerm(e.target.value);\n  const handleStatusFilterChange = e => setStatusFilter(e.target.value);\n  const filteredRequests = procedureRequests.filter(request => {\n    var _request$patientName, _request$patientNatio, _request$studentName, _request$procedureTyp;\n    // Apply status filter\n    if (statusFilter !== 'all' && request.status !== statusFilter) {\n      return false;\n    }\n\n    // Apply search filter\n    const searchTermLower = searchTerm.toLowerCase();\n    return ((_request$patientName = request.patientName) === null || _request$patientName === void 0 ? void 0 : _request$patientName.toLowerCase().includes(searchTermLower)) || ((_request$patientNatio = request.patientNationalId) === null || _request$patientNatio === void 0 ? void 0 : _request$patientNatio.includes(searchTerm)) || ((_request$studentName = request.studentName) === null || _request$studentName === void 0 ? void 0 : _request$studentName.toLowerCase().includes(searchTermLower)) || ((_request$procedureTyp = request.procedureType) === null || _request$procedureTyp === void 0 ? void 0 : _request$procedureTyp.toLowerCase().includes(searchTermLower));\n  });\n  const handleResponseSubmit = async e => {\n    e.preventDefault();\n    if (!selectedRequest) return;\n    try {\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n      const response = await axios.put(`${process.env.REACT_APP_API_URL}/api/procedure-requests/${selectedRequest._id}`, responseData, config);\n      if (response.data) {\n        // Update the procedure request in the list\n        setProcedureRequests(prevRequests => prevRequests.map(req => req._id === selectedRequest._id ? response.data.procedureRequest : req));\n        setShowResponseModal(false);\n        setSelectedRequest(null);\n        setResponseData({\n          status: 'approved',\n          responseNotes: ''\n        });\n      }\n    } catch (err) {\n      var _err$response4, _err$response5, _err$response6, _err$response6$data;\n      console.error('Response error:', (_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status, (_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : _err$response5.data);\n      setError(((_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.message) || 'Failed to respond to procedure request');\n    }\n  };\n  const getStatusBadgeClass = status => {\n    switch (status) {\n      case 'approved':\n        return 'bg-green-100 text-green-800';\n      case 'rejected':\n        return 'bg-red-100 text-red-800';\n      case 'pending':\n      default:\n        return 'bg-yellow-100 text-yellow-800';\n    }\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 129,\n    columnNumber: 23\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(AssistantSidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: [error && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: -20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n                className: \"w-5 h-5 text-red-500 mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-700 font-medium\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\",\n                  children: \"Procedure Requests\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-[#333333]\",\n                  children: \"Manage student procedure requests\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col md:flex-row items-center gap-4 w-full md:w-auto\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  whileHover: {\n                    scale: 1.02\n                  },\n                  className: \"relative w-full md:w-64\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                    children: /*#__PURE__*/_jsxDEV(FaSearch, {\n                      className: \"h-5 w-5 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 171,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    placeholder: \"Search requests...\",\n                    value: searchTerm,\n                    onChange: handleSearch,\n                    className: \"block w-full pl-10 pr-3 py-2.5 border border-gray-200 rounded-lg bg-white shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#20B2AA] focus:bg-white sm:text-sm transition-all duration-200\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: statusFilter,\n                  onChange: handleStatusFilterChange,\n                  className: \"w-full md:w-auto px-4 py-2.5 border border-gray-200 rounded-lg bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-[#20B2AA] sm:text-sm transition-all duration-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"all\",\n                    children: \"All Statuses\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"pending\",\n                    children: \"Pending\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"approved\",\n                    children: \"Approved\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"rejected\",\n                    children: \"Rejected\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.1\n              },\n              className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-4 md:p-6\",\n              children: filteredRequests.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"overflow-x-auto\",\n                children: /*#__PURE__*/_jsxDEV(\"table\", {\n                  className: \"min-w-full divide-y divide-gray-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                    className: \"bg-[rgba(0,119,182,0.05)]\",\n                    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider\",\n                        children: \"Date\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 205,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider\",\n                        children: \"Student\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 206,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider\",\n                        children: \"Patient\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 207,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider\",\n                        children: \"Procedure\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 208,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider\",\n                        children: \"Status\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 209,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"px-6 py-3 text-right text-xs font-medium text-[#333333] uppercase tracking-wider\",\n                        children: \"Actions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 210,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 204,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    className: \"bg-white divide-y divide-gray-200\",\n                    children: filteredRequests.map(request => /*#__PURE__*/_jsxDEV(\"tr\", {\n                      className: \"hover:bg-[rgba(0,119,182,0.05)] cursor-pointer\",\n                      onClick: () => {\n                        setSelectedRequest(request);\n                        if (request.status === 'pending') {\n                          setShowResponseModal(true);\n                        }\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                        children: new Date(request.requestDate).toLocaleDateString('en-US', {\n                          year: 'numeric',\n                          month: 'short',\n                          day: 'numeric'\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 225,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                        children: request.studentName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 232,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                        children: request.patientName ? request.patientNationalId ? `${request.patientName} (${request.patientNationalId})` : request.patientName : 'No patient specified'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 233,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                        children: request.procedureType\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 240,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-6 py-4 whitespace-nowrap\",\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(request.status)}`,\n                          children: request.status\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 242,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 241,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                        children: [request.status === 'pending' && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-end space-x-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                            onClick: e => {\n                              e.stopPropagation();\n                              setSelectedRequest(request);\n                              setResponseData({\n                                status: 'approved',\n                                responseNotes: ''\n                              });\n                              setShowResponseModal(true);\n                            },\n                            className: \"text-green-600 hover:text-green-800\",\n                            title: \"Approve Request\",\n                            children: /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                              className: \"inline h-5 w-5\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 262,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 249,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                            onClick: e => {\n                              e.stopPropagation();\n                              setSelectedRequest(request);\n                              setResponseData({\n                                status: 'rejected',\n                                responseNotes: ''\n                              });\n                              setShowResponseModal(true);\n                            },\n                            className: \"text-red-600 hover:text-red-800\",\n                            title: \"Reject Request\",\n                            children: /*#__PURE__*/_jsxDEV(FaTimesCircle, {\n                              className: \"inline h-5 w-5\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 277,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 264,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 248,\n                          columnNumber: 33\n                        }, this), request.status !== 'pending' && /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: e => {\n                            e.stopPropagation();\n                            setSelectedRequest(request);\n                          },\n                          className: \"text-blue-600 hover:text-blue-800\",\n                          title: \"View Details\",\n                          children: /*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                            className: \"inline h-5 w-5\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 290,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 282,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 246,\n                        columnNumber: 29\n                      }, this)]\n                    }, request._id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 215,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center py-12\",\n                children: [/*#__PURE__*/_jsxDEV(FaClipboardList, {\n                  className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: \"No procedure requests found\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-2 text-sm text-gray-500\",\n                  children: searchTerm || statusFilter !== 'all' ? 'Try adjusting your search or filter criteria' : 'There are no procedure requests at this time'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), showResponseModal && selectedRequest && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        className: \"bg-white rounded-xl p-6 w-full max-w-md shadow-2xl border border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-[#0077B6]\",\n            children: [responseData.status === 'approved' ? 'Approve' : 'Reject', \" Request\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowResponseModal(false),\n            className: \"text-gray-500 hover:text-gray-700\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleResponseSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 p-4 rounded-lg mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-700 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Student:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 21\n                }, this), \" \", selectedRequest.studentName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-700 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Patient:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 21\n                }, this), \" \", selectedRequest.patientName ? selectedRequest.patientNationalId ? `${selectedRequest.patientName} (${selectedRequest.patientNationalId})` : selectedRequest.patientName : 'No patient specified']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-700 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Procedure:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 21\n                }, this), \" \", selectedRequest.procedureType]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-700\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Notes:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 21\n                }, this), \" \", selectedRequest.notes || 'None']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Response Notes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"responseNotes\",\n                value: responseData.responseNotes,\n                onChange: e => setResponseData({\n                  ...responseData,\n                  responseNotes: e.target.value\n                }),\n                rows: \"4\",\n                placeholder: \"Provide any additional information about your decision\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              type: \"button\",\n              onClick: () => setShowResponseModal(false),\n              className: \"px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 font-medium transition-colors\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              type: \"submit\",\n              className: `px-6 py-2 text-white rounded-full font-medium transition-colors shadow-md ${responseData.status === 'approved' ? 'bg-gradient-to-r from-green-500 to-green-700 hover:from-green-600 hover:to-green-800' : 'bg-gradient-to-r from-red-500 to-red-700 hover:from-red-600 hover:to-red-800'}`,\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: responseData.status === 'approved' ? 'Approve' : 'Reject'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 5\n  }, this);\n};\n_s(ProcedureRequests, \"67bRctfNPcMrbuJGGap3JSOAyHc=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = ProcedureRequests;\nexport default ProcedureRequests;\nvar _c;\n$RefreshReg$(_c, \"ProcedureRequests\");", "map": {"version": 3, "names": ["useState", "useEffect", "useNavigate", "axios", "useAuth", "<PERSON><PERSON><PERSON>", "Assistant<PERSON><PERSON><PERSON>", "Loader", "motion", "FaCheckCircle", "FaTimesCircle", "FaSearch", "FaFilter", "FaClipboardList", "FaExclamationTriangle", "FaInfoCircle", "jsxDEV", "_jsxDEV", "ProcedureRequests", "_s", "sidebarOpen", "setSidebarOpen", "procedureRequests", "setProcedureRequests", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "selectedRequest", "setSelectedRequest", "showResponseModal", "setShowResponseModal", "responseData", "setResponseData", "status", "responseNotes", "navigate", "user", "token", "fetchProcedureRequests", "config", "headers", "Authorization", "response", "get", "process", "env", "REACT_APP_API_URL", "Array", "isArray", "data", "err", "_err$response", "_err$response2", "_err$response3", "_err$response3$data", "console", "message", "handleSearch", "e", "target", "value", "handleStatusFilterChange", "filteredRequests", "filter", "request", "_request$patientName", "_request$patientNatio", "_request$studentName", "_request$procedureTyp", "searchTermLower", "toLowerCase", "patientName", "includes", "patientNationalId", "studentName", "procedureType", "handleResponseSubmit", "preventDefault", "put", "_id", "prevRequests", "map", "req", "procedureRequest", "_err$response4", "_err$response5", "_err$response6", "_err$response6$data", "getStatusBadgeClass", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "isOpen", "setIsOpen", "toggleSidebar", "div", "initial", "opacity", "y", "animate", "transition", "duration", "whileHover", "scale", "type", "placeholder", "onChange", "delay", "length", "onClick", "Date", "requestDate", "toLocaleDateString", "year", "month", "day", "stopPropagation", "title", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onSubmit", "notes", "name", "rows", "button", "whileTap", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/assistant/ProcedureRequests.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\nimport Navbar from '../student/Navbar';\nimport AssistantSidebar from './AssistantSidebar';\nimport Loader from '../components/Loader';\nimport { motion } from 'framer-motion';\nimport {\n  FaCheckCircle,\n  FaTimesCircle,\n  FaSearch,\n  FaFilter,\n  FaClipboardList,\n  FaExclamationTriangle,\n  FaInfoCircle,\n} from 'react-icons/fa';\n\nconst ProcedureRequests = () => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [procedureRequests, setProcedureRequests] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [selectedRequest, setSelectedRequest] = useState(null);\n  const [showResponseModal, setShowResponseModal] = useState(false);\n  const [responseData, setResponseData] = useState({\n    status: 'approved',\n    responseNotes: '',\n  });\n  const navigate = useNavigate();\n  const { user, token } = useAuth();\n\n  useEffect(() => {\n    const fetchProcedureRequests = async () => {\n      if (!user || !token) {\n        setError('Please log in to view procedure requests.');\n        setLoading(false);\n        return;\n      }\n\n      try {\n        const config = { headers: { Authorization: `Bearer ${token}` } };\n        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/procedure-requests`, config);\n\n        if (Array.isArray(response.data)) {\n          setProcedureRequests(response.data);\n        } else {\n          setError('Invalid data received from server');\n          setProcedureRequests([]);\n        }\n      } catch (err) {\n        console.error('Fetch error:', err.response?.status, err.response?.data);\n        setError(err.response?.data?.message || 'Failed to load procedure requests');\n        setProcedureRequests([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchProcedureRequests();\n  }, [user, token]);\n\n  const handleSearch = (e) => setSearchTerm(e.target.value);\n  const handleStatusFilterChange = (e) => setStatusFilter(e.target.value);\n\n  const filteredRequests = procedureRequests.filter(request => {\n    // Apply status filter\n    if (statusFilter !== 'all' && request.status !== statusFilter) {\n      return false;\n    }\n\n    // Apply search filter\n    const searchTermLower = searchTerm.toLowerCase();\n    return (\n      request.patientName?.toLowerCase().includes(searchTermLower) ||\n      request.patientNationalId?.includes(searchTerm) ||\n      request.studentName?.toLowerCase().includes(searchTermLower) ||\n      request.procedureType?.toLowerCase().includes(searchTermLower)\n    );\n  });\n\n  const handleResponseSubmit = async (e) => {\n    e.preventDefault();\n    if (!selectedRequest) return;\n\n    try {\n      const config = { headers: { Authorization: `Bearer ${token}` } };\n      const response = await axios.put(\n        `${process.env.REACT_APP_API_URL}/api/procedure-requests/${selectedRequest._id}`,\n        responseData,\n        config\n      );\n\n      if (response.data) {\n        // Update the procedure request in the list\n        setProcedureRequests(prevRequests =>\n          prevRequests.map(req =>\n            req._id === selectedRequest._id ? response.data.procedureRequest : req\n          )\n        );\n\n        setShowResponseModal(false);\n        setSelectedRequest(null);\n        setResponseData({\n          status: 'approved',\n          responseNotes: '',\n        });\n      }\n    } catch (err) {\n      console.error('Response error:', err.response?.status, err.response?.data);\n      setError(err.response?.data?.message || 'Failed to respond to procedure request');\n    }\n  };\n\n  const getStatusBadgeClass = (status) => {\n    switch (status) {\n      case 'approved':\n        return 'bg-green-100 text-green-800';\n      case 'rejected':\n        return 'bg-red-100 text-red-800';\n      case 'pending':\n      default:\n        return 'bg-yellow-100 text-yellow-800';\n    }\n  };\n\n  if (loading) return <Loader />;\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\">\n      <AssistantSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\n\n        <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\">\n          <div className=\"max-w-7xl mx-auto\">\n            {error && (\n              <motion.div\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                className=\"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\"\n              >\n                <div className=\"flex items-center\">\n                  <FaExclamationTriangle className=\"w-5 h-5 text-red-500 mr-3\" />\n                  <p className=\"text-red-700 font-medium\">{error}</p>\n                </div>\n              </motion.div>\n            )}\n\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.5 }}\n            >\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\">\n                <div>\n                  <h1 className=\"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\">\n                    Procedure Requests\n                  </h1>\n                  <p className=\"text-[#333333]\">Manage student procedure requests</p>\n                </div>\n                <div className=\"flex flex-col md:flex-row items-center gap-4 w-full md:w-auto\">\n                  <motion.div\n                    whileHover={{ scale: 1.02 }}\n                    className=\"relative w-full md:w-64\"\n                  >\n                    <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                      <FaSearch className=\"h-5 w-5 text-gray-400\" />\n                    </div>\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search requests...\"\n                      value={searchTerm}\n                      onChange={handleSearch}\n                      className=\"block w-full pl-10 pr-3 py-2.5 border border-gray-200 rounded-lg bg-white shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#20B2AA] focus:bg-white sm:text-sm transition-all duration-200\"\n                    />\n                  </motion.div>\n                  <select\n                    value={statusFilter}\n                    onChange={handleStatusFilterChange}\n                    className=\"w-full md:w-auto px-4 py-2.5 border border-gray-200 rounded-lg bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-[#20B2AA] sm:text-sm transition-all duration-200\"\n                  >\n                    <option value=\"all\">All Statuses</option>\n                    <option value=\"pending\">Pending</option>\n                    <option value=\"approved\">Approved</option>\n                    <option value=\"rejected\">Rejected</option>\n                  </select>\n                </div>\n              </div>\n\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.1 }}\n                className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-4 md:p-6\"\n              >\n                {filteredRequests.length > 0 ? (\n                  <div className=\"overflow-x-auto\">\n                    <table className=\"min-w-full divide-y divide-gray-200\">\n                      <thead className=\"bg-[rgba(0,119,182,0.05)]\">\n                        <tr>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider\">Date</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider\">Student</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider\">Patient</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider\">Procedure</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider\">Status</th>\n                          <th className=\"px-6 py-3 text-right text-xs font-medium text-[#333333] uppercase tracking-wider\">Actions</th>\n                        </tr>\n                      </thead>\n                      <tbody className=\"bg-white divide-y divide-gray-200\">\n                        {filteredRequests.map((request) => (\n                          <tr\n                            key={request._id}\n                            className=\"hover:bg-[rgba(0,119,182,0.05)] cursor-pointer\"\n                            onClick={() => {\n                              setSelectedRequest(request);\n                              if (request.status === 'pending') {\n                                setShowResponseModal(true);\n                              }\n                            }}\n                          >\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                              {new Date(request.requestDate).toLocaleDateString('en-US', {\n                                year: 'numeric',\n                                month: 'short',\n                                day: 'numeric'\n                              })}\n                            </td>\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{request.studentName}</td>\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                              {request.patientName ?\n                                (request.patientNationalId ?\n                                  `${request.patientName} (${request.patientNationalId})` :\n                                  request.patientName) :\n                                'No patient specified'}\n                            </td>\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{request.procedureType}</td>\n                            <td className=\"px-6 py-4 whitespace-nowrap\">\n                              <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(request.status)}`}>\n                                {request.status}\n                              </span>\n                            </td>\n                            <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                              {request.status === 'pending' && (\n                                <div className=\"flex justify-end space-x-2\">\n                                  <button\n                                    onClick={(e) => {\n                                      e.stopPropagation();\n                                      setSelectedRequest(request);\n                                      setResponseData({\n                                        status: 'approved',\n                                        responseNotes: '',\n                                      });\n                                      setShowResponseModal(true);\n                                    }}\n                                    className=\"text-green-600 hover:text-green-800\"\n                                    title=\"Approve Request\"\n                                  >\n                                    <FaCheckCircle className=\"inline h-5 w-5\" />\n                                  </button>\n                                  <button\n                                    onClick={(e) => {\n                                      e.stopPropagation();\n                                      setSelectedRequest(request);\n                                      setResponseData({\n                                        status: 'rejected',\n                                        responseNotes: '',\n                                      });\n                                      setShowResponseModal(true);\n                                    }}\n                                    className=\"text-red-600 hover:text-red-800\"\n                                    title=\"Reject Request\"\n                                  >\n                                    <FaTimesCircle className=\"inline h-5 w-5\" />\n                                  </button>\n                                </div>\n                              )}\n                              {request.status !== 'pending' && (\n                                <button\n                                  onClick={(e) => {\n                                    e.stopPropagation();\n                                    setSelectedRequest(request);\n                                  }}\n                                  className=\"text-blue-600 hover:text-blue-800\"\n                                  title=\"View Details\"\n                                >\n                                  <FaInfoCircle className=\"inline h-5 w-5\" />\n                                </button>\n                              )}\n                            </td>\n                          </tr>\n                        ))}\n                      </tbody>\n                    </table>\n                  </div>\n                ) : (\n                  <div className=\"text-center py-12\">\n                    <FaClipboardList className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" />\n                    <h3 className=\"text-lg font-medium text-gray-900\">No procedure requests found</h3>\n                    <p className=\"mt-2 text-sm text-gray-500\">\n                      {searchTerm || statusFilter !== 'all'\n                        ? 'Try adjusting your search or filter criteria'\n                        : 'There are no procedure requests at this time'}\n                    </p>\n                  </div>\n                )}\n              </motion.div>\n            </motion.div>\n          </div>\n        </main>\n      </div>\n\n      {/* Response Modal */}\n      {showResponseModal && selectedRequest && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <motion.div\n            initial={{ scale: 0.9, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            className=\"bg-white rounded-xl p-6 w-full max-w-md shadow-2xl border border-gray-100\"\n          >\n            <div className=\"flex justify-between items-center mb-6\">\n              <h2 className=\"text-2xl font-bold text-[#0077B6]\">\n                {responseData.status === 'approved' ? 'Approve' : 'Reject'} Request\n              </h2>\n              <button\n                onClick={() => setShowResponseModal(false)}\n                className=\"text-gray-500 hover:text-gray-700\"\n              >\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n              </button>\n            </div>\n\n            <form onSubmit={handleResponseSubmit}>\n              <div className=\"mb-6\">\n                <div className=\"bg-gray-50 p-4 rounded-lg mb-4\">\n                  <p className=\"text-sm text-gray-700 mb-2\">\n                    <span className=\"font-medium\">Student:</span> {selectedRequest.studentName}\n                  </p>\n                  <p className=\"text-sm text-gray-700 mb-2\">\n                    <span className=\"font-medium\">Patient:</span> {\n                      selectedRequest.patientName ?\n                        (selectedRequest.patientNationalId ?\n                          `${selectedRequest.patientName} (${selectedRequest.patientNationalId})` :\n                          selectedRequest.patientName) :\n                        'No patient specified'\n                    }\n                  </p>\n                  <p className=\"text-sm text-gray-700 mb-2\">\n                    <span className=\"font-medium\">Procedure:</span> {selectedRequest.procedureType}\n                  </p>\n                  <p className=\"text-sm text-gray-700\">\n                    <span className=\"font-medium\">Notes:</span> {selectedRequest.notes || 'None'}\n                  </p>\n                </div>\n\n                <div className=\"mb-4\">\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Response Notes</label>\n                  <textarea\n                    name=\"responseNotes\"\n                    value={responseData.responseNotes}\n                    onChange={(e) => setResponseData({ ...responseData, responseNotes: e.target.value })}\n                    rows=\"4\"\n                    placeholder=\"Provide any additional information about your decision\"\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"flex justify-end space-x-4\">\n                <motion.button\n                  type=\"button\"\n                  onClick={() => setShowResponseModal(false)}\n                  className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 font-medium transition-colors\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  Cancel\n                </motion.button>\n                <motion.button\n                  type=\"submit\"\n                  className={`px-6 py-2 text-white rounded-full font-medium transition-colors shadow-md ${\n                    responseData.status === 'approved'\n                      ? 'bg-gradient-to-r from-green-500 to-green-700 hover:from-green-600 hover:to-green-800'\n                      : 'bg-gradient-to-r from-red-500 to-red-700 hover:from-red-600 hover:to-red-800'\n                  }`}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  {responseData.status === 'approved' ? 'Approve' : 'Reject'}\n                </motion.button>\n              </div>\n            </form>\n          </motion.div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ProcedureRequests;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,aAAa,EACbC,aAAa,EACbC,QAAQ,EACRC,QAAQ,EACRC,eAAe,EACfC,qBAAqB,EACrBC,YAAY,QACP,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACkC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC;IAC/CsC,MAAM,EAAE,UAAU;IAClBC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEuC,IAAI;IAAEC;EAAM,CAAC,GAAGtC,OAAO,CAAC,CAAC;EAEjCH,SAAS,CAAC,MAAM;IACd,MAAM0C,sBAAsB,GAAG,MAAAA,CAAA,KAAY;MACzC,IAAI,CAACF,IAAI,IAAI,CAACC,KAAK,EAAE;QACnBf,QAAQ,CAAC,2CAA2C,CAAC;QACrDF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,IAAI;QACF,MAAMmB,MAAM,GAAG;UAAEC,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAUJ,KAAK;UAAG;QAAE,CAAC;QAChE,MAAMK,QAAQ,GAAG,MAAM5C,KAAK,CAAC6C,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,yBAAyB,EAAEP,MAAM,CAAC;QAEnG,IAAIQ,KAAK,CAACC,OAAO,CAACN,QAAQ,CAACO,IAAI,CAAC,EAAE;UAChC/B,oBAAoB,CAACwB,QAAQ,CAACO,IAAI,CAAC;QACrC,CAAC,MAAM;UACL3B,QAAQ,CAAC,mCAAmC,CAAC;UAC7CJ,oBAAoB,CAAC,EAAE,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOgC,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZC,OAAO,CAAClC,KAAK,CAAC,cAAc,GAAA8B,aAAA,GAAED,GAAG,CAACR,QAAQ,cAAAS,aAAA,uBAAZA,aAAA,CAAclB,MAAM,GAAAmB,cAAA,GAAEF,GAAG,CAACR,QAAQ,cAAAU,cAAA,uBAAZA,cAAA,CAAcH,IAAI,CAAC;QACvE3B,QAAQ,CAAC,EAAA+B,cAAA,GAAAH,GAAG,CAACR,QAAQ,cAAAW,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcJ,IAAI,cAAAK,mBAAA,uBAAlBA,mBAAA,CAAoBE,OAAO,KAAI,mCAAmC,CAAC;QAC5EtC,oBAAoB,CAAC,EAAE,CAAC;MAC1B,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDkB,sBAAsB,CAAC,CAAC;EAC1B,CAAC,EAAE,CAACF,IAAI,EAAEC,KAAK,CAAC,CAAC;EAEjB,MAAMoB,YAAY,GAAIC,CAAC,IAAKlC,aAAa,CAACkC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EACzD,MAAMC,wBAAwB,GAAIH,CAAC,IAAKhC,eAAe,CAACgC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAEvE,MAAME,gBAAgB,GAAG7C,iBAAiB,CAAC8C,MAAM,CAACC,OAAO,IAAI;IAAA,IAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA;IAC3D;IACA,IAAI3C,YAAY,KAAK,KAAK,IAAIuC,OAAO,CAAC/B,MAAM,KAAKR,YAAY,EAAE;MAC7D,OAAO,KAAK;IACd;;IAEA;IACA,MAAM4C,eAAe,GAAG9C,UAAU,CAAC+C,WAAW,CAAC,CAAC;IAChD,OACE,EAAAL,oBAAA,GAAAD,OAAO,CAACO,WAAW,cAAAN,oBAAA,uBAAnBA,oBAAA,CAAqBK,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,eAAe,CAAC,OAAAH,qBAAA,GAC5DF,OAAO,CAACS,iBAAiB,cAAAP,qBAAA,uBAAzBA,qBAAA,CAA2BM,QAAQ,CAACjD,UAAU,CAAC,OAAA4C,oBAAA,GAC/CH,OAAO,CAACU,WAAW,cAAAP,oBAAA,uBAAnBA,oBAAA,CAAqBG,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,eAAe,CAAC,OAAAD,qBAAA,GAC5DJ,OAAO,CAACW,aAAa,cAAAP,qBAAA,uBAArBA,qBAAA,CAAuBE,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,eAAe,CAAC;EAElE,CAAC,CAAC;EAEF,MAAMO,oBAAoB,GAAG,MAAOlB,CAAC,IAAK;IACxCA,CAAC,CAACmB,cAAc,CAAC,CAAC;IAClB,IAAI,CAAClD,eAAe,EAAE;IAEtB,IAAI;MACF,MAAMY,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUJ,KAAK;QAAG;MAAE,CAAC;MAChE,MAAMK,QAAQ,GAAG,MAAM5C,KAAK,CAACgF,GAAG,CAC9B,GAAGlC,OAAO,CAACC,GAAG,CAACC,iBAAiB,2BAA2BnB,eAAe,CAACoD,GAAG,EAAE,EAChFhD,YAAY,EACZQ,MACF,CAAC;MAED,IAAIG,QAAQ,CAACO,IAAI,EAAE;QACjB;QACA/B,oBAAoB,CAAC8D,YAAY,IAC/BA,YAAY,CAACC,GAAG,CAACC,GAAG,IAClBA,GAAG,CAACH,GAAG,KAAKpD,eAAe,CAACoD,GAAG,GAAGrC,QAAQ,CAACO,IAAI,CAACkC,gBAAgB,GAAGD,GACrE,CACF,CAAC;QAEDpD,oBAAoB,CAAC,KAAK,CAAC;QAC3BF,kBAAkB,CAAC,IAAI,CAAC;QACxBI,eAAe,CAAC;UACdC,MAAM,EAAE,UAAU;UAClBC,aAAa,EAAE;QACjB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOgB,GAAG,EAAE;MAAA,IAAAkC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;MACZhC,OAAO,CAAClC,KAAK,CAAC,iBAAiB,GAAA+D,cAAA,GAAElC,GAAG,CAACR,QAAQ,cAAA0C,cAAA,uBAAZA,cAAA,CAAcnD,MAAM,GAAAoD,cAAA,GAAEnC,GAAG,CAACR,QAAQ,cAAA2C,cAAA,uBAAZA,cAAA,CAAcpC,IAAI,CAAC;MAC1E3B,QAAQ,CAAC,EAAAgE,cAAA,GAAApC,GAAG,CAACR,QAAQ,cAAA4C,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcrC,IAAI,cAAAsC,mBAAA,uBAAlBA,mBAAA,CAAoB/B,OAAO,KAAI,wCAAwC,CAAC;IACnF;EACF,CAAC;EAED,MAAMgC,mBAAmB,GAAIvD,MAAM,IAAK;IACtC,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,6BAA6B;MACtC,KAAK,UAAU;QACb,OAAO,yBAAyB;MAClC,KAAK,SAAS;MACd;QACE,OAAO,+BAA+B;IAC1C;EACF,CAAC;EAED,IAAId,OAAO,EAAE,oBAAOP,OAAA,CAACV,MAAM;IAAAuF,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAE9B,oBACEhF,OAAA;IAAKiF,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvClF,OAAA,CAACX,gBAAgB;MAAC8F,MAAM,EAAEhF,WAAY;MAACiF,SAAS,EAAEhF;IAAe;MAAAyE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEpEhF,OAAA;MAAKiF,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDlF,OAAA,CAACZ,MAAM;QAACiG,aAAa,EAAEA,CAAA,KAAMjF,cAAc,CAAC,CAACD,WAAW;MAAE;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE7DhF,OAAA;QAAMiF,SAAS,EAAC,0FAA0F;QAAAC,QAAA,eACxGlF,OAAA;UAAKiF,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAC/BzE,KAAK,iBACJT,OAAA,CAACT,MAAM,CAAC+F,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BR,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAE7ElF,OAAA;cAAKiF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClF,OAAA,CAACH,qBAAqB;gBAACoF,SAAS,EAAC;cAA2B;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/DhF,OAAA;gBAAGiF,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAEzE;cAAK;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eAEDhF,OAAA,CAACT,MAAM,CAAC+F,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBG,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAV,QAAA,gBAE9BlF,OAAA;cAAKiF,SAAS,EAAC,kFAAkF;cAAAC,QAAA,gBAC/FlF,OAAA;gBAAAkF,QAAA,gBACElF,OAAA;kBAAIiF,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAAC;gBAEnE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhF,OAAA;kBAAGiF,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAiC;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACNhF,OAAA;gBAAKiF,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,gBAC5ElF,OAAA,CAACT,MAAM,CAAC+F,GAAG;kBACTO,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5Bb,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBAEnClF,OAAA;oBAAKiF,SAAS,EAAC,sEAAsE;oBAAAC,QAAA,eACnFlF,OAAA,CAACN,QAAQ;sBAACuF,SAAS,EAAC;oBAAuB;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,eACNhF,OAAA;oBACE+F,IAAI,EAAC,MAAM;oBACXC,WAAW,EAAC,oBAAoB;oBAChChD,KAAK,EAAErC,UAAW;oBAClBsF,QAAQ,EAAEpD,YAAa;oBACvBoC,SAAS,EAAC;kBAAqN;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eACbhF,OAAA;kBACEgD,KAAK,EAAEnC,YAAa;kBACpBoF,QAAQ,EAAEhD,wBAAyB;kBACnCgC,SAAS,EAAC,+KAA+K;kBAAAC,QAAA,gBAEzLlF,OAAA;oBAAQgD,KAAK,EAAC,KAAK;oBAAAkC,QAAA,EAAC;kBAAY;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACzChF,OAAA;oBAAQgD,KAAK,EAAC,SAAS;oBAAAkC,QAAA,EAAC;kBAAO;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxChF,OAAA;oBAAQgD,KAAK,EAAC,UAAU;oBAAAkC,QAAA,EAAC;kBAAQ;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC1ChF,OAAA;oBAAQgD,KAAK,EAAC,UAAU;oBAAAkC,QAAA,EAAC;kBAAQ;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENhF,OAAA,CAACT,MAAM,CAAC+F,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEO,KAAK,EAAE;cAAI,CAAE;cAC3BjB,SAAS,EAAC,0HAA0H;cAAAC,QAAA,EAEnIhC,gBAAgB,CAACiD,MAAM,GAAG,CAAC,gBAC1BnG,OAAA;gBAAKiF,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,eAC9BlF,OAAA;kBAAOiF,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBACpDlF,OAAA;oBAAOiF,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,eAC1ClF,OAAA;sBAAAkF,QAAA,gBACElF,OAAA;wBAAIiF,SAAS,EAAC,iFAAiF;wBAAAC,QAAA,EAAC;sBAAI;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACzGhF,OAAA;wBAAIiF,SAAS,EAAC,iFAAiF;wBAAAC,QAAA,EAAC;sBAAO;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC5GhF,OAAA;wBAAIiF,SAAS,EAAC,iFAAiF;wBAAAC,QAAA,EAAC;sBAAO;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC5GhF,OAAA;wBAAIiF,SAAS,EAAC,iFAAiF;wBAAAC,QAAA,EAAC;sBAAS;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC9GhF,OAAA;wBAAIiF,SAAS,EAAC,iFAAiF;wBAAAC,QAAA,EAAC;sBAAM;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC3GhF,OAAA;wBAAIiF,SAAS,EAAC,kFAAkF;wBAAAC,QAAA,EAAC;sBAAO;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3G;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACRhF,OAAA;oBAAOiF,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EACjDhC,gBAAgB,CAACmB,GAAG,CAAEjB,OAAO,iBAC5BpD,OAAA;sBAEEiF,SAAS,EAAC,gDAAgD;sBAC1DmB,OAAO,EAAEA,CAAA,KAAM;wBACbpF,kBAAkB,CAACoC,OAAO,CAAC;wBAC3B,IAAIA,OAAO,CAAC/B,MAAM,KAAK,SAAS,EAAE;0BAChCH,oBAAoB,CAAC,IAAI,CAAC;wBAC5B;sBACF,CAAE;sBAAAgE,QAAA,gBAEFlF,OAAA;wBAAIiF,SAAS,EAAC,+DAA+D;wBAAAC,QAAA,EAC1E,IAAImB,IAAI,CAACjD,OAAO,CAACkD,WAAW,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;0BACzDC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,OAAO;0BACdC,GAAG,EAAE;wBACP,CAAC;sBAAC;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACLhF,OAAA;wBAAIiF,SAAS,EAAC,mDAAmD;wBAAAC,QAAA,EAAE9B,OAAO,CAACU;sBAAW;wBAAAe,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC5FhF,OAAA;wBAAIiF,SAAS,EAAC,mDAAmD;wBAAAC,QAAA,EAC9D9B,OAAO,CAACO,WAAW,GACjBP,OAAO,CAACS,iBAAiB,GACxB,GAAGT,OAAO,CAACO,WAAW,KAAKP,OAAO,CAACS,iBAAiB,GAAG,GACvDT,OAAO,CAACO,WAAW,GACrB;sBAAsB;wBAAAkB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB,CAAC,eACLhF,OAAA;wBAAIiF,SAAS,EAAC,mDAAmD;wBAAAC,QAAA,EAAE9B,OAAO,CAACW;sBAAa;wBAAAc,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC9FhF,OAAA;wBAAIiF,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,eACzClF,OAAA;0BAAMiF,SAAS,EAAE,sEAAsEL,mBAAmB,CAACxB,OAAO,CAAC/B,MAAM,CAAC,EAAG;0BAAA6D,QAAA,EAC1H9B,OAAO,CAAC/B;wBAAM;0BAAAwD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACX;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACLhF,OAAA;wBAAIiF,SAAS,EAAC,4DAA4D;wBAAAC,QAAA,GACvE9B,OAAO,CAAC/B,MAAM,KAAK,SAAS,iBAC3BrB,OAAA;0BAAKiF,SAAS,EAAC,4BAA4B;0BAAAC,QAAA,gBACzClF,OAAA;4BACEoG,OAAO,EAAGtD,CAAC,IAAK;8BACdA,CAAC,CAAC6D,eAAe,CAAC,CAAC;8BACnB3F,kBAAkB,CAACoC,OAAO,CAAC;8BAC3BhC,eAAe,CAAC;gCACdC,MAAM,EAAE,UAAU;gCAClBC,aAAa,EAAE;8BACjB,CAAC,CAAC;8BACFJ,oBAAoB,CAAC,IAAI,CAAC;4BAC5B,CAAE;4BACF+D,SAAS,EAAC,qCAAqC;4BAC/C2B,KAAK,EAAC,iBAAiB;4BAAA1B,QAAA,eAEvBlF,OAAA,CAACR,aAAa;8BAACyF,SAAS,EAAC;4BAAgB;8BAAAJ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtC,CAAC,eACThF,OAAA;4BACEoG,OAAO,EAAGtD,CAAC,IAAK;8BACdA,CAAC,CAAC6D,eAAe,CAAC,CAAC;8BACnB3F,kBAAkB,CAACoC,OAAO,CAAC;8BAC3BhC,eAAe,CAAC;gCACdC,MAAM,EAAE,UAAU;gCAClBC,aAAa,EAAE;8BACjB,CAAC,CAAC;8BACFJ,oBAAoB,CAAC,IAAI,CAAC;4BAC5B,CAAE;4BACF+D,SAAS,EAAC,iCAAiC;4BAC3C2B,KAAK,EAAC,gBAAgB;4BAAA1B,QAAA,eAEtBlF,OAAA,CAACP,aAAa;8BAACwF,SAAS,EAAC;4BAAgB;8BAAAJ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN,CACN,EACA5B,OAAO,CAAC/B,MAAM,KAAK,SAAS,iBAC3BrB,OAAA;0BACEoG,OAAO,EAAGtD,CAAC,IAAK;4BACdA,CAAC,CAAC6D,eAAe,CAAC,CAAC;4BACnB3F,kBAAkB,CAACoC,OAAO,CAAC;0BAC7B,CAAE;0BACF6B,SAAS,EAAC,mCAAmC;0BAC7C2B,KAAK,EAAC,cAAc;0BAAA1B,QAAA,eAEpBlF,OAAA,CAACF,YAAY;4BAACmF,SAAS,EAAC;0BAAgB;4BAAAJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrC,CACT;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA,GA7EA5B,OAAO,CAACe,GAAG;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA8Ed,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,gBAENhF,OAAA;gBAAKiF,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChClF,OAAA,CAACJ,eAAe;kBAACqF,SAAS,EAAC;gBAAsC;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpEhF,OAAA;kBAAIiF,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAA2B;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClFhF,OAAA;kBAAGiF,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EACtCvE,UAAU,IAAIE,YAAY,KAAK,KAAK,GACjC,8CAA8C,GAC9C;gBAA8C;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAGL/D,iBAAiB,IAAIF,eAAe,iBACnCf,OAAA;MAAKiF,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7FlF,OAAA,CAACT,MAAM,CAAC+F,GAAG;QACTC,OAAO,EAAE;UAAEO,KAAK,EAAE,GAAG;UAAEN,OAAO,EAAE;QAAE,CAAE;QACpCE,OAAO,EAAE;UAAEI,KAAK,EAAE,CAAC;UAAEN,OAAO,EAAE;QAAE,CAAE;QAClCP,SAAS,EAAC,2EAA2E;QAAAC,QAAA,gBAErFlF,OAAA;UAAKiF,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDlF,OAAA;YAAIiF,SAAS,EAAC,mCAAmC;YAAAC,QAAA,GAC9C/D,YAAY,CAACE,MAAM,KAAK,UAAU,GAAG,SAAS,GAAG,QAAQ,EAAC,UAC7D;UAAA;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLhF,OAAA;YACEoG,OAAO,EAAEA,CAAA,KAAMlF,oBAAoB,CAAC,KAAK,CAAE;YAC3C+D,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAE7ClF,OAAA;cAAK6G,KAAK,EAAC,4BAA4B;cAAC5B,SAAS,EAAC,SAAS;cAAC6B,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAA9B,QAAA,eAC/GlF,OAAA;gBAAMiH,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAsB;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENhF,OAAA;UAAMqH,QAAQ,EAAErD,oBAAqB;UAAAkB,QAAA,gBACnClF,OAAA;YAAKiF,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBlF,OAAA;cAAKiF,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7ClF,OAAA;gBAAGiF,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACvClF,OAAA;kBAAMiF,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAACjE,eAAe,CAAC+C,WAAW;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACJhF,OAAA;gBAAGiF,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACvClF,OAAA;kBAAMiF,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAC5CjE,eAAe,CAAC4C,WAAW,GACxB5C,eAAe,CAAC8C,iBAAiB,GAChC,GAAG9C,eAAe,CAAC4C,WAAW,KAAK5C,eAAe,CAAC8C,iBAAiB,GAAG,GACvE9C,eAAe,CAAC4C,WAAW,GAC7B,sBAAsB;cAAA;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEzB,CAAC,eACJhF,OAAA;gBAAGiF,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACvClF,OAAA;kBAAMiF,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAU;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAACjE,eAAe,CAACgD,aAAa;cAAA;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC,eACJhF,OAAA;gBAAGiF,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBAClClF,OAAA;kBAAMiF,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAM;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAACjE,eAAe,CAACuG,KAAK,IAAI,MAAM;cAAA;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENhF,OAAA;cAAKiF,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBlF,OAAA;gBAAOiF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAc;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtFhF,OAAA;gBACEuH,IAAI,EAAC,eAAe;gBACpBvE,KAAK,EAAE7B,YAAY,CAACG,aAAc;gBAClC2E,QAAQ,EAAGnD,CAAC,IAAK1B,eAAe,CAAC;kBAAE,GAAGD,YAAY;kBAAEG,aAAa,EAAEwB,CAAC,CAACC,MAAM,CAACC;gBAAM,CAAC,CAAE;gBACrFwE,IAAI,EAAC,GAAG;gBACRxB,WAAW,EAAC,wDAAwD;gBACpEf,SAAS,EAAC;cAA6G;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhF,OAAA;YAAKiF,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzClF,OAAA,CAACT,MAAM,CAACkI,MAAM;cACZ1B,IAAI,EAAC,QAAQ;cACbK,OAAO,EAAEA,CAAA,KAAMlF,oBAAoB,CAAC,KAAK,CAAE;cAC3C+D,SAAS,EAAC,4GAA4G;cACtHY,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5B4B,QAAQ,EAAE;gBAAE5B,KAAK,EAAE;cAAK,CAAE;cAAAZ,QAAA,EAC3B;YAED;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChBhF,OAAA,CAACT,MAAM,CAACkI,MAAM;cACZ1B,IAAI,EAAC,QAAQ;cACbd,SAAS,EAAE,6EACT9D,YAAY,CAACE,MAAM,KAAK,UAAU,GAC9B,sFAAsF,GACtF,8EAA8E,EACjF;cACHwE,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5B4B,QAAQ,EAAE;gBAAE5B,KAAK,EAAE;cAAK,CAAE;cAAAZ,QAAA,EAEzB/D,YAAY,CAACE,MAAM,KAAK,UAAU,GAAG,SAAS,GAAG;YAAQ;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9E,EAAA,CAhYID,iBAAiB;EAAA,QAaJhB,WAAW,EACJE,OAAO;AAAA;AAAAwI,EAAA,GAd3B1H,iBAAiB;AAkYvB,eAAeA,iBAAiB;AAAC,IAAA0H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}