{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\superadmin\\\\SuperAdminSidebar.jsx\",\n  _s = $RefreshSig$();\nimport { Link } from 'react-router-dom';\nimport { FaHome, FaUniversity, FaUsers, FaChartBar, FaList, FaNewspaper, FaTooth } from 'react-icons/fa';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SuperAdminSidebar = ({\n  isOpen,\n  setIsOpen\n}) => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const navItems = [{\n    name: 'Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(FaHome, {\n      className: \"h-5 w-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 32\n    }, this),\n    path: '/superadmin/dashboard'\n  }, {\n    name: 'Universities',\n    icon: /*#__PURE__*/_jsxDEV(FaUniversity, {\n      className: \"h-5 w-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 35\n    }, this),\n    path: '/superadmin/universities'\n  }, {\n    name: 'Accounts',\n    icon: /*#__PURE__*/_jsxDEV(FaUsers, {\n      className: \"h-5 w-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 31\n    }, this),\n    path: '/superadmin/accounts'\n  }, {\n    name: 'Analytics',\n    icon: /*#__PURE__*/_jsxDEV(FaChartBar, {\n      className: \"h-5 w-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 32\n    }, this),\n    path: '/superadmin/analytics'\n  }, {\n    name: 'Activity',\n    icon: /*#__PURE__*/_jsxDEV(FaList, {\n      className: \"h-5 w-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 31\n    }, this),\n    path: '/superadmin/activity'\n  }, {\n    name: 'News',\n    icon: /*#__PURE__*/_jsxDEV(FaNewspaper, {\n      className: \"h-5 w-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 27\n    }, this),\n    path: '/superadmin/news'\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 z-10 md:hidden\",\n      onClick: () => setIsOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `fixed md:relative z-20 h-full transition-all duration-300 ease-in-out ${isOpen ? 'translate-x-0 w-64' : '-translate-x-full md:translate-x-0 w-20'} bg-white shadow-lg flex flex-col`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 flex items-center justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(FaTooth, {\n            className: \"w-8 h-8 text-[#0077B6] transition-transform duration-300 hover:scale-110\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), isOpen && /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/imgs/odenta-logo2.jpg\" // Update this path based on your project structure\n            ,\n            alt: \"ODenta Logo\",\n            className: \"h-10 w-auto\" // Adjust size as needed\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"flex-1 px-2 py-4 overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"space-y-2\",\n          children: navItems.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: item.path,\n              className: \"flex items-center p-3 text-gray-600 hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.1)] rounded-lg transition-colors group\",\n              onClick: () => window.innerWidth < 768 && setIsOpen(false),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-500 group-hover:text-[#0077B6]\",\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 19\n              }, this), isOpen && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-3\",\n                children: item.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 30\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 17\n            }, this)\n          }, item.name, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-t border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/profile\",\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-10 w-10 rounded-full bg-[rgba(0,119,182,0.1)] flex items-center justify-center text-[#0077B6]\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-700\",\n              children: (user === null || user === void 0 ? void 0 : user.name) || 'Super Admin'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Super Admin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(SuperAdminSidebar, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\n_c = SuperAdminSidebar;\nexport default SuperAdminSidebar;\nvar _c;\n$RefreshReg$(_c, \"SuperAdminSidebar\");", "map": {"version": 3, "names": ["Link", "FaHome", "FaUniversity", "FaUsers", "FaChartBar", "FaList", "FaNewspaper", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SuperAdminSidebar", "isOpen", "setIsOpen", "_s", "user", "navItems", "name", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "children", "onClick", "to", "src", "alt", "map", "item", "window", "innerWidth", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/superadmin/SuperAdminSidebar.jsx"], "sourcesContent": ["import { Link } from 'react-router-dom';\nimport {\n  FaHome,\n  FaUniversity,\n  FaUsers,\n  FaChartBar,\n  FaList,\n  FaNewspaper,\n  FaTooth\n} from 'react-icons/fa';\nimport { useAuth } from '../context/AuthContext';\n\nconst SuperAdminSidebar = ({ isOpen, setIsOpen }) => {\n  const { user } = useAuth();\n\n  const navItems = [\n    { name: 'Dashboard', icon: <FaHome className=\"h-5 w-5\" />, path: '/superadmin/dashboard' },\n    { name: 'Universities', icon: <FaUniversity className=\"h-5 w-5\" />, path: '/superadmin/universities' },\n    { name: 'Accounts', icon: <FaUsers className=\"h-5 w-5\" />, path: '/superadmin/accounts' },\n    { name: 'Analytics', icon: <FaChartBar className=\"h-5 w-5\" />, path: '/superadmin/analytics' },\n    { name: 'Activity', icon: <FaList className=\"h-5 w-5\" />, path: '/superadmin/activity' },\n    { name: 'News', icon: <FaNewspaper className=\"h-5 w-5\" />, path: '/superadmin/news' },\n  ];\n\n  return (\n    <>\n      {isOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-10 md:hidden\"\n          onClick={() => setIsOpen(false)}\n        />\n      )}\n\n      <div\n        className={`fixed md:relative z-20 h-full transition-all duration-300 ease-in-out ${isOpen ? 'translate-x-0 w-64' : '-translate-x-full md:translate-x-0 w-20'} bg-white shadow-lg flex flex-col`}\n      >\n        <div className=\"p-4 flex items-center justify-center\">\n          <Link to=\"/\" className=\"flex items-center justify-center\">\n            <FaTooth className=\"w-8 h-8 text-[#0077B6] transition-transform duration-300 hover:scale-110\" />\n          </Link>\n          {isOpen && (\n          <Link to=\"/\">\n                    <img \n                      src=\"/imgs/odenta-logo2.jpg\" // Update this path based on your project structure\n                      alt=\"ODenta Logo\"\n                      className=\"h-10 w-auto\" // Adjust size as needed\n                    />\n            </Link>\n          )}\n        </div>\n\n        <nav className=\"flex-1 px-2 py-4 overflow-y-auto\">\n          <ul className=\"space-y-2\">\n            {navItems.map((item) => (\n              <li key={item.name}>\n                <Link\n                  to={item.path}\n                  className=\"flex items-center p-3 text-gray-600 hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.1)] rounded-lg transition-colors group\"\n                  onClick={() => window.innerWidth < 768 && setIsOpen(false)}\n                >\n                  <span className=\"text-gray-500 group-hover:text-[#0077B6]\">\n                    {item.icon}\n                  </span>\n                  {isOpen && <span className=\"ml-3\">{item.name}</span>}\n                </Link>\n              </li>\n            ))}\n          </ul>\n        </nav>\n\n        <div className=\"p-4 border-t border-gray-200\">\n          <Link to=\"/profile\" className=\"flex items-center\">\n            <div className=\"h-10 w-10 rounded-full bg-[rgba(0,119,182,0.1)] flex items-center justify-center text-[#0077B6]\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            {isOpen && (\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-700\">{user?.name || 'Super Admin'}</p>\n                <p className=\"text-xs text-gray-500\">Super Admin</p>\n              </div>\n            )}\n          </Link>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default SuperAdminSidebar;"], "mappings": ";;AAAA,SAASA,IAAI,QAAQ,kBAAkB;AACvC,SACEC,MAAM,EACNC,YAAY,EACZC,OAAO,EACPC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,OAAO,QACF,gBAAgB;AACvB,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACnD,MAAM;IAAEC;EAAK,CAAC,GAAGT,OAAO,CAAC,CAAC;EAE1B,MAAMU,QAAQ,GAAG,CACf;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,eAAEV,OAAA,CAACT,MAAM;MAACoB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAwB,CAAC,EAC1F;IAAEP,IAAI,EAAE,cAAc;IAAEC,IAAI,eAAEV,OAAA,CAACR,YAAY;MAACmB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAA2B,CAAC,EACtG;IAAEP,IAAI,EAAE,UAAU;IAAEC,IAAI,eAAEV,OAAA,CAACP,OAAO;MAACkB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAuB,CAAC,EACzF;IAAEP,IAAI,EAAE,WAAW;IAAEC,IAAI,eAAEV,OAAA,CAACN,UAAU;MAACiB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAwB,CAAC,EAC9F;IAAEP,IAAI,EAAE,UAAU;IAAEC,IAAI,eAAEV,OAAA,CAACL,MAAM;MAACgB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAuB,CAAC,EACxF;IAAEP,IAAI,EAAE,MAAM;IAAEC,IAAI,eAAEV,OAAA,CAACJ,WAAW;MAACe,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAmB,CAAC,CACtF;EAED,oBACEhB,OAAA,CAAAE,SAAA;IAAAe,QAAA,GACGb,MAAM,iBACLJ,OAAA;MACEW,SAAS,EAAC,qDAAqD;MAC/DO,OAAO,EAAEA,CAAA,KAAMb,SAAS,CAAC,KAAK;IAAE;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CACF,eAEDf,OAAA;MACEW,SAAS,EAAE,yEAAyEP,MAAM,GAAG,oBAAoB,GAAG,yCAAyC,mCAAoC;MAAAa,QAAA,gBAEjMjB,OAAA;QAAKW,SAAS,EAAC,sCAAsC;QAAAM,QAAA,gBACnDjB,OAAA,CAACV,IAAI;UAAC6B,EAAE,EAAC,GAAG;UAACR,SAAS,EAAC,kCAAkC;UAAAM,QAAA,eACvDjB,OAAA,CAACH,OAAO;YAACc,SAAS,EAAC;UAA0E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5F,CAAC,EACNX,MAAM,iBACPJ,OAAA,CAACV,IAAI;UAAC6B,EAAE,EAAC,GAAG;UAAAF,QAAA,eACFjB,OAAA;YACEoB,GAAG,EAAC,wBAAwB,CAAC;YAAA;YAC7BC,GAAG,EAAC,aAAa;YACjBV,SAAS,EAAC,aAAa,CAAC;UAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENf,OAAA;QAAKW,SAAS,EAAC,kCAAkC;QAAAM,QAAA,eAC/CjB,OAAA;UAAIW,SAAS,EAAC,WAAW;UAAAM,QAAA,EACtBT,QAAQ,CAACc,GAAG,CAAEC,IAAI,iBACjBvB,OAAA;YAAAiB,QAAA,eACEjB,OAAA,CAACV,IAAI;cACH6B,EAAE,EAAEI,IAAI,CAACP,IAAK;cACdL,SAAS,EAAC,4HAA4H;cACtIO,OAAO,EAAEA,CAAA,KAAMM,MAAM,CAACC,UAAU,GAAG,GAAG,IAAIpB,SAAS,CAAC,KAAK,CAAE;cAAAY,QAAA,gBAE3DjB,OAAA;gBAAMW,SAAS,EAAC,0CAA0C;gBAAAM,QAAA,EACvDM,IAAI,CAACb;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EACNX,MAAM,iBAAIJ,OAAA;gBAAMW,SAAS,EAAC,MAAM;gBAAAM,QAAA,EAAEM,IAAI,CAACd;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC,GAVAQ,IAAI,CAACd,IAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWd,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENf,OAAA;QAAKW,SAAS,EAAC,8BAA8B;QAAAM,QAAA,eAC3CjB,OAAA,CAACV,IAAI;UAAC6B,EAAE,EAAC,UAAU;UAACR,SAAS,EAAC,mBAAmB;UAAAM,QAAA,gBAC/CjB,OAAA;YAAKW,SAAS,EAAC,iGAAiG;YAAAM,QAAA,eAC9GjB,OAAA;cAAK0B,KAAK,EAAC,4BAA4B;cAACf,SAAS,EAAC,SAAS;cAACgB,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAZ,QAAA,eAC/GjB,OAAA;gBAAM8B,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAmI;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLX,MAAM,iBACLJ,OAAA;YAAKW,SAAS,EAAC,MAAM;YAAAM,QAAA,gBACnBjB,OAAA;cAAGW,SAAS,EAAC,mCAAmC;cAAAM,QAAA,EAAE,CAAAV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,KAAI;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClFf,OAAA;cAAGW,SAAS,EAAC,uBAAuB;cAAAM,QAAA,EAAC;YAAW;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACT,EAAA,CA5EIH,iBAAiB;EAAA,QACJL,OAAO;AAAA;AAAAoC,EAAA,GADpB/B,iBAAiB;AA8EvB,eAAeA,iBAAiB;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}