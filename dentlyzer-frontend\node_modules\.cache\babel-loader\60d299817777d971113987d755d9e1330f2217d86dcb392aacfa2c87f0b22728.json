{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\student\\\\History.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../context/AuthContext';\nimport Sidebar from './Sidebar';\nimport Navbar from './Navbar';\nimport PatientNav from './PatientNav';\nimport { printElementAsPDF, createPDFDownloadButton, generateSheetPDF } from '../utils/pdfUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst History = ({\n  selectedChart,\n  setSelectedChart,\n  charts\n}) => {\n  _s();\n  const {\n    nationalId\n  } = useParams();\n  const {\n    token\n  } = useAuth();\n  const [sheets, setSheets] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedSheet, setSelectedSheet] = useState(null);\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [showModal, setShowModal] = useState(false);\n\n  // Search and filter states\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedType, setSelectedType] = useState('All');\n  const [sortOrder, setSortOrder] = useState('newest'); // 'newest' or 'oldest'\n\n  // Sheet types for filter dropdown\n  const sheetTypes = ['All', 'Operative', 'Fixed Prosthodontics', 'Removable Prosthodontics', 'Endodontics', 'Periodontics'];\n  useEffect(() => {\n    const fetchSheetHistory = async () => {\n      try {\n        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/patients/${nationalId}/treatment-sheets`, {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        });\n        setSheets(response.data);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response3$data;\n        console.error('Error fetching sheet history:', (_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status, (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data);\n        setError(((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || 'Failed to load sheet history');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchSheetHistory();\n  }, [nationalId, token]);\n  const renderSheetDetails = sheet => {\n    const {\n      details\n    } = sheet;\n\n    // Function to render nested objects in a more organized way\n    const renderObject = (obj, level = 0) => {\n      if (!obj || typeof obj !== 'object') return null;\n\n      // Group data into sections for better organization\n      const sections = {};\n\n      // Process object entries and group them by section\n      Object.entries(obj).forEach(([key, value]) => {\n        // Skip empty objects\n        if (value === null || typeof value === 'object' && Object.keys(value).length === 0) {\n          return;\n        }\n\n        // Determine section based on key naming patterns\n        let section = 'General';\n        if (key.includes('examination') || key.includes('Examination')) {\n          section = 'Examination';\n        } else if (key.includes('treatment') || key.includes('Treatment') || key.includes('Plan')) {\n          section = 'Treatment Plan';\n        } else if (key.includes('diagnosis') || key.includes('Diagnosis') || key.includes('assessment')) {\n          section = 'Diagnosis & Assessment';\n        } else if (key.includes('history') || key.includes('History')) {\n          section = 'Patient History';\n        }\n\n        // Initialize section if it doesn't exist\n        if (!sections[section]) {\n          sections[section] = [];\n        }\n\n        // Add data to the appropriate section\n        if (typeof value === 'object' && !Array.isArray(value)) {\n          sections[section].push(/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-md font-medium text-[#0077B6] capitalize mb-2 border-b border-[#0077B6]/10 pb-1\",\n              children: key.replace(/([A-Z])/g, ' $1').trim()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pl-4 border-l-2 border-[#0077B6]/10\",\n              children: renderObject(value, level + 1)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)]\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this));\n        } else if (Array.isArray(value)) {\n          // Special handling for arrays\n          sections[section].push(/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-md font-medium text-[#0077B6] capitalize mb-2 border-b border-[#0077B6]/10 pb-1\",\n              children: key.replace(/([A-Z])/g, ' $1').trim()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pl-4 border-l-2 border-[#0077B6]/10\",\n              children: value.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: value.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 p-3 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                    children: [\"Item \", index + 1]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 25\n                  }, this), typeof item === 'object' ? renderObject(item, level + 1) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-800\",\n                    children: item || '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 110,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500\",\n                children: \"No items\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)]\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this));\n        } else {\n          sections[section].push(/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start py-1.5 group hover:bg-[#0077B6]/10 px-2 rounded-md transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-700 capitalize w-1/3 pr-2\",\n              children: [key.replace(/([A-Z])/g, ' $1').trim(), \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-800 w-2/3 font-normal group-hover:text-[#0077B6]\",\n              children: value || '-'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this)]\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this));\n        }\n      });\n\n      // Render each section\n      return Object.entries(sections).map(([sectionName, content]) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: [level === 0 && /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-[#0077B6] mb-3 pb-1 border-b border-gray-200\",\n          children: sectionName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-1\",\n          children: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, sectionName, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this));\n    };\n\n    // Main sheet details component\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-5 border-b border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-[#0077B6]/10 p-4 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-md font-semibold text-[#0077B6] mb-2\",\n              children: \"Diagnosis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-800 whitespace-pre-wrap\",\n              children: details.diagnosis || 'No diagnosis provided'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-[#20B2AA]/10 p-4 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-md font-semibold text-[#20B2AA] mb-2\",\n              children: \"Treatment Plan\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-800 whitespace-pre-wrap\",\n              children: details.treatmentPlan || 'No treatment plan provided'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), details.notes && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 bg-[#28A745]/10 p-4 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-md font-semibold text-[#28A745] mb-2\",\n            children: \"Notes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-800 whitespace-pre-wrap\",\n            children: details.notes\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-5\",\n        children: details.specificData && Object.keys(details.specificData).length > 0 ? renderObject(details.specificData) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-6 text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-12 w-12 mx-auto text-gray-300 mb-3\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 1,\n              d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No detailed data available for this sheet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n        isOpen: sidebarOpen,\n        setIsOpen: setSidebarOpen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex flex-col overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(Navbar, {\n          toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(PatientNav, {\n            selectedChart: selectedChart,\n            setSelectedChart: setSelectedChart,\n            charts: charts\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 overflow-y-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-pulse space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-8 bg-gray-200 rounded w-1/4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-4 bg-gray-200 rounded w-3/4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-4 bg-gray-200 rounded w-1/2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n        isOpen: sidebarOpen,\n        setIsOpen: setSidebarOpen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex flex-col overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(Navbar, {\n          toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(PatientNav, {\n            selectedChart: selectedChart,\n            setSelectedChart: setSelectedChart,\n            charts: charts\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 overflow-y-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 bg-red-100 text-red-700 rounded-lg\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this);\n  }\n  const handleViewSheet = sheet => {\n    setSelectedSheet(sheet);\n    setShowModal(true);\n  };\n\n  // Function to filter and sort sheets\n  const getFilteredSheets = () => {\n    const sheetsToFilter = sheets || [];\n    return [...sheetsToFilter].filter(sheet => {\n      var _sheet$details, _sheet$details2, _sheet$details3;\n      // Filter by search term (check in diagnosis, treatment plan, and type)\n      const searchLower = searchTerm.toLowerCase();\n      const matchesSearch = searchTerm === '' || sheet.type.toLowerCase().includes(searchLower) || (((_sheet$details = sheet.details) === null || _sheet$details === void 0 ? void 0 : _sheet$details.diagnosis) || '').toLowerCase().includes(searchLower) || (((_sheet$details2 = sheet.details) === null || _sheet$details2 === void 0 ? void 0 : _sheet$details2.treatmentPlan) || '').toLowerCase().includes(searchLower) || (((_sheet$details3 = sheet.details) === null || _sheet$details3 === void 0 ? void 0 : _sheet$details3.notes) || '').toLowerCase().includes(searchLower);\n\n      // Filter by sheet type\n      const matchesType = selectedType === 'All' || sheet.type === selectedType;\n      return matchesSearch && matchesType;\n    }).sort((a, b) => {\n      // Sort by date\n      const dateA = new Date(a.createdAt).getTime();\n      const dateB = new Date(b.createdAt).getTime();\n      return sortOrder === 'newest' ? dateB - dateA : dateA - dateB;\n    });\n  };\n  const handleCloseModal = () => {\n    setShowModal(false);\n    setSelectedSheet(null);\n  };\n\n  // Function to handle PDF download for individual sheet cards\n  const handleDownloadSheetPDF = async (sheet, event) => {\n    event.stopPropagation(); // Prevent opening the modal\n    try {\n      await generateSheetPDF(sheet, nationalId);\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      alert('Failed to generate PDF. Please try again.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-shrink-0\",\n        children: /*#__PURE__*/_jsxDEV(PatientNav, {\n          selectedChart: selectedChart,\n          setSelectedChart: setSelectedChart,\n          charts: charts\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.5\n        },\n        className: \"p-6 overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 flex justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-semibold text-gray-800\",\n            children: \"Sheet History\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this), sheets.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600 bg-gray-100 px-3 py-1 rounded-full\",\n            children: [getFilteredSheets().length, \" of \", sheets.length, \" sheets\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 flex flex-col md:flex-row gap-3 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative flex-grow\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-5 w-5 text-[#0077B6]\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                viewBox: \"0 0 20 20\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search by type, diagnosis, treatment plan...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              className: \"pl-10 pr-4 py-2.5 bg-white border-0 shadow-sm rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-[#0077B6]\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0 w-full md:w-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"type-filter\",\n              value: selectedType,\n              onChange: e => setSelectedType(e.target.value),\n              className: \"block w-full bg-white border-0 shadow-sm py-2.5 pl-3 pr-10 text-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0077B6]\",\n              \"aria-label\": \"Filter by sheet type\",\n              children: sheetTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: type,\n                children: type\n              }, type, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0 flex items-center bg-white rounded-lg shadow-sm overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSortOrder('newest'),\n              className: `px-4 py-2.5 text-sm font-medium transition-colors ${sortOrder === 'newest' ? 'bg-[#0077B6] text-white' : 'bg-white text-gray-700 hover:bg-gray-50'}`,\n              \"aria-label\": \"Sort by newest first\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  className: \"h-4 w-4 mr-1\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 19\n                }, this), \"Newest\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-6 w-px bg-gray-200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSortOrder('oldest'),\n              className: `px-4 py-2.5 text-sm font-medium transition-colors ${sortOrder === 'oldest' ? 'bg-[#0077B6] text-white' : 'bg-white text-gray-700 hover:bg-gray-50'}`,\n              \"aria-label\": \"Sort by oldest first\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  className: \"h-4 w-4 mr-1\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 19\n                }, this), \"Oldest\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this), sheets.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 bg-white rounded-lg shadow text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"No sheets have been saved for this patient yet.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 13\n        }, this) : getFilteredSheets().length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 bg-white rounded-lg shadow text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"No sheets match your search criteria. Try adjusting your filters.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setSearchTerm('');\n              setSelectedType('All');\n            },\n            className: \"mt-4 px-4 py-2 bg-[#0077B6] text-white rounded-lg hover:bg-[#0077B6]/80 transition-colors\",\n            children: \"Clear Filters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n          children: getFilteredSheets().map(sheet => {\n            var _sheet$details4;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                y: -5,\n                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'\n              },\n              className: \"bg-white rounded-xl shadow-md overflow-hidden cursor-pointer transition-all duration-300\",\n              onClick: () => handleViewSheet(sheet),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `h-2 ${sheet.type === 'Operative' ? 'bg-[#0077B6]' : sheet.type === 'Fixed Prosthodontics' ? 'bg-[#20B2AA]' : sheet.type === 'Removable Prosthodontics' ? 'bg-[#28A745]' : sheet.type === 'Endodontics' ? 'bg-[#0077B6]' : sheet.type === 'Periodontics' ? 'bg-[#20B2AA]' : sheet.type === 'Oral Surgery' ? 'bg-[#0077B6]' : 'bg-gray-500'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-5\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-800\",\n                    children: sheet.type\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"bg-[#0077B6]/10 text-[#0077B6] text-xs px-2 py-1 rounded-full\",\n                    children: new Date(sheet.createdAt).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 mb-3 truncate\",\n                  children: ((_sheet$details4 = sheet.details) === null || _sheet$details4 === void 0 ? void 0 : _sheet$details4.diagnosis) || 'No diagnosis provided'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs text-gray-500\",\n                    children: new Date(sheet.createdAt).toLocaleTimeString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: e => handleDownloadSheetPDF(sheet, e),\n                      className: \"p-1.5 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:shadow-md transition-all duration-200 flex items-center justify-center\",\n                      title: \"Download PDF\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        className: \"h-3.5 w-3.5\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: 2,\n                          d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 443,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 436,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 431,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-[#0077B6] font-medium\",\n                      children: \"Click to view details\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 19\n              }, this)]\n            }, sheet._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 7\n    }, this), showModal && selectedSheet && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        className: \"bg-white rounded-2xl shadow-2xl w-full max-w-5xl max-h-[90vh] overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sticky top-0 z-10 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white p-5 rounded-t-2xl flex justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `h-10 w-10 rounded-full flex items-center justify-center mr-3 ${selectedSheet.type === 'Operative' ? 'bg-[#0077B6]/80' : selectedSheet.type === 'Fixed Prosthodontics' ? 'bg-[#20B2AA]/80' : selectedSheet.type === 'Removable Prosthodontics' ? 'bg-[#28A745]/80' : selectedSheet.type === 'Endodontics' ? 'bg-[#0077B6]/80' : selectedSheet.type === 'Periodontics' ? 'bg-[#20B2AA]/80' : 'bg-gray-400'}`,\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-6 w-6 text-white\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl font-bold\",\n                children: [selectedSheet.type, \" Sheet\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex text-xs text-white/80 mt-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-3 w-3 mr-1\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 23\n                  }, this), \"Created: \", new Date(selectedSheet.createdAt).toLocaleDateString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 21\n                }, this), selectedSheet.updatedAt && selectedSheet.updatedAt !== selectedSheet.createdAt && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex items-center ml-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-3 w-3 mr-1\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 496,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 495,\n                    columnNumber: 25\n                  }, this), \"Updated: \", new Date(selectedSheet.updatedAt).toLocaleDateString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [createPDFDownloadButton('sheet-content', `${selectedSheet.type}_Sheet_${new Date(selectedSheet.createdAt).toLocaleDateString().replace(/\\//g, '-')}.pdf`), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCloseModal,\n              className: \"text-white hover:text-gray-200 bg-[#0077B6]/80 hover:bg-[#0077B6] rounded-full p-2 transition-colors ml-3\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-y-auto\",\n          style: {\n            maxHeight: 'calc(90vh - 140px)'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            id: \"sheet-content\",\n            children: renderSheetDetails(selectedSheet)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sticky bottom-0 bg-white p-4 border-t rounded-b-2xl flex justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: e => handleDownloadSheetPDF(selectedSheet, e),\n            className: \"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg hover:shadow-lg transition-all duration-300 font-semibold flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-5 w-5 mr-2\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 17\n            }, this), \"Download PDF\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleCloseModal,\n            className: \"px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors font-semibold flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-5 w-5 mr-2\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 17\n            }, this), \"Close\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 289,\n    columnNumber: 5\n  }, this);\n};\n_s(History, \"GNeYFYvGHm1bADl9uTB8u7KaBoE=\", false, function () {\n  return [useParams, useAuth];\n});\n_c = History;\nexport default History;\nvar _c;\n$RefreshReg$(_c, \"History\");", "map": {"version": 3, "names": ["useState", "useEffect", "useParams", "axios", "motion", "useAuth", "Sidebar", "<PERSON><PERSON><PERSON>", "PatientNav", "printElementAsPDF", "createPDFDownloadButton", "generateSheetPDF", "jsxDEV", "_jsxDEV", "History", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>hart", "charts", "_s", "nationalId", "token", "sheets", "setSheets", "loading", "setLoading", "error", "setError", "selectedSheet", "setSelectedSheet", "sidebarOpen", "setSidebarOpen", "showModal", "setShowModal", "searchTerm", "setSearchTerm", "selectedType", "setSelectedType", "sortOrder", "setSortOrder", "sheetTypes", "fetchSheetHistory", "response", "get", "process", "env", "REACT_APP_API_URL", "headers", "Authorization", "data", "err", "_err$response", "_err$response2", "_err$response3", "_err$response3$data", "console", "status", "message", "renderSheetDetails", "sheet", "details", "renderObject", "obj", "level", "sections", "Object", "entries", "for<PERSON>ach", "key", "value", "keys", "length", "section", "includes", "Array", "isArray", "push", "className", "children", "replace", "trim", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "index", "sectionName", "content", "diagnosis", "treatmentPlan", "notes", "specificData", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "isOpen", "setIsOpen", "toggleSidebar", "handleViewSheet", "getFilteredSheets", "sheetsToFilter", "filter", "_sheet$details", "_sheet$details2", "_sheet$details3", "searchLower", "toLowerCase", "matchesSearch", "type", "matchesType", "sort", "a", "b", "dateA", "Date", "createdAt", "getTime", "dateB", "handleCloseModal", "handleDownloadSheetPDF", "event", "stopPropagation", "alert", "div", "initial", "opacity", "y", "animate", "transition", "duration", "fillRule", "clipRule", "placeholder", "onChange", "e", "target", "id", "onClick", "_sheet$details4", "whileHover", "boxShadow", "toLocaleDateString", "toLocaleTimeString", "title", "_id", "scale", "updatedAt", "style", "maxHeight", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/student/History.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport { useParams } from 'react-router-dom';\r\nimport axios from 'axios';\r\nimport { motion } from 'framer-motion';\r\nimport { useAuth } from '../context/AuthContext';\r\nimport Sidebar from './Sidebar';\r\nimport Navbar from './Navbar';\r\nimport PatientNav from './PatientNav';\r\nimport { printElementAsPDF, createPDFDownloadButton, generateSheetPDF } from '../utils/pdfUtils';\r\n\r\nconst History = ({ selectedChart, setSelectedChart, charts }) => {\r\n  const { nationalId } = useParams();\r\n  const { token } = useAuth();\r\n  const [sheets, setSheets] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [selectedSheet, setSelectedSheet] = useState(null);\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const [showModal, setShowModal] = useState(false);\r\n\r\n  // Search and filter states\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedType, setSelectedType] = useState('All');\r\n  const [sortOrder, setSortOrder] = useState('newest'); // 'newest' or 'oldest'\r\n\r\n  // Sheet types for filter dropdown\r\n  const sheetTypes = ['All', 'Operative', 'Fixed Prosthodontics', 'Removable Prosthodontics', 'Endodontics', 'Periodontics'];\r\n\r\n  useEffect(() => {\r\n    const fetchSheetHistory = async () => {\r\n      try {\r\n        const response = await axios.get(\r\n          `${process.env.REACT_APP_API_URL}/api/patients/${nationalId}/treatment-sheets`,\r\n          { headers: { Authorization: `Bearer ${token}` } }\r\n        );\r\n        setSheets(response.data);\r\n      } catch (err) {\r\n        console.error('Error fetching sheet history:', err.response?.status, err.response?.data);\r\n        setError(err.response?.data?.message || 'Failed to load sheet history');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchSheetHistory();\r\n  }, [nationalId, token]);\r\n\r\n  const renderSheetDetails = (sheet) => {\r\n    const { details } = sheet;\r\n\r\n    // Function to render nested objects in a more organized way\r\n    const renderObject = (obj, level = 0) => {\r\n      if (!obj || typeof obj !== 'object') return null;\r\n\r\n      // Group data into sections for better organization\r\n      const sections = {};\r\n\r\n      // Process object entries and group them by section\r\n      Object.entries(obj).forEach(([key, value]) => {\r\n        // Skip empty objects\r\n        if (value === null || (typeof value === 'object' && Object.keys(value).length === 0)) {\r\n          return;\r\n        }\r\n\r\n        // Determine section based on key naming patterns\r\n        let section = 'General';\r\n        if (key.includes('examination') || key.includes('Examination')) {\r\n          section = 'Examination';\r\n        } else if (key.includes('treatment') || key.includes('Treatment') || key.includes('Plan')) {\r\n          section = 'Treatment Plan';\r\n        } else if (key.includes('diagnosis') || key.includes('Diagnosis') || key.includes('assessment')) {\r\n          section = 'Diagnosis & Assessment';\r\n        } else if (key.includes('history') || key.includes('History')) {\r\n          section = 'Patient History';\r\n        }\r\n\r\n        // Initialize section if it doesn't exist\r\n        if (!sections[section]) {\r\n          sections[section] = [];\r\n        }\r\n\r\n        // Add data to the appropriate section\r\n        if (typeof value === 'object' && !Array.isArray(value)) {\r\n          sections[section].push(\r\n            <div key={key} className=\"mb-4\">\r\n              <h4 className=\"text-md font-medium text-[#0077B6] capitalize mb-2 border-b border-[#0077B6]/10 pb-1\">\r\n                {key.replace(/([A-Z])/g, ' $1').trim()}\r\n              </h4>\r\n              <div className=\"pl-4 border-l-2 border-[#0077B6]/10\">\r\n                {renderObject(value, level + 1)}\r\n              </div>\r\n            </div>\r\n          );\r\n        } else if (Array.isArray(value)) {\r\n          // Special handling for arrays\r\n          sections[section].push(\r\n            <div key={key} className=\"mb-4\">\r\n              <h4 className=\"text-md font-medium text-[#0077B6] capitalize mb-2 border-b border-[#0077B6]/10 pb-1\">\r\n                {key.replace(/([A-Z])/g, ' $1').trim()}\r\n              </h4>\r\n              <div className=\"pl-4 border-l-2 border-[#0077B6]/10\">\r\n                {value.length > 0 ? (\r\n                  <div className=\"space-y-3\">\r\n                    {value.map((item, index) => (\r\n                      <div key={index} className=\"bg-gray-50 p-3 rounded-lg\">\r\n                        <h5 className=\"text-sm font-medium text-gray-700 mb-2\">Item {index + 1}</h5>\r\n                        {typeof item === 'object' ? (\r\n                          renderObject(item, level + 1)\r\n                        ) : (\r\n                          <span className=\"text-sm text-gray-800\">{item || '-'}</span>\r\n                        )}\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                ) : (\r\n                  <span className=\"text-sm text-gray-500\">No items</span>\r\n                )}\r\n              </div>\r\n            </div>\r\n          );\r\n        } else {\r\n          sections[section].push(\r\n            <div key={key} className=\"flex items-start py-1.5 group hover:bg-[#0077B6]/10 px-2 rounded-md transition-colors\">\r\n              <span className=\"text-sm font-medium text-gray-700 capitalize w-1/3 pr-2\">\r\n                {key.replace(/([A-Z])/g, ' $1').trim()}:\r\n              </span>\r\n              <span className=\"text-sm text-gray-800 w-2/3 font-normal group-hover:text-[#0077B6]\">\r\n                {value || '-'}\r\n              </span>\r\n            </div>\r\n          );\r\n        }\r\n      });\r\n\r\n      // Render each section\r\n      return Object.entries(sections).map(([sectionName, content]) => (\r\n        <div key={sectionName} className=\"mb-6\">\r\n          {level === 0 && (\r\n            <h3 className=\"text-lg font-semibold text-[#0077B6] mb-3 pb-1 border-b border-gray-200\">\r\n              {sectionName}\r\n            </h3>\r\n          )}\r\n          <div className=\"space-y-1\">\r\n            {content}\r\n          </div>\r\n        </div>\r\n      ));\r\n    };\r\n\r\n    // Main sheet details component\r\n    return (\r\n      <div className=\"bg-white rounded-lg shadow-sm\">\r\n        {/* Basic sheet information */}\r\n        <div className=\"p-5 border-b border-gray-100\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n            <div className=\"bg-[#0077B6]/10 p-4 rounded-lg\">\r\n              <h3 className=\"text-md font-semibold text-[#0077B6] mb-2\">Diagnosis</h3>\r\n              <p className=\"text-sm text-gray-800 whitespace-pre-wrap\">\r\n                {details.diagnosis || 'No diagnosis provided'}\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"bg-[#20B2AA]/10 p-4 rounded-lg\">\r\n              <h3 className=\"text-md font-semibold text-[#20B2AA] mb-2\">Treatment Plan</h3>\r\n              <p className=\"text-sm text-gray-800 whitespace-pre-wrap\">\r\n                {details.treatmentPlan || 'No treatment plan provided'}\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          {details.notes && (\r\n            <div className=\"mt-4 bg-[#28A745]/10 p-4 rounded-lg\">\r\n              <h3 className=\"text-md font-semibold text-[#28A745] mb-2\">Notes</h3>\r\n              <p className=\"text-sm text-gray-800 whitespace-pre-wrap\">\r\n                {details.notes}\r\n              </p>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Detailed sheet data */}\r\n        <div className=\"p-5\">\r\n          {details.specificData && Object.keys(details.specificData).length > 0 ? (\r\n            renderObject(details.specificData)\r\n          ) : (\r\n            <div className=\"text-center py-6 text-gray-500\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-12 w-12 mx-auto text-gray-300 mb-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n              </svg>\r\n              <p>No detailed data available for this sheet</p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex h-screen bg-gray-50\">\r\n        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n        <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\r\n          <div className=\"flex-shrink-0\">\r\n            <PatientNav selectedChart={selectedChart} setSelectedChart={setSelectedChart} charts={charts} />\r\n          </div>\r\n          <div className=\"p-6 overflow-y-auto\">\r\n            <div className=\"animate-pulse space-y-4\">\r\n              <div className=\"h-8 bg-gray-200 rounded w-1/4\"></div>\r\n              <div className=\"space-y-2\">\r\n                <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\r\n                <div className=\"h-4 bg-gray-200 rounded w-1/2\"></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"flex h-screen bg-gray-50\">\r\n        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n        <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\r\n          <div className=\"flex-shrink-0\">\r\n            <PatientNav selectedChart={selectedChart} setSelectedChart={setSelectedChart} charts={charts} />\r\n          </div>\r\n          <div className=\"p-6 overflow-y-auto\">\r\n            <div className=\"p-4 bg-red-100 text-red-700 rounded-lg\">{error}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const handleViewSheet = (sheet) => {\r\n    setSelectedSheet(sheet);\r\n    setShowModal(true);\r\n  };\r\n\r\n  // Function to filter and sort sheets\r\n  const getFilteredSheets = () => {\r\n    const sheetsToFilter = sheets || [];\r\n\r\n    return [...sheetsToFilter]\r\n      .filter(sheet => {\r\n        // Filter by search term (check in diagnosis, treatment plan, and type)\r\n        const searchLower = searchTerm.toLowerCase();\r\n        const matchesSearch =\r\n          searchTerm === '' ||\r\n          sheet.type.toLowerCase().includes(searchLower) ||\r\n          (sheet.details?.diagnosis || '').toLowerCase().includes(searchLower) ||\r\n          (sheet.details?.treatmentPlan || '').toLowerCase().includes(searchLower) ||\r\n          (sheet.details?.notes || '').toLowerCase().includes(searchLower);\r\n\r\n        // Filter by sheet type\r\n        const matchesType = selectedType === 'All' || sheet.type === selectedType;\r\n\r\n        return matchesSearch && matchesType;\r\n      })\r\n      .sort((a, b) => {\r\n        // Sort by date\r\n        const dateA = new Date(a.createdAt).getTime();\r\n        const dateB = new Date(b.createdAt).getTime();\r\n\r\n        return sortOrder === 'newest' ? dateB - dateA : dateA - dateB;\r\n      });\r\n  };\r\n\r\n  const handleCloseModal = () => {\r\n    setShowModal(false);\r\n    setSelectedSheet(null);\r\n  };\r\n\r\n  // Function to handle PDF download for individual sheet cards\r\n  const handleDownloadSheetPDF = async (sheet, event) => {\r\n    event.stopPropagation(); // Prevent opening the modal\r\n    try {\r\n      await generateSheetPDF(sheet, nationalId);\r\n    } catch (error) {\r\n      console.error('Error generating PDF:', error);\r\n      alert('Failed to generate PDF. Please try again.');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex h-screen bg-gray-50\">\r\n      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\r\n        <div className=\"flex-shrink-0\">\r\n          <PatientNav selectedChart={selectedChart} setSelectedChart={setSelectedChart} charts={charts} />\r\n        </div>\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.5 }}\r\n          className=\"p-6 overflow-y-auto\"\r\n        >\r\n          <div className=\"mb-6 flex justify-between items-center\">\r\n            <h2 className=\"text-2xl font-semibold text-gray-800\">Sheet History</h2>\r\n            {sheets.length > 0 && (\r\n              <div className=\"text-sm text-gray-600 bg-gray-100 px-3 py-1 rounded-full\">\r\n                {getFilteredSheets().length} of {sheets.length} sheets\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Search and Filter Controls - Redesigned */}\r\n          <div className=\"mb-6 flex flex-col md:flex-row gap-3 items-center\">\r\n            {/* Search Bar */}\r\n            <div className=\"relative flex-grow\">\r\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                <svg className=\"h-5 w-5 text-[#0077B6]\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                  <path fillRule=\"evenodd\" d=\"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\" clipRule=\"evenodd\" />\r\n                </svg>\r\n              </div>\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Search by type, diagnosis, treatment plan...\"\r\n                value={searchTerm}\r\n                onChange={(e) => setSearchTerm(e.target.value)}\r\n                className=\"pl-10 pr-4 py-2.5 bg-white border-0 shadow-sm rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-[#0077B6]\"\r\n              />\r\n            </div>\r\n\r\n            {/* Type Filter */}\r\n            <div className=\"flex-shrink-0 w-full md:w-auto\">\r\n              <select\r\n                id=\"type-filter\"\r\n                value={selectedType}\r\n                onChange={(e) => setSelectedType(e.target.value)}\r\n                className=\"block w-full bg-white border-0 shadow-sm py-2.5 pl-3 pr-10 text-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0077B6]\"\r\n                aria-label=\"Filter by sheet type\"\r\n              >\r\n                {sheetTypes.map((type) => (\r\n                  <option key={type} value={type}>{type}</option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n\r\n            {/* Sort Order */}\r\n            <div className=\"flex-shrink-0 flex items-center bg-white rounded-lg shadow-sm overflow-hidden\">\r\n              <button\r\n                onClick={() => setSortOrder('newest')}\r\n                className={`px-4 py-2.5 text-sm font-medium transition-colors ${\r\n                  sortOrder === 'newest'\r\n                    ? 'bg-[#0077B6] text-white'\r\n                    : 'bg-white text-gray-700 hover:bg-gray-50'\r\n                }`}\r\n                aria-label=\"Sort by newest first\"\r\n              >\r\n                <div className=\"flex items-center\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12\" />\r\n                  </svg>\r\n                  Newest\r\n                </div>\r\n              </button>\r\n              <div className=\"h-6 w-px bg-gray-200\"></div>\r\n              <button\r\n                onClick={() => setSortOrder('oldest')}\r\n                className={`px-4 py-2.5 text-sm font-medium transition-colors ${\r\n                  sortOrder === 'oldest'\r\n                    ? 'bg-[#0077B6] text-white'\r\n                    : 'bg-white text-gray-700 hover:bg-gray-50'\r\n                }`}\r\n                aria-label=\"Sort by oldest first\"\r\n              >\r\n                <div className=\"flex items-center\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4\" />\r\n                  </svg>\r\n                  Oldest\r\n                </div>\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {sheets.length === 0 ? (\r\n            <div className=\"p-6 bg-white rounded-lg shadow text-center\">\r\n              <p className=\"text-gray-600\">No sheets have been saved for this patient yet.</p>\r\n            </div>\r\n          ) : getFilteredSheets().length === 0 ? (\r\n            <div className=\"p-6 bg-white rounded-lg shadow text-center\">\r\n              <p className=\"text-gray-600\">No sheets match your search criteria. Try adjusting your filters.</p>\r\n              <button\r\n                onClick={() => {\r\n                  setSearchTerm('');\r\n                  setSelectedType('All');\r\n                }}\r\n                className=\"mt-4 px-4 py-2 bg-[#0077B6] text-white rounded-lg hover:bg-[#0077B6]/80 transition-colors\"\r\n              >\r\n                Clear Filters\r\n              </button>\r\n            </div>\r\n          ) : (\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n              {getFilteredSheets().map((sheet) => (\r\n                <motion.div\r\n                  key={sheet._id}\r\n                  whileHover={{ y: -5, boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)' }}\r\n                  className=\"bg-white rounded-xl shadow-md overflow-hidden cursor-pointer transition-all duration-300\"\r\n                  onClick={() => handleViewSheet(sheet)}\r\n                >\r\n                  <div className={`h-2 ${\r\n                    sheet.type === 'Operative' ? 'bg-[#0077B6]' :\r\n                    sheet.type === 'Fixed Prosthodontics' ? 'bg-[#20B2AA]' :\r\n                    sheet.type === 'Removable Prosthodontics' ? 'bg-[#28A745]' :\r\n                    sheet.type === 'Endodontics' ? 'bg-[#0077B6]' :\r\n                    sheet.type === 'Periodontics' ? 'bg-[#20B2AA]' :\r\n                    sheet.type === 'Oral Surgery' ? 'bg-[#0077B6]' : 'bg-gray-500'\r\n                  }`}></div>\r\n                  <div className=\"p-5\">\r\n                    <div className=\"flex justify-between items-center mb-3\">\r\n                      <h3 className=\"text-lg font-semibold text-gray-800\">{sheet.type}</h3>\r\n                      <span className=\"bg-[#0077B6]/10 text-[#0077B6] text-xs px-2 py-1 rounded-full\">\r\n                        {new Date(sheet.createdAt).toLocaleDateString()}\r\n                      </span>\r\n                    </div>\r\n                    <p className=\"text-sm text-gray-600 mb-3 truncate\">\r\n                      {sheet.details?.diagnosis || 'No diagnosis provided'}\r\n                    </p>\r\n                    <div className=\"flex justify-between items-center\">\r\n                      <span className=\"text-xs text-gray-500\">\r\n                        {new Date(sheet.createdAt).toLocaleTimeString()}\r\n                      </span>\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <button\r\n                          onClick={(e) => handleDownloadSheetPDF(sheet, e)}\r\n                          className=\"p-1.5 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:shadow-md transition-all duration-200 flex items-center justify-center\"\r\n                          title=\"Download PDF\"\r\n                        >\r\n                          <svg\r\n                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                            className=\"h-3.5 w-3.5\"\r\n                            fill=\"none\"\r\n                            viewBox=\"0 0 24 24\"\r\n                            stroke=\"currentColor\"\r\n                          >\r\n                            <path\r\n                              strokeLinecap=\"round\"\r\n                              strokeLinejoin=\"round\"\r\n                              strokeWidth={2}\r\n                              d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\r\n                            />\r\n                          </svg>\r\n                        </button>\r\n                        <span className=\"text-xs text-[#0077B6] font-medium\">Click to view details</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </motion.div>\r\n      </div>\r\n\r\n      {/* Sheet Details Modal */}\r\n      {showModal && selectedSheet && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4\">\r\n          <motion.div\r\n            initial={{ scale: 0.9, opacity: 0 }}\r\n            animate={{ scale: 1, opacity: 1 }}\r\n            className=\"bg-white rounded-2xl shadow-2xl w-full max-w-5xl max-h-[90vh] overflow-hidden\"\r\n          >\r\n            {/* Modal Header */}\r\n            <div className=\"sticky top-0 z-10 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white p-5 rounded-t-2xl flex justify-between items-center\">\r\n              <div className=\"flex items-center\">\r\n                <div className={`h-10 w-10 rounded-full flex items-center justify-center mr-3 ${\r\n                  selectedSheet.type === 'Operative' ? 'bg-[#0077B6]/80' :\r\n                  selectedSheet.type === 'Fixed Prosthodontics' ? 'bg-[#20B2AA]/80' :\r\n                  selectedSheet.type === 'Removable Prosthodontics' ? 'bg-[#28A745]/80' :\r\n                  selectedSheet.type === 'Endodontics' ? 'bg-[#0077B6]/80' :\r\n                  selectedSheet.type === 'Periodontics' ? 'bg-[#20B2AA]/80' : 'bg-gray-400'\r\n                }`}>\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n                  </svg>\r\n                </div>\r\n                <div>\r\n                  <h2 className=\"text-2xl font-bold\">{selectedSheet.type} Sheet</h2>\r\n                  <div className=\"flex text-xs text-white/80 mt-1\">\r\n                    <span className=\"flex items-center\">\r\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-3 w-3 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\r\n                      </svg>\r\n                      Created: {new Date(selectedSheet.createdAt).toLocaleDateString()}\r\n                    </span>\r\n                    {selectedSheet.updatedAt && selectedSheet.updatedAt !== selectedSheet.createdAt && (\r\n                      <span className=\"flex items-center ml-3\">\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-3 w-3 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                        </svg>\r\n                        Updated: {new Date(selectedSheet.updatedAt).toLocaleDateString()}\r\n                      </span>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex items-center\">\r\n                {createPDFDownloadButton('sheet-content', `${selectedSheet.type}_Sheet_${new Date(selectedSheet.createdAt).toLocaleDateString().replace(/\\//g, '-')}.pdf`)}\r\n                <button\r\n                  onClick={handleCloseModal}\r\n                  className=\"text-white hover:text-gray-200 bg-[#0077B6]/80 hover:bg-[#0077B6] rounded-full p-2 transition-colors ml-3\"\r\n                >\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Modal Content - Scrollable Area */}\r\n            <div className=\"overflow-y-auto\" style={{ maxHeight: 'calc(90vh - 140px)' }}>\r\n              {/* Sheet Details */}\r\n              <div className=\"p-6\" id=\"sheet-content\">\r\n                {renderSheetDetails(selectedSheet)}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Modal Footer */}\r\n            <div className=\"sticky bottom-0 bg-white p-4 border-t rounded-b-2xl flex justify-between items-center\">\r\n              <button\r\n                onClick={(e) => handleDownloadSheetPDF(selectedSheet, e)}\r\n                className=\"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg hover:shadow-lg transition-all duration-300 font-semibold flex items-center\"\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  className=\"h-5 w-5 mr-2\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  stroke=\"currentColor\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    strokeWidth={2}\r\n                    d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\r\n                  />\r\n                </svg>\r\n                Download PDF\r\n              </button>\r\n              <button\r\n                onClick={handleCloseModal}\r\n                className=\"px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors font-semibold flex items-center\"\r\n              >\r\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                </svg>\r\n                Close\r\n              </button>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default History;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,iBAAiB,EAAEC,uBAAuB,EAAEC,gBAAgB,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjG,MAAMC,OAAO,GAAGA,CAAC;EAAEC,aAAa;EAAEC,gBAAgB;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC/D,MAAM;IAAEC;EAAW,CAAC,GAAGjB,SAAS,CAAC,CAAC;EAClC,MAAM;IAAEkB;EAAM,CAAC,GAAGf,OAAO,CAAC,CAAC;EAC3B,MAAM,CAACgB,MAAM,EAAEC,SAAS,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;;EAEtD;EACA,MAAMuC,UAAU,GAAG,CAAC,KAAK,EAAE,WAAW,EAAE,sBAAsB,EAAE,0BAA0B,EAAE,aAAa,EAAE,cAAc,CAAC;EAE1HtC,SAAS,CAAC,MAAM;IACd,MAAMuC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMtC,KAAK,CAACuC,GAAG,CAC9B,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,iBAAiB1B,UAAU,mBAAmB,EAC9E;UAAE2B,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAU3B,KAAK;UAAG;QAAE,CAClD,CAAC;QACDE,SAAS,CAACmB,QAAQ,CAACO,IAAI,CAAC;MAC1B,CAAC,CAAC,OAAOC,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZC,OAAO,CAAC7B,KAAK,CAAC,+BAA+B,GAAAyB,aAAA,GAAED,GAAG,CAACR,QAAQ,cAAAS,aAAA,uBAAZA,aAAA,CAAcK,MAAM,GAAAJ,cAAA,GAAEF,GAAG,CAACR,QAAQ,cAAAU,cAAA,uBAAZA,cAAA,CAAcH,IAAI,CAAC;QACxFtB,QAAQ,CAAC,EAAA0B,cAAA,GAAAH,GAAG,CAACR,QAAQ,cAAAW,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcJ,IAAI,cAAAK,mBAAA,uBAAlBA,mBAAA,CAAoBG,OAAO,KAAI,8BAA8B,CAAC;MACzE,CAAC,SAAS;QACRhC,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDgB,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACrB,UAAU,EAAEC,KAAK,CAAC,CAAC;EAEvB,MAAMqC,kBAAkB,GAAIC,KAAK,IAAK;IACpC,MAAM;MAAEC;IAAQ,CAAC,GAAGD,KAAK;;IAEzB;IACA,MAAME,YAAY,GAAGA,CAACC,GAAG,EAAEC,KAAK,GAAG,CAAC,KAAK;MACvC,IAAI,CAACD,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,OAAO,IAAI;;MAEhD;MACA,MAAME,QAAQ,GAAG,CAAC,CAAC;;MAEnB;MACAC,MAAM,CAACC,OAAO,CAACJ,GAAG,CAAC,CAACK,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;QAC5C;QACA,IAAIA,KAAK,KAAK,IAAI,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIJ,MAAM,CAACK,IAAI,CAACD,KAAK,CAAC,CAACE,MAAM,KAAK,CAAE,EAAE;UACpF;QACF;;QAEA;QACA,IAAIC,OAAO,GAAG,SAAS;QACvB,IAAIJ,GAAG,CAACK,QAAQ,CAAC,aAAa,CAAC,IAAIL,GAAG,CAACK,QAAQ,CAAC,aAAa,CAAC,EAAE;UAC9DD,OAAO,GAAG,aAAa;QACzB,CAAC,MAAM,IAAIJ,GAAG,CAACK,QAAQ,CAAC,WAAW,CAAC,IAAIL,GAAG,CAACK,QAAQ,CAAC,WAAW,CAAC,IAAIL,GAAG,CAACK,QAAQ,CAAC,MAAM,CAAC,EAAE;UACzFD,OAAO,GAAG,gBAAgB;QAC5B,CAAC,MAAM,IAAIJ,GAAG,CAACK,QAAQ,CAAC,WAAW,CAAC,IAAIL,GAAG,CAACK,QAAQ,CAAC,WAAW,CAAC,IAAIL,GAAG,CAACK,QAAQ,CAAC,YAAY,CAAC,EAAE;UAC/FD,OAAO,GAAG,wBAAwB;QACpC,CAAC,MAAM,IAAIJ,GAAG,CAACK,QAAQ,CAAC,SAAS,CAAC,IAAIL,GAAG,CAACK,QAAQ,CAAC,SAAS,CAAC,EAAE;UAC7DD,OAAO,GAAG,iBAAiB;QAC7B;;QAEA;QACA,IAAI,CAACR,QAAQ,CAACQ,OAAO,CAAC,EAAE;UACtBR,QAAQ,CAACQ,OAAO,CAAC,GAAG,EAAE;QACxB;;QAEA;QACA,IAAI,OAAOH,KAAK,KAAK,QAAQ,IAAI,CAACK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,EAAE;UACtDL,QAAQ,CAACQ,OAAO,CAAC,CAACI,IAAI,cACpB9D,OAAA;YAAe+D,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC7BhE,OAAA;cAAI+D,SAAS,EAAC,sFAAsF;cAAAC,QAAA,EACjGV,GAAG,CAACW,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACC,IAAI,CAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACLtE,OAAA;cAAK+D,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EACjDjB,YAAY,CAACQ,KAAK,EAAEN,KAAK,GAAG,CAAC;YAAC;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA,GANEhB,GAAG;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOR,CACP,CAAC;QACH,CAAC,MAAM,IAAIV,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,EAAE;UAC/B;UACAL,QAAQ,CAACQ,OAAO,CAAC,CAACI,IAAI,cACpB9D,OAAA;YAAe+D,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC7BhE,OAAA;cAAI+D,SAAS,EAAC,sFAAsF;cAAAC,QAAA,EACjGV,GAAG,CAACW,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACC,IAAI,CAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACLtE,OAAA;cAAK+D,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EACjDT,KAAK,CAACE,MAAM,GAAG,CAAC,gBACfzD,OAAA;gBAAK+D,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvBT,KAAK,CAACgB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBzE,OAAA;kBAAiB+D,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBACpDhE,OAAA;oBAAI+D,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,GAAC,OAAK,EAACS,KAAK,GAAG,CAAC;kBAAA;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,EAC3E,OAAOE,IAAI,KAAK,QAAQ,GACvBzB,YAAY,CAACyB,IAAI,EAAEvB,KAAK,GAAG,CAAC,CAAC,gBAE7BjD,OAAA;oBAAM+D,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEQ,IAAI,IAAI;kBAAG;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAC5D;gBAAA,GANOG,KAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAENtE,OAAA;gBAAM+D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YACvD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GArBEhB,GAAG;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsBR,CACP,CAAC;QACH,CAAC,MAAM;UACLpB,QAAQ,CAACQ,OAAO,CAAC,CAACI,IAAI,cACpB9D,OAAA;YAAe+D,SAAS,EAAC,uFAAuF;YAAAC,QAAA,gBAC9GhE,OAAA;cAAM+D,SAAS,EAAC,yDAAyD;cAAAC,QAAA,GACtEV,GAAG,CAACW,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACC,IAAI,CAAC,CAAC,EAAC,GACzC;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPtE,OAAA;cAAM+D,SAAS,EAAC,oEAAoE;cAAAC,QAAA,EACjFT,KAAK,IAAI;YAAG;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA,GANChB,GAAG;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOR,CACP,CAAC;QACH;MACF,CAAC,CAAC;;MAEF;MACA,OAAOnB,MAAM,CAACC,OAAO,CAACF,QAAQ,CAAC,CAACqB,GAAG,CAAC,CAAC,CAACG,WAAW,EAAEC,OAAO,CAAC,kBACzD3E,OAAA;QAAuB+D,SAAS,EAAC,MAAM;QAAAC,QAAA,GACpCf,KAAK,KAAK,CAAC,iBACVjD,OAAA;UAAI+D,SAAS,EAAC,yEAAyE;UAAAC,QAAA,EACpFU;QAAW;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACL,eACDtE,OAAA;UAAK+D,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBW;QAAO;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA,GAREI,WAAW;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAShB,CACN,CAAC;IACJ,CAAC;;IAED;IACA,oBACEtE,OAAA;MAAK+D,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAE5ChE,OAAA;QAAK+D,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3ChE,OAAA;UAAK+D,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDhE,OAAA;YAAK+D,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7ChE,OAAA;cAAI+D,SAAS,EAAC,2CAA2C;cAAAC,QAAA,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxEtE,OAAA;cAAG+D,SAAS,EAAC,2CAA2C;cAAAC,QAAA,EACrDlB,OAAO,CAAC8B,SAAS,IAAI;YAAuB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENtE,OAAA;YAAK+D,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7ChE,OAAA;cAAI+D,SAAS,EAAC,2CAA2C;cAAAC,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7EtE,OAAA;cAAG+D,SAAS,EAAC,2CAA2C;cAAAC,QAAA,EACrDlB,OAAO,CAAC+B,aAAa,IAAI;YAA4B;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELxB,OAAO,CAACgC,KAAK,iBACZ9E,OAAA;UAAK+D,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBAClDhE,OAAA;YAAI+D,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpEtE,OAAA;YAAG+D,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EACrDlB,OAAO,CAACgC;UAAK;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNtE,OAAA;QAAK+D,SAAS,EAAC,KAAK;QAAAC,QAAA,EACjBlB,OAAO,CAACiC,YAAY,IAAI5B,MAAM,CAACK,IAAI,CAACV,OAAO,CAACiC,YAAY,CAAC,CAACtB,MAAM,GAAG,CAAC,GACnEV,YAAY,CAACD,OAAO,CAACiC,YAAY,CAAC,gBAElC/E,OAAA;UAAK+D,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7ChE,OAAA;YAAKgF,KAAK,EAAC,4BAA4B;YAACjB,SAAS,EAAC,sCAAsC;YAACkB,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAAAnB,QAAA,eAC5IhE,OAAA;cAAMoF,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAsH;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3L,CAAC,eACNtE,OAAA;YAAAgE,QAAA,EAAG;UAAyC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,IAAI5D,OAAO,EAAE;IACX,oBACEV,OAAA;MAAK+D,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvChE,OAAA,CAACP,OAAO;QAAC+F,MAAM,EAAExE,WAAY;QAACyE,SAAS,EAAExE;MAAe;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DtE,OAAA;QAAK+D,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnDhE,OAAA,CAACN,MAAM;UAACgG,aAAa,EAAEA,CAAA,KAAMzE,cAAc,CAAC,CAACD,WAAW;QAAE;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DtE,OAAA;UAAK+D,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BhE,OAAA,CAACL,UAAU;YAACO,aAAa,EAAEA,aAAc;YAACC,gBAAgB,EAAEA,gBAAiB;YAACC,MAAM,EAAEA;UAAO;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7F,CAAC,eACNtE,OAAA;UAAK+D,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClChE,OAAA;YAAK+D,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtChE,OAAA;cAAK+D,SAAS,EAAC;YAA+B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrDtE,OAAA;cAAK+D,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBhE,OAAA;gBAAK+D,SAAS,EAAC;cAA+B;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDtE,OAAA;gBAAK+D,SAAS,EAAC;cAA+B;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI1D,KAAK,EAAE;IACT,oBACEZ,OAAA;MAAK+D,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvChE,OAAA,CAACP,OAAO;QAAC+F,MAAM,EAAExE,WAAY;QAACyE,SAAS,EAAExE;MAAe;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DtE,OAAA;QAAK+D,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnDhE,OAAA,CAACN,MAAM;UAACgG,aAAa,EAAEA,CAAA,KAAMzE,cAAc,CAAC,CAACD,WAAW;QAAE;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DtE,OAAA;UAAK+D,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BhE,OAAA,CAACL,UAAU;YAACO,aAAa,EAAEA,aAAc;YAACC,gBAAgB,EAAEA,gBAAiB;YAACC,MAAM,EAAEA;UAAO;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7F,CAAC,eACNtE,OAAA;UAAK+D,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClChE,OAAA;YAAK+D,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAEpD;UAAK;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMqB,eAAe,GAAI9C,KAAK,IAAK;IACjC9B,gBAAgB,CAAC8B,KAAK,CAAC;IACvB1B,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAMyE,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,cAAc,GAAGrF,MAAM,IAAI,EAAE;IAEnC,OAAO,CAAC,GAAGqF,cAAc,CAAC,CACvBC,MAAM,CAACjD,KAAK,IAAI;MAAA,IAAAkD,cAAA,EAAAC,eAAA,EAAAC,eAAA;MACf;MACA,MAAMC,WAAW,GAAG9E,UAAU,CAAC+E,WAAW,CAAC,CAAC;MAC5C,MAAMC,aAAa,GACjBhF,UAAU,KAAK,EAAE,IACjByB,KAAK,CAACwD,IAAI,CAACF,WAAW,CAAC,CAAC,CAACxC,QAAQ,CAACuC,WAAW,CAAC,IAC9C,CAAC,EAAAH,cAAA,GAAAlD,KAAK,CAACC,OAAO,cAAAiD,cAAA,uBAAbA,cAAA,CAAenB,SAAS,KAAI,EAAE,EAAEuB,WAAW,CAAC,CAAC,CAACxC,QAAQ,CAACuC,WAAW,CAAC,IACpE,CAAC,EAAAF,eAAA,GAAAnD,KAAK,CAACC,OAAO,cAAAkD,eAAA,uBAAbA,eAAA,CAAenB,aAAa,KAAI,EAAE,EAAEsB,WAAW,CAAC,CAAC,CAACxC,QAAQ,CAACuC,WAAW,CAAC,IACxE,CAAC,EAAAD,eAAA,GAAApD,KAAK,CAACC,OAAO,cAAAmD,eAAA,uBAAbA,eAAA,CAAenB,KAAK,KAAI,EAAE,EAAEqB,WAAW,CAAC,CAAC,CAACxC,QAAQ,CAACuC,WAAW,CAAC;;MAElE;MACA,MAAMI,WAAW,GAAGhF,YAAY,KAAK,KAAK,IAAIuB,KAAK,CAACwD,IAAI,KAAK/E,YAAY;MAEzE,OAAO8E,aAAa,IAAIE,WAAW;IACrC,CAAC,CAAC,CACDC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACd;MACA,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAACH,CAAC,CAACI,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC;MAC7C,MAAMC,KAAK,GAAG,IAAIH,IAAI,CAACF,CAAC,CAACG,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC;MAE7C,OAAOrF,SAAS,KAAK,QAAQ,GAAGsF,KAAK,GAAGJ,KAAK,GAAGA,KAAK,GAAGI,KAAK;IAC/D,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B5F,YAAY,CAAC,KAAK,CAAC;IACnBJ,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAMiG,sBAAsB,GAAG,MAAAA,CAAOnE,KAAK,EAAEoE,KAAK,KAAK;IACrDA,KAAK,CAACC,eAAe,CAAC,CAAC,CAAC,CAAC;IACzB,IAAI;MACF,MAAMpH,gBAAgB,CAAC+C,KAAK,EAAEvC,UAAU,CAAC;IAC3C,CAAC,CAAC,OAAOM,KAAK,EAAE;MACd6B,OAAO,CAAC7B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CuG,KAAK,CAAC,2CAA2C,CAAC;IACpD;EACF,CAAC;EAED,oBACEnH,OAAA;IAAK+D,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvChE,OAAA,CAACP,OAAO;MAAC+F,MAAM,EAAExE,WAAY;MAACyE,SAAS,EAAExE;IAAe;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC3DtE,OAAA;MAAK+D,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDhE,OAAA,CAACN,MAAM;QAACgG,aAAa,EAAEA,CAAA,KAAMzE,cAAc,CAAC,CAACD,WAAW;MAAE;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7DtE,OAAA;QAAK+D,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BhE,OAAA,CAACL,UAAU;UAACO,aAAa,EAAEA,aAAc;UAACC,gBAAgB,EAAEA,gBAAiB;UAACC,MAAM,EAAEA;QAAO;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7F,CAAC,eACNtE,OAAA,CAACT,MAAM,CAAC6H,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9B3D,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAE/BhE,OAAA;UAAK+D,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDhE,OAAA;YAAI+D,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACtE9D,MAAM,CAACiD,MAAM,GAAG,CAAC,iBAChBzD,OAAA;YAAK+D,SAAS,EAAC,0DAA0D;YAAAC,QAAA,GACtE4B,iBAAiB,CAAC,CAAC,CAACnC,MAAM,EAAC,MAAI,EAACjD,MAAM,CAACiD,MAAM,EAAC,SACjD;UAAA;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNtE,OAAA;UAAK+D,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAEhEhE,OAAA;YAAK+D,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjChE,OAAA;cAAK+D,SAAS,EAAC,sEAAsE;cAAAC,QAAA,eACnFhE,OAAA;gBAAK+D,SAAS,EAAC,wBAAwB;gBAACiB,KAAK,EAAC,4BAA4B;gBAACE,OAAO,EAAC,WAAW;gBAACD,IAAI,EAAC,cAAc;gBAAAjB,QAAA,eAChHhE,OAAA;kBAAM2H,QAAQ,EAAC,SAAS;kBAACpC,CAAC,EAAC,kHAAkH;kBAACqC,QAAQ,EAAC;gBAAS;kBAAAzD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtE,OAAA;cACEqG,IAAI,EAAC,MAAM;cACXwB,WAAW,EAAC,8CAA8C;cAC1DtE,KAAK,EAAEnC,UAAW;cAClB0G,QAAQ,EAAGC,CAAC,IAAK1G,aAAa,CAAC0G,CAAC,CAACC,MAAM,CAACzE,KAAK,CAAE;cAC/CQ,SAAS,EAAC;YAAsH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNtE,OAAA;YAAK+D,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7ChE,OAAA;cACEiI,EAAE,EAAC,aAAa;cAChB1E,KAAK,EAAEjC,YAAa;cACpBwG,QAAQ,EAAGC,CAAC,IAAKxG,eAAe,CAACwG,CAAC,CAACC,MAAM,CAACzE,KAAK,CAAE;cACjDQ,SAAS,EAAC,oIAAoI;cAC9I,cAAW,sBAAsB;cAAAC,QAAA,EAEhCtC,UAAU,CAAC6C,GAAG,CAAE8B,IAAI,iBACnBrG,OAAA;gBAAmBuD,KAAK,EAAE8C,IAAK;gBAAArC,QAAA,EAAEqC;cAAI,GAAxBA,IAAI;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA6B,CAC/C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNtE,OAAA;YAAK+D,SAAS,EAAC,+EAA+E;YAAAC,QAAA,gBAC5FhE,OAAA;cACEkI,OAAO,EAAEA,CAAA,KAAMzG,YAAY,CAAC,QAAQ,CAAE;cACtCsC,SAAS,EAAE,qDACTvC,SAAS,KAAK,QAAQ,GAClB,yBAAyB,GACzB,yCAAyC,EAC5C;cACH,cAAW,sBAAsB;cAAAwC,QAAA,eAEjChE,OAAA;gBAAK+D,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChChE,OAAA;kBAAKgF,KAAK,EAAC,4BAA4B;kBAACjB,SAAS,EAAC,cAAc;kBAACkB,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACC,MAAM,EAAC,cAAc;kBAAAnB,QAAA,eACpHhE,OAAA;oBAAMoF,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAA8C;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnH,CAAC,UAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACTtE,OAAA;cAAK+D,SAAS,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5CtE,OAAA;cACEkI,OAAO,EAAEA,CAAA,KAAMzG,YAAY,CAAC,QAAQ,CAAE;cACtCsC,SAAS,EAAE,qDACTvC,SAAS,KAAK,QAAQ,GAClB,yBAAyB,GACzB,yCAAyC,EAC5C;cACH,cAAW,sBAAsB;cAAAwC,QAAA,eAEjChE,OAAA;gBAAK+D,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChChE,OAAA;kBAAKgF,KAAK,EAAC,4BAA4B;kBAACjB,SAAS,EAAC,cAAc;kBAACkB,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACC,MAAM,EAAC,cAAc;kBAAAnB,QAAA,eACpHhE,OAAA;oBAAMoF,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAA8C;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnH,CAAC,UAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEL9D,MAAM,CAACiD,MAAM,KAAK,CAAC,gBAClBzD,OAAA;UAAK+D,SAAS,EAAC,4CAA4C;UAAAC,QAAA,eACzDhE,OAAA;YAAG+D,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA+C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC,GACJsB,iBAAiB,CAAC,CAAC,CAACnC,MAAM,KAAK,CAAC,gBAClCzD,OAAA;UAAK+D,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBACzDhE,OAAA;YAAG+D,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAiE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClGtE,OAAA;YACEkI,OAAO,EAAEA,CAAA,KAAM;cACb7G,aAAa,CAAC,EAAE,CAAC;cACjBE,eAAe,CAAC,KAAK,CAAC;YACxB,CAAE;YACFwC,SAAS,EAAC,2FAA2F;YAAAC,QAAA,EACtG;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAENtE,OAAA;UAAK+D,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClE4B,iBAAiB,CAAC,CAAC,CAACrB,GAAG,CAAE1B,KAAK;YAAA,IAAAsF,eAAA;YAAA,oBAC7BnI,OAAA,CAACT,MAAM,CAAC6H,GAAG;cAETgB,UAAU,EAAE;gBAAEb,CAAC,EAAE,CAAC,CAAC;gBAAEc,SAAS,EAAE;cAA0E,CAAE;cAC5GtE,SAAS,EAAC,0FAA0F;cACpGmE,OAAO,EAAEA,CAAA,KAAMvC,eAAe,CAAC9C,KAAK,CAAE;cAAAmB,QAAA,gBAEtChE,OAAA;gBAAK+D,SAAS,EAAE,OACdlB,KAAK,CAACwD,IAAI,KAAK,WAAW,GAAG,cAAc,GAC3CxD,KAAK,CAACwD,IAAI,KAAK,sBAAsB,GAAG,cAAc,GACtDxD,KAAK,CAACwD,IAAI,KAAK,0BAA0B,GAAG,cAAc,GAC1DxD,KAAK,CAACwD,IAAI,KAAK,aAAa,GAAG,cAAc,GAC7CxD,KAAK,CAACwD,IAAI,KAAK,cAAc,GAAG,cAAc,GAC9CxD,KAAK,CAACwD,IAAI,KAAK,cAAc,GAAG,cAAc,GAAG,aAAa;cAC7D;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACVtE,OAAA;gBAAK+D,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBhE,OAAA;kBAAK+D,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrDhE,OAAA;oBAAI+D,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAEnB,KAAK,CAACwD;kBAAI;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrEtE,OAAA;oBAAM+D,SAAS,EAAC,+DAA+D;oBAAAC,QAAA,EAC5E,IAAI2C,IAAI,CAAC9D,KAAK,CAAC+D,SAAS,CAAC,CAAC0B,kBAAkB,CAAC;kBAAC;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNtE,OAAA;kBAAG+D,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAC/C,EAAAmE,eAAA,GAAAtF,KAAK,CAACC,OAAO,cAAAqF,eAAA,uBAAbA,eAAA,CAAevD,SAAS,KAAI;gBAAuB;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACJtE,OAAA;kBAAK+D,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDhE,OAAA;oBAAM+D,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EACpC,IAAI2C,IAAI,CAAC9D,KAAK,CAAC+D,SAAS,CAAC,CAAC2B,kBAAkB,CAAC;kBAAC;oBAAApE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,eACPtE,OAAA;oBAAK+D,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtChE,OAAA;sBACEkI,OAAO,EAAGH,CAAC,IAAKf,sBAAsB,CAACnE,KAAK,EAAEkF,CAAC,CAAE;sBACjDhE,SAAS,EAAC,yJAAyJ;sBACnKyE,KAAK,EAAC,cAAc;sBAAAxE,QAAA,eAEpBhE,OAAA;wBACEgF,KAAK,EAAC,4BAA4B;wBAClCjB,SAAS,EAAC,aAAa;wBACvBkB,IAAI,EAAC,MAAM;wBACXC,OAAO,EAAC,WAAW;wBACnBC,MAAM,EAAC,cAAc;wBAAAnB,QAAA,eAErBhE,OAAA;0BACEoF,aAAa,EAAC,OAAO;0BACrBC,cAAc,EAAC,OAAO;0BACtBC,WAAW,EAAE,CAAE;0BACfC,CAAC,EAAC;wBAAiI;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACTtE,OAAA;sBAAM+D,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAAC;oBAAqB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAnDDzB,KAAK,CAAC4F,GAAG;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoDJ,CAAC;UAAA,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGLpD,SAAS,IAAIJ,aAAa,iBACzBd,OAAA;MAAK+D,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7FhE,OAAA,CAACT,MAAM,CAAC6H,GAAG;QACTC,OAAO,EAAE;UAAEqB,KAAK,EAAE,GAAG;UAAEpB,OAAO,EAAE;QAAE,CAAE;QACpCE,OAAO,EAAE;UAAEkB,KAAK,EAAE,CAAC;UAAEpB,OAAO,EAAE;QAAE,CAAE;QAClCvD,SAAS,EAAC,+EAA+E;QAAAC,QAAA,gBAGzFhE,OAAA;UAAK+D,SAAS,EAAC,+HAA+H;UAAAC,QAAA,gBAC5IhE,OAAA;YAAK+D,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChChE,OAAA;cAAK+D,SAAS,EAAE,gEACdjD,aAAa,CAACuF,IAAI,KAAK,WAAW,GAAG,iBAAiB,GACtDvF,aAAa,CAACuF,IAAI,KAAK,sBAAsB,GAAG,iBAAiB,GACjEvF,aAAa,CAACuF,IAAI,KAAK,0BAA0B,GAAG,iBAAiB,GACrEvF,aAAa,CAACuF,IAAI,KAAK,aAAa,GAAG,iBAAiB,GACxDvF,aAAa,CAACuF,IAAI,KAAK,cAAc,GAAG,iBAAiB,GAAG,aAAa,EACxE;cAAArC,QAAA,eACDhE,OAAA;gBAAKgF,KAAK,EAAC,4BAA4B;gBAACjB,SAAS,EAAC,oBAAoB;gBAACkB,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAAnB,QAAA,eAC1HhE,OAAA;kBAAMoF,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAsH;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3L;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtE,OAAA;cAAAgE,QAAA,gBACEhE,OAAA;gBAAI+D,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GAAElD,aAAa,CAACuF,IAAI,EAAC,QAAM;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClEtE,OAAA;gBAAK+D,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9ChE,OAAA;kBAAM+D,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBACjChE,OAAA;oBAAKgF,KAAK,EAAC,4BAA4B;oBAACjB,SAAS,EAAC,cAAc;oBAACkB,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAAnB,QAAA,eACpHhE,OAAA;sBAAMoF,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAwF;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7J,CAAC,aACG,EAAC,IAAIqC,IAAI,CAAC7F,aAAa,CAAC8F,SAAS,CAAC,CAAC0B,kBAAkB,CAAC,CAAC;gBAAA;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC,EACNxD,aAAa,CAAC6H,SAAS,IAAI7H,aAAa,CAAC6H,SAAS,KAAK7H,aAAa,CAAC8F,SAAS,iBAC7E5G,OAAA;kBAAM+D,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACtChE,OAAA;oBAAKgF,KAAK,EAAC,4BAA4B;oBAACjB,SAAS,EAAC,cAAc;oBAACkB,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAAnB,QAAA,eACpHhE,OAAA;sBAAMoF,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAA6C;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClH,CAAC,aACG,EAAC,IAAIqC,IAAI,CAAC7F,aAAa,CAAC6H,SAAS,CAAC,CAACL,kBAAkB,CAAC,CAAC;gBAAA;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtE,OAAA;YAAK+D,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAC/BnE,uBAAuB,CAAC,eAAe,EAAE,GAAGiB,aAAa,CAACuF,IAAI,UAAU,IAAIM,IAAI,CAAC7F,aAAa,CAAC8F,SAAS,CAAC,CAAC0B,kBAAkB,CAAC,CAAC,CAACrE,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,eAC1JjE,OAAA;cACEkI,OAAO,EAAEnB,gBAAiB;cAC1BhD,SAAS,EAAC,2GAA2G;cAAAC,QAAA,eAErHhE,OAAA;gBAAKgF,KAAK,EAAC,4BAA4B;gBAACjB,SAAS,EAAC,SAAS;gBAACkB,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAAnB,QAAA,eAC/GhE,OAAA;kBAAMoF,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAsB;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtE,OAAA;UAAK+D,SAAS,EAAC,iBAAiB;UAAC6E,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAqB,CAAE;UAAA7E,QAAA,eAE1EhE,OAAA;YAAK+D,SAAS,EAAC,KAAK;YAACkE,EAAE,EAAC,eAAe;YAAAjE,QAAA,EACpCpB,kBAAkB,CAAC9B,aAAa;UAAC;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtE,OAAA;UAAK+D,SAAS,EAAC,uFAAuF;UAAAC,QAAA,gBACpGhE,OAAA;YACEkI,OAAO,EAAGH,CAAC,IAAKf,sBAAsB,CAAClG,aAAa,EAAEiH,CAAC,CAAE;YACzDhE,SAAS,EAAC,0JAA0J;YAAAC,QAAA,gBAEpKhE,OAAA;cACEgF,KAAK,EAAC,4BAA4B;cAClCjB,SAAS,EAAC,cAAc;cACxBkB,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnBC,MAAM,EAAC,cAAc;cAAAnB,QAAA,eAErBhE,OAAA;gBACEoF,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,WAAW,EAAE,CAAE;gBACfC,CAAC,EAAC;cAAiI;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,gBAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtE,OAAA;YACEkI,OAAO,EAAEnB,gBAAiB;YAC1BhD,SAAS,EAAC,iHAAiH;YAAAC,QAAA,gBAE3HhE,OAAA;cAAKgF,KAAK,EAAC,4BAA4B;cAACjB,SAAS,EAAC,cAAc;cAACkB,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAnB,QAAA,eACpHhE,OAAA;gBAAMoF,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAsB;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC,SAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjE,EAAA,CAviBIJ,OAAO;EAAA,QACYZ,SAAS,EACdG,OAAO;AAAA;AAAAsJ,EAAA,GAFrB7I,OAAO;AAyiBb,eAAeA,OAAO;AAAC,IAAA6I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}