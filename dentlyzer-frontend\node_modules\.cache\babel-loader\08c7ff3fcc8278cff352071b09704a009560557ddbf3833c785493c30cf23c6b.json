{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\superadmin\\\\Analytics.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { motion } from 'framer-motion';\nimport { Bar, Pie, Line, Doughnut } from 'react-chartjs-2';\nimport { Chart as ChartJS, CategoryScale, LinearScale, BarElement, ArcElement, LineElement, PointElement, Title, Tooltip, Legend } from 'chart.js';\nimport Navbar from '../student/Navbar';\nimport Loader from '../components/Loader';\nimport { useAuth } from '../context/AuthContext';\nimport SuperAdminSidebar from './SuperAdminSidebar';\nimport { FaChartBar, FaUserFriends, FaCalendarAlt, FaHospital, FaUserInjured, Fa<PERSON><PERSON>board<PERSON>ist, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>aShieldAlt, Fa<PERSON>sers, FaGraduationCap } from 'react-icons/fa';\n\n// Register ChartJS components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, BarElement, ArcElement, LineElement, PointElement, Title, Tooltip, Legend);\nconst Analytics = () => {\n  _s();\n  var _analytics$appointmen, _analytics$appointmen2, _analytics$appointmen3;\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [analytics, setAnalytics] = useState({\n    counts: {\n      totalStudents: 0,\n      totalSupervisors: 0,\n      totalAdmins: 0,\n      totalAssistants: 0,\n      totalDentists: 0,\n      totalUniversities: 0,\n      totalPatients: 0,\n      totalAppointments: 0,\n      totalAccounts: 0\n    },\n    growth: {\n      studentGrowth: [],\n      supervisorGrowth: [],\n      adminGrowth: [],\n      assistantGrowth: [],\n      dentistGrowth: [],\n      universityGrowth: []\n    },\n    appointmentStats: {\n      statusCounts: {},\n      appointmentsByMonth: []\n    },\n    universityDistribution: [],\n    patientDemographics: {\n      genderDistribution: [],\n      ageDistribution: []\n    },\n    procedureTypes: [],\n    recentActivity: [],\n    businessMetrics: {\n      activeUsers: {\n        total: 0,\n        byRole: {}\n      },\n      universityAdoption: {\n        total: 0,\n        active: 0,\n        adoptionRate: 0\n      },\n      growthRate: {\n        monthly: 0,\n        quarterly: 0\n      },\n      retentionMetrics: {\n        userRetention: 0,\n        universityRetention: 0\n      }\n    },\n    systemUsage: {\n      appointmentMetrics: {\n        completionRate: 0,\n        cancellationRate: 0\n      },\n      patientMetrics: {\n        averagePatientsPerStudent: 0,\n        treatmentCompletionRate: 0\n      },\n      featureUsage: {\n        mostUsedFeatures: [],\n        leastUsedFeatures: []\n      }\n    },\n    performanceMetrics: {\n      responseTime: 0,\n      uptime: 0,\n      errorRate: 0,\n      dataQuality: {\n        completeness: 0,\n        accuracy: 0\n      }\n    }\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [timeRange, setTimeRange] = useState('6months'); // '1month', '3months', '6months', '1year'\n  const [activeTab, setActiveTab] = useState('overview'); // 'overview', 'business', 'accounts', 'universities', 'patients', 'appointments', 'performance', 'activity'\n  const [chartType, setChartType] = useState({\n    accounts: 'bar',\n    appointments: 'line',\n    universities: 'bar',\n    gender: 'pie',\n    age: 'bar',\n    procedures: 'bar'\n  });\n  const navigate = useNavigate();\n  const {\n    user,\n    token\n  } = useAuth();\n  useEffect(() => {\n    const fetchAnalytics = async () => {\n      if (!user || !token) {\n        setError('Please log in to view analytics.');\n        setLoading(false);\n        return;\n      }\n      setLoading(true);\n      try {\n        const config = {\n          headers: {\n            Authorization: `Bearer ${token}`\n          },\n          params: {\n            timeRange\n          }\n        };\n        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/analytics`, config);\n        setAnalytics(response.data);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response3$data, _err$response4;\n        console.error('Fetch error:', ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n        const errorMessage = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.status) === 401 ? 'Unauthorized. Please log in again.' : ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || 'Failed to load analytics';\n        setError(errorMessage);\n        if (((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 401) {\n          navigate('/login');\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchAnalytics();\n  }, [user, token, navigate, timeRange]);\n\n  // Animation variants with reduced motion\n  const item = {\n    hidden: {\n      opacity: 0,\n      y: 5\n    },\n    show: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.15\n      }\n    }\n  };\n\n  // Toggle chart type\n  const toggleChartType = chartName => {\n    setChartType(prev => {\n      const types = {\n        'bar': 'line',\n        'line': 'pie',\n        'pie': 'doughnut',\n        'doughnut': 'bar'\n      };\n      return {\n        ...prev,\n        [chartName]: types[prev[chartName]]\n      };\n    });\n  };\n\n  // Chart Data\n  const accountsData = {\n    labels: ['Students', 'Supervisors', 'Admins', 'Assistants', 'Dentists'],\n    datasets: [{\n      label: 'Number of Accounts',\n      data: [analytics.counts.totalStudents, analytics.counts.totalSupervisors, analytics.counts.totalAdmins, analytics.counts.totalAssistants, analytics.counts.totalDentists],\n      backgroundColor: ['rgba(54, 162, 235, 0.7)', 'rgba(255, 99, 132, 0.7)', 'rgba(255, 206, 86, 0.7)', 'rgba(75, 192, 192, 0.7)', 'rgba(153, 102, 255, 0.7)'],\n      borderColor: ['rgba(54, 162, 235, 1)', 'rgba(255, 99, 132, 1)', 'rgba(255, 206, 86, 1)', 'rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)'],\n      borderWidth: 1\n    }]\n  };\n  const appointmentsData = {\n    labels: analytics.appointmentStats.appointmentsByMonth.map(data => `${data.month} ${data.year}`) || ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],\n    datasets: [{\n      label: 'Appointments',\n      data: analytics.appointmentStats.appointmentsByMonth.map(data => data.count) || [10, 15, 20, 25, 30, 35],\n      backgroundColor: 'rgba(75, 192, 192, 0.5)',\n      borderColor: 'rgba(75, 192, 192, 1)',\n      borderWidth: 1,\n      tension: 0.4\n    }]\n  };\n\n  // University distribution data\n  const universityData = {\n    labels: analytics.universityDistribution.map(uni => uni.universityName) || ['University A', 'University B', 'University C'],\n    datasets: [{\n      label: 'Students per University',\n      data: analytics.universityDistribution.map(uni => uni.studentCount) || [50, 30, 20],\n      backgroundColor: ['rgba(255, 99, 132, 0.7)', 'rgba(54, 162, 235, 0.7)', 'rgba(255, 206, 86, 0.7)', 'rgba(75, 192, 192, 0.7)', 'rgba(153, 102, 255, 0.7)', 'rgba(255, 159, 64, 0.7)'],\n      borderColor: ['rgba(255, 99, 132, 1)', 'rgba(54, 162, 235, 1)', 'rgba(255, 206, 86, 1)', 'rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)', 'rgba(255, 159, 64, 1)'],\n      borderWidth: 1\n    }]\n  };\n\n  // Gender distribution data\n  const genderData = {\n    labels: analytics.patientDemographics.genderDistribution.map(item => item.gender) || ['Male', 'Female', 'Other'],\n    datasets: [{\n      label: 'Gender Distribution',\n      data: analytics.patientDemographics.genderDistribution.map(item => item.count) || [45, 55, 5],\n      backgroundColor: ['rgba(54, 162, 235, 0.7)', 'rgba(255, 99, 132, 0.7)', 'rgba(255, 206, 86, 0.7)'],\n      borderColor: ['rgba(54, 162, 235, 1)', 'rgba(255, 99, 132, 1)', 'rgba(255, 206, 86, 1)'],\n      borderWidth: 1\n    }]\n  };\n\n  // Age distribution data\n  const ageData = {\n    labels: analytics.patientDemographics.ageDistribution.map(item => item.ageGroup) || ['0-18', '19-30', '31-45', '46-60', '60+'],\n    datasets: [{\n      label: 'Age Distribution',\n      data: analytics.patientDemographics.ageDistribution.map(item => item.count) || [15, 25, 30, 20, 10],\n      backgroundColor: 'rgba(153, 102, 255, 0.7)',\n      borderColor: 'rgba(153, 102, 255, 1)',\n      borderWidth: 1\n    }]\n  };\n\n  // Procedure types data\n  const procedureData = {\n    labels: analytics.procedureTypes.map(item => item.procedure) || ['Filling', 'Extraction', 'Root Canal', 'Cleaning', 'Crown'],\n    datasets: [{\n      label: 'Procedure Types',\n      data: analytics.procedureTypes.map(item => item.count) || [40, 25, 15, 35, 20],\n      backgroundColor: ['rgba(255, 99, 132, 0.7)', 'rgba(54, 162, 235, 0.7)', 'rgba(255, 206, 86, 0.7)', 'rgba(75, 192, 192, 0.7)', 'rgba(153, 102, 255, 0.7)'],\n      borderColor: ['rgba(255, 99, 132, 1)', 'rgba(54, 162, 235, 1)', 'rgba(255, 206, 86, 1)', 'rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)'],\n      borderWidth: 1\n    }]\n  };\n\n  // Render the appropriate chart based on type\n  const renderChart = (data, type, height = '300px') => {\n    const chartProps = {\n      data,\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n          legend: {\n            position: 'top'\n          }\n        }\n      },\n      height\n    };\n    switch (type) {\n      case 'bar':\n        return /*#__PURE__*/_jsxDEV(Bar, {\n          ...chartProps\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 16\n        }, this);\n      case 'line':\n        return /*#__PURE__*/_jsxDEV(Line, {\n          ...chartProps\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 16\n        }, this);\n      case 'pie':\n        return /*#__PURE__*/_jsxDEV(Pie, {\n          ...chartProps\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 16\n        }, this);\n      case 'doughnut':\n        return /*#__PURE__*/_jsxDEV(Doughnut, {\n          ...chartProps\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Bar, {\n          ...chartProps\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 301,\n    columnNumber: 23\n  }, this);\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(SuperAdminSidebar, {\n        isOpen: sidebarOpen,\n        setIsOpen: setSidebarOpen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex flex-col overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(Navbar, {\n          toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 overflow-y-auto p-6 bg-gradient-to-br from-blue-50 to-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-lg shadow-md p-6 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl font-bold text-red-600 mb-4\",\n                children: \"Error\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-700\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => navigate('/login'),\n                className: \"mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\",\n                children: \"Go to Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(SuperAdminSidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              variants: item,\n              className: \"bg-gradient-to-r from-[#0077B6] to-[#20B2AA] rounded-xl shadow-lg p-6 text-white\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col md:flex-row md:justify-between md:items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-3xl font-bold mb-2\",\n                    children: \"Analytics Dashboard\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"opacity-80\",\n                    children: \"Comprehensive insights into your dental management system\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 md:mt-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"timeRange\",\n                    className: \"block text-sm font-medium text-white mb-2\",\n                    children: \"Time Range\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 346,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    id: \"timeRange\",\n                    value: timeRange,\n                    onChange: e => setTimeRange(e.target.value),\n                    className: \"bg-white bg-opacity-20 text-white border border-white border-opacity-30 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"1month\",\n                      children: \"Last Month\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 353,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"3months\",\n                      children: \"Last 3 Months\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 354,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"6months\",\n                      children: \"Last 6 Months\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 355,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"1year\",\n                      children: \"Last Year\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 356,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"all\",\n                      children: \"All Time\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 357,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: item,\n              className: \"bg-white rounded-xl shadow-md overflow-hidden\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex overflow-x-auto\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveTab('overview'),\n                  className: `flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${activeTab === 'overview' ? 'border-[#0077B6] text-[#0077B6]' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                  children: [/*#__PURE__*/_jsxDEV(FaChartBar, {\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 19\n                  }, this), \"Overview\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveTab('business'),\n                  className: `flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${activeTab === 'business' ? 'border-[#0077B6] text-[#0077B6]' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                  children: [/*#__PURE__*/_jsxDEV(FaChartBar, {\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 19\n                  }, this), \"Business\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveTab('accounts'),\n                  className: `flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${activeTab === 'accounts' ? 'border-[#0077B6] text-[#0077B6]' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                  children: [/*#__PURE__*/_jsxDEV(FaUserFriends, {\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 19\n                  }, this), \"Accounts\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveTab('universities'),\n                  className: `flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${activeTab === 'universities' ? 'border-[#0077B6] text-[#0077B6]' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                  children: [/*#__PURE__*/_jsxDEV(FaHospital, {\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 19\n                  }, this), \"Universities\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveTab('patients'),\n                  className: `flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${activeTab === 'patients' ? 'border-[#0077B6] text-[#0077B6]' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                  children: [/*#__PURE__*/_jsxDEV(FaUserInjured, {\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 19\n                  }, this), \"Patients\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveTab('appointments'),\n                  className: `flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${activeTab === 'appointments' ? 'border-[#0077B6] text-[#0077B6]' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                  children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 19\n                  }, this), \"Appointments\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveTab('performance'),\n                  className: `flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${activeTab === 'performance' ? 'border-[#0077B6] text-[#0077B6]' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                  children: [/*#__PURE__*/_jsxDEV(FaShieldAlt, {\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 19\n                  }, this), \"Performance\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveTab('activity'),\n                  className: `flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${activeTab === 'activity' ? 'border-[#0077B6] text-[#0077B6]' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                  children: [/*#__PURE__*/_jsxDEV(FaClipboardList, {\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 451,\n                    columnNumber: 19\n                  }, this), \"Activity\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6\",\n              children: [activeTab === 'overview' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-xl shadow-md p-5 border-l-4 border-[#0077B6]\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-gray-500 text-sm font-medium\",\n                      children: \"Total Accounts\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 465,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-3xl font-bold text-[#0077B6]\",\n                      children: analytics.counts.totalAccounts\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 466,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 464,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-xl shadow-md p-5 border-l-4 border-[#20B2AA]\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-gray-500 text-sm font-medium\",\n                      children: \"Total Universities\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 469,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-3xl font-bold text-[#20B2AA]\",\n                      children: analytics.counts.totalUniversities\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 470,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-xl shadow-md p-5 border-l-4 border-[#0077B6]\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-gray-500 text-sm font-medium\",\n                      children: \"Total Patients\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 473,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-3xl font-bold text-[#0077B6]\",\n                      children: analytics.counts.totalPatients\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 474,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-xl shadow-md p-5 border-l-4 border-[#20B2AA]\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-gray-500 text-sm font-medium\",\n                      children: \"Total Appointments\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 477,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-3xl font-bold text-[#20B2AA]\",\n                      children: analytics.counts.totalAppointments\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 478,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                    variants: item,\n                    className: \"bg-white rounded-xl shadow-md p-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center mb-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                        className: \"text-xl font-bold text-[#0077B6]\",\n                        children: \"Account Distribution\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 487,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => toggleChartType('accounts'),\n                        className: \"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-colors text-sm\",\n                        children: \"Change Chart Type\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 488,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 486,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        height: '300px'\n                      },\n                      children: renderChart(accountsData, chartType.accounts)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 495,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    variants: item,\n                    className: \"bg-white rounded-xl shadow-md p-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center mb-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                        className: \"text-xl font-bold text-[#0077B6]\",\n                        children: \"Appointments Over Time\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 503,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => toggleChartType('appointments'),\n                        className: \"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-colors text-sm\",\n                        children: \"Change Chart Type\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 504,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 502,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        height: '300px'\n                      },\n                      children: renderChart(appointmentsData, chartType.appointments)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 511,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white rounded-xl shadow-md p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6] mb-4\",\n                    children: \"Recent Activity\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 519,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"overflow-hidden\",\n                    children: /*#__PURE__*/_jsxDEV(\"table\", {\n                      className: \"min-w-full divide-y divide-gray-200\",\n                      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                        className: \"bg-gray-50\",\n                        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                            children: \"Action\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 524,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                            children: \"User\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 525,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                            children: \"Date\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 526,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 523,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 522,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                        className: \"bg-white divide-y divide-gray-200\",\n                        children: analytics.recentActivity.slice(0, 5).map((activity, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                          className: \"hover:bg-gray-50\",\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                            children: activity.action\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 532,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: activity.user\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 533,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: new Date(activity.date).toLocaleString()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 534,\n                            columnNumber: 31\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 531,\n                          columnNumber: 29\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 529,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 521,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 520,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 17\n              }, this), activeTab === 'business' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-xl shadow-md p-5 border-l-4 border-green-500\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-gray-500 text-sm font-medium\",\n                      children: \"Active Users\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 552,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-3xl font-bold text-green-600\",\n                      children: analytics.businessMetrics.activeUsers.total\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 553,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-400 mt-1\",\n                      children: \"In selected period\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 554,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 551,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-xl shadow-md p-5 border-l-4 border-blue-500\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-gray-500 text-sm font-medium\",\n                      children: \"University Adoption\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 557,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-3xl font-bold text-blue-600\",\n                      children: [Math.round(analytics.businessMetrics.universityAdoption.adoptionRate), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 558,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-400 mt-1\",\n                      children: [analytics.businessMetrics.universityAdoption.active, \" of \", analytics.businessMetrics.universityAdoption.total, \" universities\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 559,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 556,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-xl shadow-md p-5 border-l-4 border-purple-500\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-gray-500 text-sm font-medium\",\n                      children: \"User Retention\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 562,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-3xl font-bold text-purple-600\",\n                      children: [analytics.businessMetrics.retentionMetrics.userRetention, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 563,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-400 mt-1\",\n                      children: \"Active users\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 564,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 561,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-xl shadow-md p-5 border-l-4 border-orange-500\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-gray-500 text-sm font-medium\",\n                      children: \"Monthly Growth\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 567,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-3xl font-bold text-orange-600\",\n                      children: analytics.businessMetrics.growthRate.monthly\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 568,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-400 mt-1\",\n                      children: \"New users this month\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 569,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 566,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white rounded-xl shadow-md p-6 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6] mb-4\",\n                    children: \"Active Users by Role\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\",\n                    children: Object.entries(analytics.businessMetrics.activeUsers.byRole).map(([role, count]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gray-50 rounded-lg p-4 text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-2xl font-bold text-[#0077B6]\",\n                        children: count\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 579,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-600 capitalize\",\n                        children: [role, \"s\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 580,\n                        columnNumber: 27\n                      }, this)]\n                    }, role, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 578,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 576,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-xl shadow-md p-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-bold text-[#0077B6] mb-4\",\n                      children: \"Appointment Efficiency\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 589,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-gray-600\",\n                          children: \"Completion Rate\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 592,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-bold text-green-600\",\n                          children: [Math.round(analytics.systemUsage.appointmentMetrics.completionRate), \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 593,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 591,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-green-500 h-2 rounded-full\",\n                          style: {\n                            width: `${analytics.systemUsage.appointmentMetrics.completionRate}%`\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 596,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 595,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-gray-600\",\n                          children: \"Cancellation Rate\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 602,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-bold text-red-600\",\n                          children: [Math.round(analytics.systemUsage.appointmentMetrics.cancellationRate), \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 603,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 601,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-red-500 h-2 rounded-full\",\n                          style: {\n                            width: `${analytics.systemUsage.appointmentMetrics.cancellationRate}%`\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 606,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 605,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 590,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 588,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-xl shadow-md p-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-bold text-[#0077B6] mb-4\",\n                      children: \"Patient Management\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 615,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-3xl font-bold text-[#20B2AA]\",\n                          children: analytics.systemUsage.patientMetrics.averagePatientsPerStudent\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 618,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm text-gray-600\",\n                          children: \"Avg Patients per Student\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 619,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 617,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-3xl font-bold text-[#0077B6]\",\n                          children: [analytics.systemUsage.patientMetrics.treatmentCompletionRate, \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 622,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm text-gray-600\",\n                          children: \"Treatment Completion Rate\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 623,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 621,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 616,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 614,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white rounded-xl shadow-md p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-bold text-[#0077B6] mb-4\",\n                    children: \"Feature Usage Statistics\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 631,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-gray-700 mb-3\",\n                        children: \"Most Used Features\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 634,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-2\",\n                        children: analytics.systemUsage.featureUsage.mostUsedFeatures.slice(0, 5).map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-between items-center p-2 bg-green-50 rounded\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm text-gray-700\",\n                            children: feature.feature\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 638,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"font-bold text-green-600\",\n                            children: feature.usage\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 639,\n                            columnNumber: 31\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 637,\n                          columnNumber: 29\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 635,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 633,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-gray-700 mb-3\",\n                        children: \"Least Used Features\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 645,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-2\",\n                        children: analytics.systemUsage.featureUsage.leastUsedFeatures.slice(0, 5).map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-between items-center p-2 bg-red-50 rounded\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm text-gray-700\",\n                            children: feature.feature\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 649,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"font-bold text-red-600\",\n                            children: feature.usage\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 650,\n                            columnNumber: 31\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 648,\n                          columnNumber: 29\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 646,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 644,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 632,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 630,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 17\n              }, this), activeTab === 'accounts' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white rounded-xl shadow-md p-6 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-xl font-bold text-gray-800\",\n                      children: \"Account Distribution\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 666,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => toggleChartType('accounts'),\n                      className: \"px-3 py-1 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors text-sm\",\n                      children: \"Change Chart Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 667,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 665,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      height: '400px'\n                    },\n                    children: renderChart(accountsData, chartType.accounts)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 674,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 664,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white rounded-xl shadow-md p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-gray-800 mb-4\",\n                    children: \"Account Growth Over Time\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 681,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-blue-50 rounded-lg p-4 border border-blue-200\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-blue-800\",\n                        children: \"Students\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 684,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-blue-600\",\n                        children: analytics.counts.totalStudents\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 685,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 683,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-green-50 rounded-lg p-4 border border-green-200\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-green-800\",\n                        children: \"Supervisors\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 688,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-green-600\",\n                        children: analytics.counts.totalSupervisors\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 689,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 687,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-purple-50 rounded-lg p-4 border border-purple-200\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-purple-800\",\n                        children: \"Admins\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 692,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-purple-600\",\n                        children: analytics.counts.totalAdmins\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 693,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 691,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 682,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 680,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 662,\n                columnNumber: 17\n              }, this), activeTab === 'universities' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white rounded-xl shadow-md p-6 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-xl font-bold text-gray-800\",\n                      children: \"University Distribution\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 706,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => toggleChartType('universities'),\n                      className: \"px-3 py-1 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors text-sm\",\n                      children: \"Change Chart Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 707,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 705,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      height: '400px'\n                    },\n                    children: renderChart(universityData, chartType.universities)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 714,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 704,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white rounded-xl shadow-md p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-gray-800 mb-4\",\n                    children: \"University Statistics\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 721,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"overflow-hidden\",\n                    children: /*#__PURE__*/_jsxDEV(\"table\", {\n                      className: \"min-w-full divide-y divide-gray-200\",\n                      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                        className: \"bg-gray-50\",\n                        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                            children: \"University\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 726,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                            children: \"Students\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 727,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                            children: \"ID\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 728,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 725,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 724,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                        className: \"bg-white divide-y divide-gray-200\",\n                        children: analytics.universityDistribution.map((uni, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                          className: \"hover:bg-gray-50\",\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                            children: uni.universityName\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 734,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: uni.studentCount\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 735,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: uni.universityId\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 736,\n                            columnNumber: 31\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 733,\n                          columnNumber: 29\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 731,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 723,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 722,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 720,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 17\n              }, this), activeTab === 'patients' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-xl shadow-md p-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center mb-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                        className: \"text-xl font-bold text-gray-800\",\n                        children: \"Gender Distribution\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 754,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => toggleChartType('gender'),\n                        className: \"px-3 py-1 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors text-sm\",\n                        children: \"Change Chart Type\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 755,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 753,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        height: '300px'\n                      },\n                      children: renderChart(genderData, chartType.gender)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 762,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 752,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-xl shadow-md p-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center mb-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                        className: \"text-xl font-bold text-gray-800\",\n                        children: \"Age Distribution\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 770,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => toggleChartType('age'),\n                        className: \"px-3 py-1 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors text-sm\",\n                        children: \"Change Chart Type\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 771,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 769,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        height: '300px'\n                      },\n                      children: renderChart(ageData, chartType.age)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 778,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 768,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 750,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white rounded-xl shadow-md p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-xl font-bold text-gray-800\",\n                      children: \"Procedure Types\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 787,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => toggleChartType('procedures'),\n                      className: \"px-3 py-1 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors text-sm\",\n                      children: \"Change Chart Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 788,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 786,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      height: '300px'\n                    },\n                    children: renderChart(procedureData, chartType.procedures)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 795,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 785,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 748,\n                columnNumber: 17\n              }, this), activeTab === 'appointments' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white rounded-xl shadow-md p-6 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-xl font-bold text-gray-800\",\n                      children: \"Appointments Over Time\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 808,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => toggleChartType('appointments'),\n                      className: \"px-3 py-1 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors text-sm\",\n                      children: \"Change Chart Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 809,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 807,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      height: '400px'\n                    },\n                    children: renderChart(appointmentsData, chartType.appointments)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 816,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 806,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white rounded-xl shadow-md p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-gray-800 mb-4\",\n                    children: \"Appointment Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 823,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-green-50 rounded-lg p-4 border border-green-200\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-green-800\",\n                        children: \"Completed\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 826,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-green-600\",\n                        children: ((_analytics$appointmen = analytics.appointmentStats.statusCounts) === null || _analytics$appointmen === void 0 ? void 0 : _analytics$appointmen.completed) || 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 827,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 825,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-yellow-50 rounded-lg p-4 border border-yellow-200\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-yellow-800\",\n                        children: \"Pending\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 830,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-yellow-600\",\n                        children: ((_analytics$appointmen2 = analytics.appointmentStats.statusCounts) === null || _analytics$appointmen2 === void 0 ? void 0 : _analytics$appointmen2.pending) || 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 831,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 829,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-red-50 rounded-lg p-4 border border-red-200\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-sm font-medium text-red-800\",\n                        children: \"Cancelled\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 834,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold text-red-600\",\n                        children: ((_analytics$appointmen3 = analytics.appointmentStats.statusCounts) === null || _analytics$appointmen3 === void 0 ? void 0 : _analytics$appointmen3.cancelled) || 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 835,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 833,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 824,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 822,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 804,\n                columnNumber: 17\n              }, this), activeTab === 'performance' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-xl shadow-md p-5 border-l-4 border-green-500\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-gray-500 text-sm font-medium\",\n                      children: \"Response Time\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 848,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-3xl font-bold text-green-600\",\n                      children: [analytics.performanceMetrics.responseTime, \"ms\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 849,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-400 mt-1\",\n                      children: \"Average response time\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 850,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 847,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-xl shadow-md p-5 border-l-4 border-blue-500\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-gray-500 text-sm font-medium\",\n                      children: \"System Uptime\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 853,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-3xl font-bold text-blue-600\",\n                      children: [analytics.performanceMetrics.uptime, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 854,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-400 mt-1\",\n                      children: \"System availability\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 855,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 852,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-xl shadow-md p-5 border-l-4 border-yellow-500\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-gray-500 text-sm font-medium\",\n                      children: \"Error Rate\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 858,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-3xl font-bold text-yellow-600\",\n                      children: [analytics.performanceMetrics.errorRate, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 859,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-400 mt-1\",\n                      children: \"System error rate\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 860,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 857,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-xl shadow-md p-5 border-l-4 border-purple-500\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-gray-500 text-sm font-medium\",\n                      children: \"Data Quality\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 863,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-3xl font-bold text-purple-600\",\n                      children: [analytics.performanceMetrics.dataQuality.completeness, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 864,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-400 mt-1\",\n                      children: \"Data completeness\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 865,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 862,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 846,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-xl shadow-md p-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-bold text-[#0077B6] mb-4\",\n                      children: \"System Performance\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 872,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-between items-center mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-gray-600\",\n                            children: \"Response Time\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 876,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"font-bold text-green-600\",\n                            children: [analytics.performanceMetrics.responseTime, \"ms\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 877,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 875,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-full bg-gray-200 rounded-full h-2\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"bg-green-500 h-2 rounded-full\",\n                            style: {\n                              width: `${Math.min((300 - analytics.performanceMetrics.responseTime) / 300 * 100, 100)}%`\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 880,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 879,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 874,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-between items-center mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-gray-600\",\n                            children: \"System Uptime\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 888,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"font-bold text-blue-600\",\n                            children: [analytics.performanceMetrics.uptime, \"%\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 889,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 887,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-full bg-gray-200 rounded-full h-2\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"bg-blue-500 h-2 rounded-full\",\n                            style: {\n                              width: `${analytics.performanceMetrics.uptime}%`\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 892,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 891,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 886,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex justify-between items-center mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-gray-600\",\n                            children: \"Error Rate\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 900,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"font-bold text-red-600\",\n                            children: [analytics.performanceMetrics.errorRate, \"%\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 901,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 899,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-full bg-gray-200 rounded-full h-2\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"bg-red-500 h-2 rounded-full\",\n                            style: {\n                              width: `${analytics.performanceMetrics.errorRate * 20}%`\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 904,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 903,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 898,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 873,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 871,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-xl shadow-md p-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-bold text-[#0077B6] mb-4\",\n                      children: \"Data Quality Metrics\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 914,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-6\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"relative inline-flex items-center justify-center w-24 h-24\",\n                          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                            className: \"w-24 h-24 transform -rotate-90\",\n                            viewBox: \"0 0 36 36\",\n                            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                              className: \"text-gray-300\",\n                              stroke: \"currentColor\",\n                              strokeWidth: \"3\",\n                              fill: \"none\",\n                              d: \"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 919,\n                              columnNumber: 31\n                            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                              className: \"text-[#20B2AA]\",\n                              stroke: \"currentColor\",\n                              strokeWidth: \"3\",\n                              strokeDasharray: `${analytics.performanceMetrics.dataQuality.completeness}, 100`,\n                              strokeLinecap: \"round\",\n                              fill: \"none\",\n                              d: \"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 926,\n                              columnNumber: 31\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 918,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"text-xl font-bold text-[#0077B6]\",\n                              children: [analytics.performanceMetrics.dataQuality.completeness, \"%\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 937,\n                              columnNumber: 31\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 936,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 917,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm text-gray-600 mt-2\",\n                          children: \"Data Completeness\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 940,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 916,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-2xl font-bold text-[#0077B6]\",\n                          children: [analytics.performanceMetrics.dataQuality.accuracy, \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 943,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm text-gray-600\",\n                          children: \"Data Accuracy\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 944,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 942,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 915,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 913,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 870,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white rounded-xl shadow-md p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-bold text-[#0077B6] mb-4\",\n                    children: \"Performance Recommendations\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 952,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-4 bg-green-50 rounded-lg border border-green-200\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-green-800 mb-2\",\n                        children: \"\\u2705 Good Performance\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 955,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                        className: \"text-sm text-green-700 space-y-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [\"\\u2022 System uptime is excellent (\", analytics.performanceMetrics.uptime, \"%)\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 957,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: \"\\u2022 Response times are within acceptable range\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 958,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: \"\\u2022 Data quality metrics are strong\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 959,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 956,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 954,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-4 bg-yellow-50 rounded-lg border border-yellow-200\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-yellow-800 mb-2\",\n                        children: \"\\u26A0\\uFE0F Areas for Improvement\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 963,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                        className: \"text-sm text-yellow-700 space-y-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                          children: \"\\u2022 Monitor error rates closely\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 965,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: \"\\u2022 Consider optimizing database queries\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 966,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: \"\\u2022 Implement data validation improvements\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 967,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 964,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 962,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 953,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 951,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 844,\n                columnNumber: 17\n              }, this), activeTab === 'activity' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white rounded-xl shadow-md p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-gray-800 mb-4\",\n                    children: \"Recent Activity\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 980,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"overflow-hidden\",\n                    children: /*#__PURE__*/_jsxDEV(\"table\", {\n                      className: \"min-w-full divide-y divide-gray-200\",\n                      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                        className: \"bg-gray-50\",\n                        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                            children: \"Action\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 985,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                            children: \"User\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 986,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                            children: \"Date\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 987,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 984,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 983,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                        className: \"bg-white divide-y divide-gray-200\",\n                        children: analytics.recentActivity.map((activity, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                          className: \"hover:bg-gray-50\",\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                            children: activity.action\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 993,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: activity.user\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 994,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: new Date(activity.date).toLocaleString()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 995,\n                            columnNumber: 31\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 992,\n                          columnNumber: 29\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 990,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 982,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 981,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 979,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 977,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 329,\n    columnNumber: 5\n  }, this);\n};\n_s(Analytics, \"Mp3h0HidgCmRPJ9QYwn00T2QASM=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Analytics;\nexport default Analytics;\nvar _c;\n$RefreshReg$(_c, \"Analytics\");", "map": {"version": 3, "names": ["useState", "useEffect", "useNavigate", "axios", "motion", "Bar", "Pie", "Line", "Doughnut", "Chart", "ChartJS", "CategoryScale", "LinearScale", "BarElement", "ArcElement", "LineElement", "PointElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "<PERSON><PERSON><PERSON>", "Loader", "useAuth", "SuperAdminSidebar", "FaChartBar", "FaUserFriends", "FaCalendarAlt", "FaHospital", "FaUserInjured", "FaClipboardList", "FaCogs", "FaShieldAlt", "FaUsers", "FaGraduationCap", "jsxDEV", "_jsxDEV", "register", "Analytics", "_s", "_analytics$appointmen", "_analytics$appointmen2", "_analytics$appointmen3", "sidebarOpen", "setSidebarOpen", "analytics", "setAnalytics", "counts", "totalStudents", "totalSupervisors", "totalAdmins", "totalAssistants", "totalDentists", "totalUniversities", "totalPatients", "totalAppointments", "totalAccounts", "growth", "studentGrowth", "<PERSON><PERSON><PERSON><PERSON>", "admin<PERSON><PERSON>th", "<PERSON><PERSON><PERSON><PERSON>", "dentist<PERSON><PERSON><PERSON>", "universityGrowth", "appointmentStats", "statusCounts", "appointmentsByMonth", "universityDistribution", "patientDemographics", "genderDistribution", "ageDistribution", "procedureTypes", "recentActivity", "businessMetrics", "activeUsers", "total", "byRole", "universityAdoption", "active", "adoptionRate", "growthRate", "monthly", "quarterly", "retentionMetrics", "userRetention", "universityRetention", "systemUsage", "appointmentMetrics", "completionRate", "cancellationRate", "patientMetrics", "averagePatientsPerStudent", "treatmentCompletionRate", "featureUsage", "mostUsedFeatures", "leastUsedFeatures", "performanceMetrics", "responseTime", "uptime", "errorRate", "dataQuality", "completeness", "accuracy", "loading", "setLoading", "error", "setError", "timeRange", "setTimeRange", "activeTab", "setActiveTab", "chartType", "setChartType", "accounts", "appointments", "universities", "gender", "age", "procedures", "navigate", "user", "token", "fetchAnalytics", "config", "headers", "Authorization", "params", "response", "get", "process", "env", "REACT_APP_API_URL", "data", "err", "_err$response", "_err$response2", "_err$response3", "_err$response3$data", "_err$response4", "console", "message", "errorMessage", "status", "item", "hidden", "opacity", "y", "show", "transition", "duration", "toggleChartType", "chartName", "prev", "types", "accountsData", "labels", "datasets", "label", "backgroundColor", "borderColor", "borderWidth", "appointmentsData", "map", "month", "year", "count", "tension", "universityData", "uni", "universityName", "studentCount", "genderData", "ageData", "ageGroup", "procedureData", "procedure", "<PERSON><PERSON><PERSON>", "type", "height", "chartProps", "options", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "isOpen", "setIsOpen", "toggleSidebar", "onClick", "div", "variants", "htmlFor", "id", "value", "onChange", "e", "target", "style", "slice", "activity", "index", "action", "Date", "date", "toLocaleString", "Math", "round", "Object", "entries", "role", "width", "feature", "usage", "universityId", "completed", "pending", "cancelled", "min", "viewBox", "stroke", "strokeWidth", "fill", "d", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeLinecap", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/superadmin/Analytics.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { motion } from 'framer-motion';\nimport { Bar, Pie, Line, Doughnut } from 'react-chartjs-2';\nimport { Chart as ChartJS, CategoryScale, LinearScale, BarElement, ArcElement, LineElement, PointElement, Title, Tooltip, Legend } from 'chart.js';\nimport Navbar from '../student/Navbar';\nimport Loader from '../components/Loader';\nimport { useAuth } from '../context/AuthContext';\nimport SuperAdminSidebar from './SuperAdminSidebar';\nimport { FaChartBar, FaUserFriends, FaCalendarAlt, FaHospital, FaUserInjured, FaClipboardList, FaCogs, FaShieldAlt, FaUsers, FaGraduationCap } from 'react-icons/fa';\n\n// Register ChartJS components\nChartJS.register(CategoryScale, LinearScale, BarElement, ArcElement, LineElement, PointElement, Title, Tooltip, Legend);\n\nconst Analytics = () => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [analytics, setAnalytics] = useState({\n    counts: {\n      totalStudents: 0,\n      totalSupervisors: 0,\n      totalAdmins: 0,\n      totalAssistants: 0,\n      totalDentists: 0,\n      totalUniversities: 0,\n      totalPatients: 0,\n      totalAppointments: 0,\n      totalAccounts: 0\n    },\n    growth: {\n      studentGrowth: [],\n      supervisorGrowth: [],\n      adminGrowth: [],\n      assistantGrowth: [],\n      dentistGrowth: [],\n      universityGrowth: []\n    },\n    appointmentStats: {\n      statusCounts: {},\n      appointmentsByMonth: []\n    },\n    universityDistribution: [],\n    patientDemographics: {\n      genderDistribution: [],\n      ageDistribution: []\n    },\n    procedureTypes: [],\n    recentActivity: [],\n    businessMetrics: {\n      activeUsers: { total: 0, byRole: {} },\n      universityAdoption: { total: 0, active: 0, adoptionRate: 0 },\n      growthRate: { monthly: 0, quarterly: 0 },\n      retentionMetrics: { userRetention: 0, universityRetention: 0 }\n    },\n    systemUsage: {\n      appointmentMetrics: { completionRate: 0, cancellationRate: 0 },\n      patientMetrics: { averagePatientsPerStudent: 0, treatmentCompletionRate: 0 },\n      featureUsage: { mostUsedFeatures: [], leastUsedFeatures: [] }\n    },\n    performanceMetrics: {\n      responseTime: 0,\n      uptime: 0,\n      errorRate: 0,\n      dataQuality: { completeness: 0, accuracy: 0 }\n    }\n  });\n\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [timeRange, setTimeRange] = useState('6months'); // '1month', '3months', '6months', '1year'\n  const [activeTab, setActiveTab] = useState('overview'); // 'overview', 'business', 'accounts', 'universities', 'patients', 'appointments', 'performance', 'activity'\n  const [chartType, setChartType] = useState({\n    accounts: 'bar',\n    appointments: 'line',\n    universities: 'bar',\n    gender: 'pie',\n    age: 'bar',\n    procedures: 'bar'\n  });\n\n  const navigate = useNavigate();\n  const { user, token } = useAuth();\n\n  useEffect(() => {\n    const fetchAnalytics = async () => {\n      if (!user || !token) {\n        setError('Please log in to view analytics.');\n        setLoading(false);\n        return;\n      }\n\n      setLoading(true);\n      try {\n        const config = {\n          headers: { Authorization: `Bearer ${token}` },\n          params: { timeRange }\n        };\n        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/analytics`, config);\n        setAnalytics(response.data);\n      } catch (err) {\n        console.error('Fetch error:', err.response?.data || err.message);\n        const errorMessage = err.response?.status === 401\n          ? 'Unauthorized. Please log in again.'\n          : err.response?.data?.message || 'Failed to load analytics';\n        setError(errorMessage);\n        if (err.response?.status === 401) {\n          navigate('/login');\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchAnalytics();\n  }, [user, token, navigate, timeRange]);\n\n  // Animation variants with reduced motion\n  const item = {\n    hidden: { opacity: 0, y: 5 },\n    show: { opacity: 1, y: 0, transition: { duration: 0.15 } },\n  };\n\n  // Toggle chart type\n  const toggleChartType = (chartName) => {\n    setChartType(prev => {\n      const types = {\n        'bar': 'line',\n        'line': 'pie',\n        'pie': 'doughnut',\n        'doughnut': 'bar'\n      };\n      return { ...prev, [chartName]: types[prev[chartName]] };\n    });\n  };\n\n  // Chart Data\n  const accountsData = {\n    labels: ['Students', 'Supervisors', 'Admins', 'Assistants', 'Dentists'],\n    datasets: [\n      {\n        label: 'Number of Accounts',\n        data: [\n          analytics.counts.totalStudents,\n          analytics.counts.totalSupervisors,\n          analytics.counts.totalAdmins,\n          analytics.counts.totalAssistants,\n          analytics.counts.totalDentists\n        ],\n        backgroundColor: [\n          'rgba(54, 162, 235, 0.7)',\n          'rgba(255, 99, 132, 0.7)',\n          'rgba(255, 206, 86, 0.7)',\n          'rgba(75, 192, 192, 0.7)',\n          'rgba(153, 102, 255, 0.7)'\n        ],\n        borderColor: [\n          'rgba(54, 162, 235, 1)',\n          'rgba(255, 99, 132, 1)',\n          'rgba(255, 206, 86, 1)',\n          'rgba(75, 192, 192, 1)',\n          'rgba(153, 102, 255, 1)'\n        ],\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const appointmentsData = {\n    labels: analytics.appointmentStats.appointmentsByMonth.map(data => `${data.month} ${data.year}`) || ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],\n    datasets: [\n      {\n        label: 'Appointments',\n        data: analytics.appointmentStats.appointmentsByMonth.map(data => data.count) || [10, 15, 20, 25, 30, 35],\n        backgroundColor: 'rgba(75, 192, 192, 0.5)',\n        borderColor: 'rgba(75, 192, 192, 1)',\n        borderWidth: 1,\n        tension: 0.4\n      },\n    ],\n  };\n\n  // University distribution data\n  const universityData = {\n    labels: analytics.universityDistribution.map(uni => uni.universityName) || ['University A', 'University B', 'University C'],\n    datasets: [\n      {\n        label: 'Students per University',\n        data: analytics.universityDistribution.map(uni => uni.studentCount) || [50, 30, 20],\n        backgroundColor: [\n          'rgba(255, 99, 132, 0.7)',\n          'rgba(54, 162, 235, 0.7)',\n          'rgba(255, 206, 86, 0.7)',\n          'rgba(75, 192, 192, 0.7)',\n          'rgba(153, 102, 255, 0.7)',\n          'rgba(255, 159, 64, 0.7)',\n        ],\n        borderColor: [\n          'rgba(255, 99, 132, 1)',\n          'rgba(54, 162, 235, 1)',\n          'rgba(255, 206, 86, 1)',\n          'rgba(75, 192, 192, 1)',\n          'rgba(153, 102, 255, 1)',\n          'rgba(255, 159, 64, 1)',\n        ],\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  // Gender distribution data\n  const genderData = {\n    labels: analytics.patientDemographics.genderDistribution.map(item => item.gender) || ['Male', 'Female', 'Other'],\n    datasets: [\n      {\n        label: 'Gender Distribution',\n        data: analytics.patientDemographics.genderDistribution.map(item => item.count) || [45, 55, 5],\n        backgroundColor: [\n          'rgba(54, 162, 235, 0.7)',\n          'rgba(255, 99, 132, 0.7)',\n          'rgba(255, 206, 86, 0.7)',\n        ],\n        borderColor: [\n          'rgba(54, 162, 235, 1)',\n          'rgba(255, 99, 132, 1)',\n          'rgba(255, 206, 86, 1)',\n        ],\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  // Age distribution data\n  const ageData = {\n    labels: analytics.patientDemographics.ageDistribution.map(item => item.ageGroup) || ['0-18', '19-30', '31-45', '46-60', '60+'],\n    datasets: [\n      {\n        label: 'Age Distribution',\n        data: analytics.patientDemographics.ageDistribution.map(item => item.count) || [15, 25, 30, 20, 10],\n        backgroundColor: 'rgba(153, 102, 255, 0.7)',\n        borderColor: 'rgba(153, 102, 255, 1)',\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  // Procedure types data\n  const procedureData = {\n    labels: analytics.procedureTypes.map(item => item.procedure) || ['Filling', 'Extraction', 'Root Canal', 'Cleaning', 'Crown'],\n    datasets: [\n      {\n        label: 'Procedure Types',\n        data: analytics.procedureTypes.map(item => item.count) || [40, 25, 15, 35, 20],\n        backgroundColor: [\n          'rgba(255, 99, 132, 0.7)',\n          'rgba(54, 162, 235, 0.7)',\n          'rgba(255, 206, 86, 0.7)',\n          'rgba(75, 192, 192, 0.7)',\n          'rgba(153, 102, 255, 0.7)',\n        ],\n        borderColor: [\n          'rgba(255, 99, 132, 1)',\n          'rgba(54, 162, 235, 1)',\n          'rgba(255, 206, 86, 1)',\n          'rgba(75, 192, 192, 1)',\n          'rgba(153, 102, 255, 1)',\n        ],\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  // Render the appropriate chart based on type\n  const renderChart = (data, type, height = '300px') => {\n    const chartProps = {\n      data,\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n          legend: {\n            position: 'top',\n          },\n        },\n      },\n      height\n    };\n\n    switch (type) {\n      case 'bar':\n        return <Bar {...chartProps} />;\n      case 'line':\n        return <Line {...chartProps} />;\n      case 'pie':\n        return <Pie {...chartProps} />;\n      case 'doughnut':\n        return <Doughnut {...chartProps} />;\n      default:\n        return <Bar {...chartProps} />;\n    }\n  };\n\n  if (loading) return <Loader />;\n\n  if (error) {\n    return (\n      <div className=\"flex h-screen bg-gray-50\">\n        <SuperAdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n        <div className=\"flex-1 flex flex-col overflow-hidden\">\n          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\n          <div className=\"flex-1 overflow-y-auto p-6 bg-gradient-to-br from-blue-50 to-white\">\n            <div className=\"max-w-7xl mx-auto\">\n              <div className=\"bg-white rounded-lg shadow-md p-6 text-center\">\n                <h2 className=\"text-2xl font-bold text-red-600 mb-4\">Error</h2>\n                <p className=\"text-gray-700\">{error}</p>\n                <button\n                  onClick={() => navigate('/login')}\n                  className=\"mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n                >\n                  Go to Login\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\">\n      <SuperAdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\n\n        <div className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\">\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"grid grid-cols-1 gap-6\">\n            {/* Header */}\n            <motion.div variants={item} className=\"bg-gradient-to-r from-[#0077B6] to-[#20B2AA] rounded-xl shadow-lg p-6 text-white\">\n              <div className=\"flex flex-col md:flex-row md:justify-between md:items-center\">\n                <div>\n                  <h1 className=\"text-3xl font-bold mb-2\">Analytics Dashboard</h1>\n                  <p className=\"opacity-80\">Comprehensive insights into your dental management system</p>\n                </div>\n                <div className=\"mt-4 md:mt-0\">\n                  <label htmlFor=\"timeRange\" className=\"block text-sm font-medium text-white mb-2\">Time Range</label>\n                  <select\n                    id=\"timeRange\"\n                    value={timeRange}\n                    onChange={(e) => setTimeRange(e.target.value)}\n                    className=\"bg-white bg-opacity-20 text-white border border-white border-opacity-30 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50\"\n                  >\n                    <option value=\"1month\">Last Month</option>\n                    <option value=\"3months\">Last 3 Months</option>\n                    <option value=\"6months\">Last 6 Months</option>\n                    <option value=\"1year\">Last Year</option>\n                    <option value=\"all\">All Time</option>\n                  </select>\n                </div>\n              </div>\n            </motion.div>\n\n            {/* Tab Navigation */}\n            <motion.div variants={item} className=\"bg-white rounded-xl shadow-md overflow-hidden\">\n              <div className=\"flex overflow-x-auto\">\n                <button\n                  onClick={() => setActiveTab('overview')}\n                  className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${\n                    activeTab === 'overview'\n                      ? 'border-[#0077B6] text-[#0077B6]'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <FaChartBar className=\"mr-2\" />\n                  Overview\n                </button>\n                <button\n                  onClick={() => setActiveTab('business')}\n                  className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${\n                    activeTab === 'business'\n                      ? 'border-[#0077B6] text-[#0077B6]'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <FaChartBar className=\"mr-2\" />\n                  Business\n                </button>\n                <button\n                  onClick={() => setActiveTab('accounts')}\n                  className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${\n                    activeTab === 'accounts'\n                      ? 'border-[#0077B6] text-[#0077B6]'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <FaUserFriends className=\"mr-2\" />\n                  Accounts\n                </button>\n                <button\n                  onClick={() => setActiveTab('universities')}\n                  className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${\n                    activeTab === 'universities'\n                      ? 'border-[#0077B6] text-[#0077B6]'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <FaHospital className=\"mr-2\" />\n                  Universities\n                </button>\n                <button\n                  onClick={() => setActiveTab('patients')}\n                  className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${\n                    activeTab === 'patients'\n                      ? 'border-[#0077B6] text-[#0077B6]'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <FaUserInjured className=\"mr-2\" />\n                  Patients\n                </button>\n                <button\n                  onClick={() => setActiveTab('appointments')}\n                  className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${\n                    activeTab === 'appointments'\n                      ? 'border-[#0077B6] text-[#0077B6]'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <FaCalendarAlt className=\"mr-2\" />\n                  Appointments\n                </button>\n                <button\n                  onClick={() => setActiveTab('performance')}\n                  className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${\n                    activeTab === 'performance'\n                      ? 'border-[#0077B6] text-[#0077B6]'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <FaShieldAlt className=\"mr-2\" />\n                  Performance\n                </button>\n                <button\n                  onClick={() => setActiveTab('activity')}\n                  className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${\n                    activeTab === 'activity'\n                      ? 'border-[#0077B6] text-[#0077B6]'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <FaClipboardList className=\"mr-2\" />\n                  Activity\n                </button>\n              </div>\n            </motion.div>\n\n            {/* Tab Content */}\n            <div className=\"mt-6\">\n              {/* Overview Tab */}\n              {activeTab === 'overview' && (\n                <div>\n                  {/* Summary Cards */}\n                  <motion.div variants={item} className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\n                    <div className=\"bg-white rounded-xl shadow-md p-5 border-l-4 border-[#0077B6]\">\n                      <h3 className=\"text-gray-500 text-sm font-medium\">Total Accounts</h3>\n                      <p className=\"text-3xl font-bold text-[#0077B6]\">{analytics.counts.totalAccounts}</p>\n                    </div>\n                    <div className=\"bg-white rounded-xl shadow-md p-5 border-l-4 border-[#20B2AA]\">\n                      <h3 className=\"text-gray-500 text-sm font-medium\">Total Universities</h3>\n                      <p className=\"text-3xl font-bold text-[#20B2AA]\">{analytics.counts.totalUniversities}</p>\n                    </div>\n                    <div className=\"bg-white rounded-xl shadow-md p-5 border-l-4 border-[#0077B6]\">\n                      <h3 className=\"text-gray-500 text-sm font-medium\">Total Patients</h3>\n                      <p className=\"text-3xl font-bold text-[#0077B6]\">{analytics.counts.totalPatients}</p>\n                    </div>\n                    <div className=\"bg-white rounded-xl shadow-md p-5 border-l-4 border-[#20B2AA]\">\n                      <h3 className=\"text-gray-500 text-sm font-medium\">Total Appointments</h3>\n                      <p className=\"text-3xl font-bold text-[#20B2AA]\">{analytics.counts.totalAppointments}</p>\n                    </div>\n                  </motion.div>\n\n                  {/* Main Charts - Two Side by Side */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                    {/* Account Distribution */}\n                    <motion.div variants={item} className=\"bg-white rounded-xl shadow-md p-6\">\n                      <div className=\"flex justify-between items-center mb-4\">\n                        <h2 className=\"text-xl font-bold text-[#0077B6]\">Account Distribution</h2>\n                        <button\n                          onClick={() => toggleChartType('accounts')}\n                          className=\"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-colors text-sm\"\n                        >\n                          Change Chart Type\n                        </button>\n                      </div>\n                      <div style={{ height: '300px' }}>\n                        {renderChart(accountsData, chartType.accounts)}\n                      </div>\n                    </motion.div>\n\n                    {/* Appointments Over Time */}\n                    <motion.div variants={item} className=\"bg-white rounded-xl shadow-md p-6\">\n                      <div className=\"flex justify-between items-center mb-4\">\n                        <h2 className=\"text-xl font-bold text-[#0077B6]\">Appointments Over Time</h2>\n                        <button\n                          onClick={() => toggleChartType('appointments')}\n                          className=\"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-colors text-sm\"\n                        >\n                          Change Chart Type\n                        </button>\n                      </div>\n                      <div style={{ height: '300px' }}>\n                        {renderChart(appointmentsData, chartType.appointments)}\n                      </div>\n                    </motion.div>\n                  </div>\n\n                  {/* Recent Activity */}\n                  <motion.div variants={item} className=\"bg-white rounded-xl shadow-md p-6\">\n                    <h2 className=\"text-xl font-bold text-[#0077B6] mb-4\">Recent Activity</h2>\n                    <div className=\"overflow-hidden\">\n                      <table className=\"min-w-full divide-y divide-gray-200\">\n                        <thead className=\"bg-gray-50\">\n                          <tr>\n                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Action</th>\n                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">User</th>\n                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Date</th>\n                          </tr>\n                        </thead>\n                        <tbody className=\"bg-white divide-y divide-gray-200\">\n                          {analytics.recentActivity.slice(0, 5).map((activity, index) => (\n                            <tr key={index} className=\"hover:bg-gray-50\">\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">{activity.action}</td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{activity.user}</td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                                {new Date(activity.date).toLocaleString()}\n                              </td>\n                            </tr>\n                          ))}\n                        </tbody>\n                      </table>\n                    </div>\n                  </motion.div>\n                </div>\n              )}\n\n              {/* Business Tab */}\n              {activeTab === 'business' && (\n                <div>\n                  {/* Key Business Metrics */}\n                  <motion.div variants={item} className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\n                    <div className=\"bg-white rounded-xl shadow-md p-5 border-l-4 border-green-500\">\n                      <h3 className=\"text-gray-500 text-sm font-medium\">Active Users</h3>\n                      <p className=\"text-3xl font-bold text-green-600\">{analytics.businessMetrics.activeUsers.total}</p>\n                      <p className=\"text-xs text-gray-400 mt-1\">In selected period</p>\n                    </div>\n                    <div className=\"bg-white rounded-xl shadow-md p-5 border-l-4 border-blue-500\">\n                      <h3 className=\"text-gray-500 text-sm font-medium\">University Adoption</h3>\n                      <p className=\"text-3xl font-bold text-blue-600\">{Math.round(analytics.businessMetrics.universityAdoption.adoptionRate)}%</p>\n                      <p className=\"text-xs text-gray-400 mt-1\">{analytics.businessMetrics.universityAdoption.active} of {analytics.businessMetrics.universityAdoption.total} universities</p>\n                    </div>\n                    <div className=\"bg-white rounded-xl shadow-md p-5 border-l-4 border-purple-500\">\n                      <h3 className=\"text-gray-500 text-sm font-medium\">User Retention</h3>\n                      <p className=\"text-3xl font-bold text-purple-600\">{analytics.businessMetrics.retentionMetrics.userRetention}%</p>\n                      <p className=\"text-xs text-gray-400 mt-1\">Active users</p>\n                    </div>\n                    <div className=\"bg-white rounded-xl shadow-md p-5 border-l-4 border-orange-500\">\n                      <h3 className=\"text-gray-500 text-sm font-medium\">Monthly Growth</h3>\n                      <p className=\"text-3xl font-bold text-orange-600\">{analytics.businessMetrics.growthRate.monthly}</p>\n                      <p className=\"text-xs text-gray-400 mt-1\">New users this month</p>\n                    </div>\n                  </motion.div>\n\n                  {/* Active Users by Role */}\n                  <motion.div variants={item} className=\"bg-white rounded-xl shadow-md p-6 mb-6\">\n                    <h2 className=\"text-xl font-bold text-[#0077B6] mb-4\">Active Users by Role</h2>\n                    <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\">\n                      {Object.entries(analytics.businessMetrics.activeUsers.byRole).map(([role, count]) => (\n                        <div key={role} className=\"bg-gray-50 rounded-lg p-4 text-center\">\n                          <div className=\"text-2xl font-bold text-[#0077B6]\">{count}</div>\n                          <div className=\"text-sm text-gray-600 capitalize\">{role}s</div>\n                        </div>\n                      ))}\n                    </div>\n                  </motion.div>\n\n                  {/* System Usage Metrics */}\n                  <motion.div variants={item} className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                    <div className=\"bg-white rounded-xl shadow-md p-6\">\n                      <h3 className=\"text-lg font-bold text-[#0077B6] mb-4\">Appointment Efficiency</h3>\n                      <div className=\"space-y-4\">\n                        <div className=\"flex justify-between items-center\">\n                          <span className=\"text-gray-600\">Completion Rate</span>\n                          <span className=\"font-bold text-green-600\">{Math.round(analytics.systemUsage.appointmentMetrics.completionRate)}%</span>\n                        </div>\n                        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                          <div\n                            className=\"bg-green-500 h-2 rounded-full\"\n                            style={{ width: `${analytics.systemUsage.appointmentMetrics.completionRate}%` }}\n                          ></div>\n                        </div>\n                        <div className=\"flex justify-between items-center\">\n                          <span className=\"text-gray-600\">Cancellation Rate</span>\n                          <span className=\"font-bold text-red-600\">{Math.round(analytics.systemUsage.appointmentMetrics.cancellationRate)}%</span>\n                        </div>\n                        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                          <div\n                            className=\"bg-red-500 h-2 rounded-full\"\n                            style={{ width: `${analytics.systemUsage.appointmentMetrics.cancellationRate}%` }}\n                          ></div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"bg-white rounded-xl shadow-md p-6\">\n                      <h3 className=\"text-lg font-bold text-[#0077B6] mb-4\">Patient Management</h3>\n                      <div className=\"space-y-4\">\n                        <div className=\"text-center\">\n                          <div className=\"text-3xl font-bold text-[#20B2AA]\">{analytics.systemUsage.patientMetrics.averagePatientsPerStudent}</div>\n                          <div className=\"text-sm text-gray-600\">Avg Patients per Student</div>\n                        </div>\n                        <div className=\"text-center\">\n                          <div className=\"text-3xl font-bold text-[#0077B6]\">{analytics.systemUsage.patientMetrics.treatmentCompletionRate}%</div>\n                          <div className=\"text-sm text-gray-600\">Treatment Completion Rate</div>\n                        </div>\n                      </div>\n                    </div>\n                  </motion.div>\n\n                  {/* Feature Usage */}\n                  <motion.div variants={item} className=\"bg-white rounded-xl shadow-md p-6\">\n                    <h3 className=\"text-lg font-bold text-[#0077B6] mb-4\">Feature Usage Statistics</h3>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                      <div>\n                        <h4 className=\"font-semibold text-gray-700 mb-3\">Most Used Features</h4>\n                        <div className=\"space-y-2\">\n                          {analytics.systemUsage.featureUsage.mostUsedFeatures.slice(0, 5).map((feature, index) => (\n                            <div key={index} className=\"flex justify-between items-center p-2 bg-green-50 rounded\">\n                              <span className=\"text-sm text-gray-700\">{feature.feature}</span>\n                              <span className=\"font-bold text-green-600\">{feature.usage}</span>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                      <div>\n                        <h4 className=\"font-semibold text-gray-700 mb-3\">Least Used Features</h4>\n                        <div className=\"space-y-2\">\n                          {analytics.systemUsage.featureUsage.leastUsedFeatures.slice(0, 5).map((feature, index) => (\n                            <div key={index} className=\"flex justify-between items-center p-2 bg-red-50 rounded\">\n                              <span className=\"text-sm text-gray-700\">{feature.feature}</span>\n                              <span className=\"font-bold text-red-600\">{feature.usage}</span>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  </motion.div>\n                </div>\n              )}\n\n              {/* Accounts Tab */}\n              {activeTab === 'accounts' && (\n                <div>\n                  {/* Account Distribution Chart */}\n                  <motion.div variants={item} className=\"bg-white rounded-xl shadow-md p-6 mb-6\">\n                    <div className=\"flex justify-between items-center mb-4\">\n                      <h2 className=\"text-xl font-bold text-gray-800\">Account Distribution</h2>\n                      <button\n                        onClick={() => toggleChartType('accounts')}\n                        className=\"px-3 py-1 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors text-sm\"\n                      >\n                        Change Chart Type\n                      </button>\n                    </div>\n                    <div style={{ height: '400px' }}>\n                      {renderChart(accountsData, chartType.accounts)}\n                    </div>\n                  </motion.div>\n\n                  {/* Account Growth Over Time */}\n                  <motion.div variants={item} className=\"bg-white rounded-xl shadow-md p-6\">\n                    <h2 className=\"text-xl font-bold text-gray-800 mb-4\">Account Growth Over Time</h2>\n                    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                      <div className=\"bg-blue-50 rounded-lg p-4 border border-blue-200\">\n                        <h3 className=\"text-sm font-medium text-blue-800\">Students</h3>\n                        <p className=\"text-2xl font-bold text-blue-600\">{analytics.counts.totalStudents}</p>\n                      </div>\n                      <div className=\"bg-green-50 rounded-lg p-4 border border-green-200\">\n                        <h3 className=\"text-sm font-medium text-green-800\">Supervisors</h3>\n                        <p className=\"text-2xl font-bold text-green-600\">{analytics.counts.totalSupervisors}</p>\n                      </div>\n                      <div className=\"bg-purple-50 rounded-lg p-4 border border-purple-200\">\n                        <h3 className=\"text-sm font-medium text-purple-800\">Admins</h3>\n                        <p className=\"text-2xl font-bold text-purple-600\">{analytics.counts.totalAdmins}</p>\n                      </div>\n                    </div>\n                  </motion.div>\n                </div>\n              )}\n\n              {/* Universities Tab */}\n              {activeTab === 'universities' && (\n                <div>\n                  {/* University Distribution */}\n                  <motion.div variants={item} className=\"bg-white rounded-xl shadow-md p-6 mb-6\">\n                    <div className=\"flex justify-between items-center mb-4\">\n                      <h2 className=\"text-xl font-bold text-gray-800\">University Distribution</h2>\n                      <button\n                        onClick={() => toggleChartType('universities')}\n                        className=\"px-3 py-1 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors text-sm\"\n                      >\n                        Change Chart Type\n                      </button>\n                    </div>\n                    <div style={{ height: '400px' }}>\n                      {renderChart(universityData, chartType.universities)}\n                    </div>\n                  </motion.div>\n\n                  {/* University Stats */}\n                  <motion.div variants={item} className=\"bg-white rounded-xl shadow-md p-6\">\n                    <h2 className=\"text-xl font-bold text-gray-800 mb-4\">University Statistics</h2>\n                    <div className=\"overflow-hidden\">\n                      <table className=\"min-w-full divide-y divide-gray-200\">\n                        <thead className=\"bg-gray-50\">\n                          <tr>\n                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">University</th>\n                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Students</th>\n                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">ID</th>\n                          </tr>\n                        </thead>\n                        <tbody className=\"bg-white divide-y divide-gray-200\">\n                          {analytics.universityDistribution.map((uni, index) => (\n                            <tr key={index} className=\"hover:bg-gray-50\">\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">{uni.universityName}</td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{uni.studentCount}</td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{uni.universityId}</td>\n                            </tr>\n                          ))}\n                        </tbody>\n                      </table>\n                    </div>\n                  </motion.div>\n                </div>\n              )}\n\n              {/* Patients Tab */}\n              {activeTab === 'patients' && (\n                <div>\n                  {/* Patient Demographics - Two Charts Side by Side */}\n                  <motion.div variants={item} className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                    {/* Gender Distribution */}\n                    <div className=\"bg-white rounded-xl shadow-md p-6\">\n                      <div className=\"flex justify-between items-center mb-4\">\n                        <h2 className=\"text-xl font-bold text-gray-800\">Gender Distribution</h2>\n                        <button\n                          onClick={() => toggleChartType('gender')}\n                          className=\"px-3 py-1 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors text-sm\"\n                        >\n                          Change Chart Type\n                        </button>\n                      </div>\n                      <div style={{ height: '300px' }}>\n                        {renderChart(genderData, chartType.gender)}\n                      </div>\n                    </div>\n\n                    {/* Age Distribution */}\n                    <div className=\"bg-white rounded-xl shadow-md p-6\">\n                      <div className=\"flex justify-between items-center mb-4\">\n                        <h2 className=\"text-xl font-bold text-gray-800\">Age Distribution</h2>\n                        <button\n                          onClick={() => toggleChartType('age')}\n                          className=\"px-3 py-1 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors text-sm\"\n                        >\n                          Change Chart Type\n                        </button>\n                      </div>\n                      <div style={{ height: '300px' }}>\n                        {renderChart(ageData, chartType.age)}\n                      </div>\n                    </div>\n                  </motion.div>\n\n                  {/* Procedure Types */}\n                  <motion.div variants={item} className=\"bg-white rounded-xl shadow-md p-6\">\n                    <div className=\"flex justify-between items-center mb-4\">\n                      <h2 className=\"text-xl font-bold text-gray-800\">Procedure Types</h2>\n                      <button\n                        onClick={() => toggleChartType('procedures')}\n                        className=\"px-3 py-1 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors text-sm\"\n                      >\n                        Change Chart Type\n                      </button>\n                    </div>\n                    <div style={{ height: '300px' }}>\n                      {renderChart(procedureData, chartType.procedures)}\n                    </div>\n                  </motion.div>\n                </div>\n              )}\n\n              {/* Appointments Tab */}\n              {activeTab === 'appointments' && (\n                <div>\n                  {/* Appointments Over Time */}\n                  <motion.div variants={item} className=\"bg-white rounded-xl shadow-md p-6 mb-6\">\n                    <div className=\"flex justify-between items-center mb-4\">\n                      <h2 className=\"text-xl font-bold text-gray-800\">Appointments Over Time</h2>\n                      <button\n                        onClick={() => toggleChartType('appointments')}\n                        className=\"px-3 py-1 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors text-sm\"\n                      >\n                        Change Chart Type\n                      </button>\n                    </div>\n                    <div style={{ height: '400px' }}>\n                      {renderChart(appointmentsData, chartType.appointments)}\n                    </div>\n                  </motion.div>\n\n                  {/* Appointment Status */}\n                  <motion.div variants={item} className=\"bg-white rounded-xl shadow-md p-6\">\n                    <h2 className=\"text-xl font-bold text-gray-800 mb-4\">Appointment Status</h2>\n                    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                      <div className=\"bg-green-50 rounded-lg p-4 border border-green-200\">\n                        <h3 className=\"text-sm font-medium text-green-800\">Completed</h3>\n                        <p className=\"text-2xl font-bold text-green-600\">{analytics.appointmentStats.statusCounts?.completed || 0}</p>\n                      </div>\n                      <div className=\"bg-yellow-50 rounded-lg p-4 border border-yellow-200\">\n                        <h3 className=\"text-sm font-medium text-yellow-800\">Pending</h3>\n                        <p className=\"text-2xl font-bold text-yellow-600\">{analytics.appointmentStats.statusCounts?.pending || 0}</p>\n                      </div>\n                      <div className=\"bg-red-50 rounded-lg p-4 border border-red-200\">\n                        <h3 className=\"text-sm font-medium text-red-800\">Cancelled</h3>\n                        <p className=\"text-2xl font-bold text-red-600\">{analytics.appointmentStats.statusCounts?.cancelled || 0}</p>\n                      </div>\n                    </div>\n                  </motion.div>\n                </div>\n              )}\n\n              {/* Performance Tab */}\n              {activeTab === 'performance' && (\n                <div>\n                  {/* Performance Metrics */}\n                  <motion.div variants={item} className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\n                    <div className=\"bg-white rounded-xl shadow-md p-5 border-l-4 border-green-500\">\n                      <h3 className=\"text-gray-500 text-sm font-medium\">Response Time</h3>\n                      <p className=\"text-3xl font-bold text-green-600\">{analytics.performanceMetrics.responseTime}ms</p>\n                      <p className=\"text-xs text-gray-400 mt-1\">Average response time</p>\n                    </div>\n                    <div className=\"bg-white rounded-xl shadow-md p-5 border-l-4 border-blue-500\">\n                      <h3 className=\"text-gray-500 text-sm font-medium\">System Uptime</h3>\n                      <p className=\"text-3xl font-bold text-blue-600\">{analytics.performanceMetrics.uptime}%</p>\n                      <p className=\"text-xs text-gray-400 mt-1\">System availability</p>\n                    </div>\n                    <div className=\"bg-white rounded-xl shadow-md p-5 border-l-4 border-yellow-500\">\n                      <h3 className=\"text-gray-500 text-sm font-medium\">Error Rate</h3>\n                      <p className=\"text-3xl font-bold text-yellow-600\">{analytics.performanceMetrics.errorRate}%</p>\n                      <p className=\"text-xs text-gray-400 mt-1\">System error rate</p>\n                    </div>\n                    <div className=\"bg-white rounded-xl shadow-md p-5 border-l-4 border-purple-500\">\n                      <h3 className=\"text-gray-500 text-sm font-medium\">Data Quality</h3>\n                      <p className=\"text-3xl font-bold text-purple-600\">{analytics.performanceMetrics.dataQuality.completeness}%</p>\n                      <p className=\"text-xs text-gray-400 mt-1\">Data completeness</p>\n                    </div>\n                  </motion.div>\n\n                  {/* System Health */}\n                  <motion.div variants={item} className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                    <div className=\"bg-white rounded-xl shadow-md p-6\">\n                      <h3 className=\"text-lg font-bold text-[#0077B6] mb-4\">System Performance</h3>\n                      <div className=\"space-y-4\">\n                        <div>\n                          <div className=\"flex justify-between items-center mb-2\">\n                            <span className=\"text-gray-600\">Response Time</span>\n                            <span className=\"font-bold text-green-600\">{analytics.performanceMetrics.responseTime}ms</span>\n                          </div>\n                          <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                            <div\n                              className=\"bg-green-500 h-2 rounded-full\"\n                              style={{ width: `${Math.min((300 - analytics.performanceMetrics.responseTime) / 300 * 100, 100)}%` }}\n                            ></div>\n                          </div>\n                        </div>\n                        <div>\n                          <div className=\"flex justify-between items-center mb-2\">\n                            <span className=\"text-gray-600\">System Uptime</span>\n                            <span className=\"font-bold text-blue-600\">{analytics.performanceMetrics.uptime}%</span>\n                          </div>\n                          <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                            <div\n                              className=\"bg-blue-500 h-2 rounded-full\"\n                              style={{ width: `${analytics.performanceMetrics.uptime}%` }}\n                            ></div>\n                          </div>\n                        </div>\n                        <div>\n                          <div className=\"flex justify-between items-center mb-2\">\n                            <span className=\"text-gray-600\">Error Rate</span>\n                            <span className=\"font-bold text-red-600\">{analytics.performanceMetrics.errorRate}%</span>\n                          </div>\n                          <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                            <div\n                              className=\"bg-red-500 h-2 rounded-full\"\n                              style={{ width: `${analytics.performanceMetrics.errorRate * 20}%` }}\n                            ></div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"bg-white rounded-xl shadow-md p-6\">\n                      <h3 className=\"text-lg font-bold text-[#0077B6] mb-4\">Data Quality Metrics</h3>\n                      <div className=\"space-y-6\">\n                        <div className=\"text-center\">\n                          <div className=\"relative inline-flex items-center justify-center w-24 h-24\">\n                            <svg className=\"w-24 h-24 transform -rotate-90\" viewBox=\"0 0 36 36\">\n                              <path\n                                className=\"text-gray-300\"\n                                stroke=\"currentColor\"\n                                strokeWidth=\"3\"\n                                fill=\"none\"\n                                d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                              />\n                              <path\n                                className=\"text-[#20B2AA]\"\n                                stroke=\"currentColor\"\n                                strokeWidth=\"3\"\n                                strokeDasharray={`${analytics.performanceMetrics.dataQuality.completeness}, 100`}\n                                strokeLinecap=\"round\"\n                                fill=\"none\"\n                                d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                              />\n                            </svg>\n                            <div className=\"absolute inset-0 flex items-center justify-center\">\n                              <span className=\"text-xl font-bold text-[#0077B6]\">{analytics.performanceMetrics.dataQuality.completeness}%</span>\n                            </div>\n                          </div>\n                          <div className=\"text-sm text-gray-600 mt-2\">Data Completeness</div>\n                        </div>\n                        <div className=\"text-center\">\n                          <div className=\"text-2xl font-bold text-[#0077B6]\">{analytics.performanceMetrics.dataQuality.accuracy}%</div>\n                          <div className=\"text-sm text-gray-600\">Data Accuracy</div>\n                        </div>\n                      </div>\n                    </div>\n                  </motion.div>\n\n                  {/* Performance Recommendations */}\n                  <motion.div variants={item} className=\"bg-white rounded-xl shadow-md p-6\">\n                    <h3 className=\"text-lg font-bold text-[#0077B6] mb-4\">Performance Recommendations</h3>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      <div className=\"p-4 bg-green-50 rounded-lg border border-green-200\">\n                        <h4 className=\"font-semibold text-green-800 mb-2\">✅ Good Performance</h4>\n                        <ul className=\"text-sm text-green-700 space-y-1\">\n                          <li>• System uptime is excellent ({analytics.performanceMetrics.uptime}%)</li>\n                          <li>• Response times are within acceptable range</li>\n                          <li>• Data quality metrics are strong</li>\n                        </ul>\n                      </div>\n                      <div className=\"p-4 bg-yellow-50 rounded-lg border border-yellow-200\">\n                        <h4 className=\"font-semibold text-yellow-800 mb-2\">⚠️ Areas for Improvement</h4>\n                        <ul className=\"text-sm text-yellow-700 space-y-1\">\n                          <li>• Monitor error rates closely</li>\n                          <li>• Consider optimizing database queries</li>\n                          <li>• Implement data validation improvements</li>\n                        </ul>\n                      </div>\n                    </div>\n                  </motion.div>\n                </div>\n              )}\n\n              {/* Activity Tab */}\n              {activeTab === 'activity' && (\n                <div>\n                  {/* Recent Activity */}\n                  <motion.div variants={item} className=\"bg-white rounded-xl shadow-md p-6\">\n                    <h2 className=\"text-xl font-bold text-gray-800 mb-4\">Recent Activity</h2>\n                    <div className=\"overflow-hidden\">\n                      <table className=\"min-w-full divide-y divide-gray-200\">\n                        <thead className=\"bg-gray-50\">\n                          <tr>\n                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Action</th>\n                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">User</th>\n                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Date</th>\n                          </tr>\n                        </thead>\n                        <tbody className=\"bg-white divide-y divide-gray-200\">\n                          {analytics.recentActivity.map((activity, index) => (\n                            <tr key={index} className=\"hover:bg-gray-50\">\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">{activity.action}</td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{activity.user}</td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                                {new Date(activity.date).toLocaleString()}\n                              </td>\n                            </tr>\n                          ))}\n                        </tbody>\n                      </table>\n                    </div>\n                  </motion.div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    </div>\n  );\n};\n\nexport default Analytics;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,iBAAiB;AAC1D,SAASC,KAAK,IAAIC,OAAO,EAAEC,aAAa,EAAEC,WAAW,EAAEC,UAAU,EAAEC,UAAU,EAAEC,WAAW,EAAEC,YAAY,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,QAAQ,UAAU;AAClJ,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,UAAU,EAAEC,aAAa,EAAEC,aAAa,EAAEC,UAAU,EAAEC,aAAa,EAAEC,eAAe,EAAEC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAEC,eAAe,QAAQ,gBAAgB;;AAEpK;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAzB,OAAO,CAAC0B,QAAQ,CAACzB,aAAa,EAAEC,WAAW,EAAEC,UAAU,EAAEC,UAAU,EAAEC,WAAW,EAAEC,YAAY,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,CAAC;AAEvH,MAAMkB,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACtB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC;IACzC8C,MAAM,EAAE;MACNC,aAAa,EAAE,CAAC;MAChBC,gBAAgB,EAAE,CAAC;MACnBC,WAAW,EAAE,CAAC;MACdC,eAAe,EAAE,CAAC;MAClBC,aAAa,EAAE,CAAC;MAChBC,iBAAiB,EAAE,CAAC;MACpBC,aAAa,EAAE,CAAC;MAChBC,iBAAiB,EAAE,CAAC;MACpBC,aAAa,EAAE;IACjB,CAAC;IACDC,MAAM,EAAE;MACNC,aAAa,EAAE,EAAE;MACjBC,gBAAgB,EAAE,EAAE;MACpBC,WAAW,EAAE,EAAE;MACfC,eAAe,EAAE,EAAE;MACnBC,aAAa,EAAE,EAAE;MACjBC,gBAAgB,EAAE;IACpB,CAAC;IACDC,gBAAgB,EAAE;MAChBC,YAAY,EAAE,CAAC,CAAC;MAChBC,mBAAmB,EAAE;IACvB,CAAC;IACDC,sBAAsB,EAAE,EAAE;IAC1BC,mBAAmB,EAAE;MACnBC,kBAAkB,EAAE,EAAE;MACtBC,eAAe,EAAE;IACnB,CAAC;IACDC,cAAc,EAAE,EAAE;IAClBC,cAAc,EAAE,EAAE;IAClBC,eAAe,EAAE;MACfC,WAAW,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE,CAAC;MAAE,CAAC;MACrCC,kBAAkB,EAAE;QAAEF,KAAK,EAAE,CAAC;QAAEG,MAAM,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAE,CAAC;MAC5DC,UAAU,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAE,CAAC;MACxCC,gBAAgB,EAAE;QAAEC,aAAa,EAAE,CAAC;QAAEC,mBAAmB,EAAE;MAAE;IAC/D,CAAC;IACDC,WAAW,EAAE;MACXC,kBAAkB,EAAE;QAAEC,cAAc,EAAE,CAAC;QAAEC,gBAAgB,EAAE;MAAE,CAAC;MAC9DC,cAAc,EAAE;QAAEC,yBAAyB,EAAE,CAAC;QAAEC,uBAAuB,EAAE;MAAE,CAAC;MAC5EC,YAAY,EAAE;QAAEC,gBAAgB,EAAE,EAAE;QAAEC,iBAAiB,EAAE;MAAG;IAC9D,CAAC;IACDC,kBAAkB,EAAE;MAClBC,YAAY,EAAE,CAAC;MACfC,MAAM,EAAE,CAAC;MACTC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;QAAEC,YAAY,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAE;IAC9C;EACF,CAAC,CAAC;EAEF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvG,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwG,KAAK,EAAEC,QAAQ,CAAC,GAAGzG,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0G,SAAS,EAAEC,YAAY,CAAC,GAAG3G,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC4G,SAAS,EAAEC,YAAY,CAAC,GAAG7G,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;EACxD,MAAM,CAAC8G,SAAS,EAAEC,YAAY,CAAC,GAAG/G,QAAQ,CAAC;IACzCgH,QAAQ,EAAE,KAAK;IACfC,YAAY,EAAE,MAAM;IACpBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,KAAK;IACbC,GAAG,EAAE,KAAK;IACVC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAMC,QAAQ,GAAGpH,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEqH,IAAI;IAAEC;EAAM,CAAC,GAAGlG,OAAO,CAAC,CAAC;EAEjCrB,SAAS,CAAC,MAAM;IACd,MAAMwH,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI,CAACF,IAAI,IAAI,CAACC,KAAK,EAAE;QACnBf,QAAQ,CAAC,kCAAkC,CAAC;QAC5CF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEAA,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMmB,MAAM,GAAG;UACbC,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAUJ,KAAK;UAAG,CAAC;UAC7CK,MAAM,EAAE;YAAEnB;UAAU;QACtB,CAAC;QACD,MAAMoB,QAAQ,GAAG,MAAM3H,KAAK,CAAC4H,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,2BAA2B,EAAER,MAAM,CAAC;QACrG7E,YAAY,CAACiF,QAAQ,CAACK,IAAI,CAAC;MAC7B,CAAC,CAAC,OAAOC,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA,EAAAC,cAAA;QACZC,OAAO,CAAClC,KAAK,CAAC,cAAc,EAAE,EAAA6B,aAAA,GAAAD,GAAG,CAACN,QAAQ,cAAAO,aAAA,uBAAZA,aAAA,CAAcF,IAAI,KAAIC,GAAG,CAACO,OAAO,CAAC;QAChE,MAAMC,YAAY,GAAG,EAAAN,cAAA,GAAAF,GAAG,CAACN,QAAQ,cAAAQ,cAAA,uBAAZA,cAAA,CAAcO,MAAM,MAAK,GAAG,GAC7C,oCAAoC,GACpC,EAAAN,cAAA,GAAAH,GAAG,CAACN,QAAQ,cAAAS,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcJ,IAAI,cAAAK,mBAAA,uBAAlBA,mBAAA,CAAoBG,OAAO,KAAI,0BAA0B;QAC7DlC,QAAQ,CAACmC,YAAY,CAAC;QACtB,IAAI,EAAAH,cAAA,GAAAL,GAAG,CAACN,QAAQ,cAAAW,cAAA,uBAAZA,cAAA,CAAcI,MAAM,MAAK,GAAG,EAAE;UAChCvB,QAAQ,CAAC,QAAQ,CAAC;QACpB;MACF,CAAC,SAAS;QACRf,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDkB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACF,IAAI,EAAEC,KAAK,EAAEF,QAAQ,EAAEZ,SAAS,CAAC,CAAC;;EAEtC;EACA,MAAMoC,IAAI,GAAG;IACXC,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAC5BC,IAAI,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAK;IAAE;EAC3D,CAAC;;EAED;EACA,MAAMC,eAAe,GAAIC,SAAS,IAAK;IACrCvC,YAAY,CAACwC,IAAI,IAAI;MACnB,MAAMC,KAAK,GAAG;QACZ,KAAK,EAAE,MAAM;QACb,MAAM,EAAE,KAAK;QACb,KAAK,EAAE,UAAU;QACjB,UAAU,EAAE;MACd,CAAC;MACD,OAAO;QAAE,GAAGD,IAAI;QAAE,CAACD,SAAS,GAAGE,KAAK,CAACD,IAAI,CAACD,SAAS,CAAC;MAAE,CAAC;IACzD,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMG,YAAY,GAAG;IACnBC,MAAM,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,CAAC;IACvEC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,oBAAoB;MAC3BzB,IAAI,EAAE,CACJvF,SAAS,CAACE,MAAM,CAACC,aAAa,EAC9BH,SAAS,CAACE,MAAM,CAACE,gBAAgB,EACjCJ,SAAS,CAACE,MAAM,CAACG,WAAW,EAC5BL,SAAS,CAACE,MAAM,CAACI,eAAe,EAChCN,SAAS,CAACE,MAAM,CAACK,aAAa,CAC/B;MACD0G,eAAe,EAAE,CACf,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,0BAA0B,CAC3B;MACDC,WAAW,EAAE,CACX,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,wBAAwB,CACzB;MACDC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;EAED,MAAMC,gBAAgB,GAAG;IACvBN,MAAM,EAAE9G,SAAS,CAACmB,gBAAgB,CAACE,mBAAmB,CAACgG,GAAG,CAAC9B,IAAI,IAAI,GAAGA,IAAI,CAAC+B,KAAK,IAAI/B,IAAI,CAACgC,IAAI,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC9IR,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,cAAc;MACrBzB,IAAI,EAAEvF,SAAS,CAACmB,gBAAgB,CAACE,mBAAmB,CAACgG,GAAG,CAAC9B,IAAI,IAAIA,IAAI,CAACiC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MACxGP,eAAe,EAAE,yBAAyB;MAC1CC,WAAW,EAAE,uBAAuB;MACpCC,WAAW,EAAE,CAAC;MACdM,OAAO,EAAE;IACX,CAAC;EAEL,CAAC;;EAED;EACA,MAAMC,cAAc,GAAG;IACrBZ,MAAM,EAAE9G,SAAS,CAACsB,sBAAsB,CAAC+F,GAAG,CAACM,GAAG,IAAIA,GAAG,CAACC,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC;IAC3Hb,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,yBAAyB;MAChCzB,IAAI,EAAEvF,SAAS,CAACsB,sBAAsB,CAAC+F,GAAG,CAACM,GAAG,IAAIA,GAAG,CAACE,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MACnFZ,eAAe,EAAE,CACf,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,0BAA0B,EAC1B,yBAAyB,CAC1B;MACDC,WAAW,EAAE,CACX,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,wBAAwB,EACxB,uBAAuB,CACxB;MACDC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;;EAED;EACA,MAAMW,UAAU,GAAG;IACjBhB,MAAM,EAAE9G,SAAS,CAACuB,mBAAmB,CAACC,kBAAkB,CAAC6F,GAAG,CAACnB,IAAI,IAAIA,IAAI,CAAC3B,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC;IAChHwC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,qBAAqB;MAC5BzB,IAAI,EAAEvF,SAAS,CAACuB,mBAAmB,CAACC,kBAAkB,CAAC6F,GAAG,CAACnB,IAAI,IAAIA,IAAI,CAACsB,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MAC7FP,eAAe,EAAE,CACf,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,CAC1B;MACDC,WAAW,EAAE,CACX,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,CACxB;MACDC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;;EAED;EACA,MAAMY,OAAO,GAAG;IACdjB,MAAM,EAAE9G,SAAS,CAACuB,mBAAmB,CAACE,eAAe,CAAC4F,GAAG,CAACnB,IAAI,IAAIA,IAAI,CAAC8B,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC;IAC9HjB,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,kBAAkB;MACzBzB,IAAI,EAAEvF,SAAS,CAACuB,mBAAmB,CAACE,eAAe,CAAC4F,GAAG,CAACnB,IAAI,IAAIA,IAAI,CAACsB,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MACnGP,eAAe,EAAE,0BAA0B;MAC3CC,WAAW,EAAE,wBAAwB;MACrCC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;;EAED;EACA,MAAMc,aAAa,GAAG;IACpBnB,MAAM,EAAE9G,SAAS,CAAC0B,cAAc,CAAC2F,GAAG,CAACnB,IAAI,IAAIA,IAAI,CAACgC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,CAAC;IAC5HnB,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,iBAAiB;MACxBzB,IAAI,EAAEvF,SAAS,CAAC0B,cAAc,CAAC2F,GAAG,CAACnB,IAAI,IAAIA,IAAI,CAACsB,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAC9EP,eAAe,EAAE,CACf,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,0BAA0B,CAC3B;MACDC,WAAW,EAAE,CACX,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,wBAAwB,CACzB;MACDC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;;EAED;EACA,MAAMgB,WAAW,GAAGA,CAAC5C,IAAI,EAAE6C,IAAI,EAAEC,MAAM,GAAG,OAAO,KAAK;IACpD,MAAMC,UAAU,GAAG;MACjB/C,IAAI;MACJgD,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,KAAK;QAC1BC,OAAO,EAAE;UACPC,MAAM,EAAE;YACNC,QAAQ,EAAE;UACZ;QACF;MACF,CAAC;MACDP;IACF,CAAC;IAED,QAAQD,IAAI;MACV,KAAK,KAAK;QACR,oBAAO7I,OAAA,CAAC9B,GAAG;UAAA,GAAK6K;QAAU;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAChC,KAAK,MAAM;QACT,oBAAOzJ,OAAA,CAAC5B,IAAI;UAAA,GAAK2K;QAAU;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MACjC,KAAK,KAAK;QACR,oBAAOzJ,OAAA,CAAC7B,GAAG;UAAA,GAAK4K;QAAU;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAChC,KAAK,UAAU;QACb,oBAAOzJ,OAAA,CAAC3B,QAAQ;UAAA,GAAK0K;QAAU;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MACrC;QACE,oBAAOzJ,OAAA,CAAC9B,GAAG;UAAA,GAAK6K;QAAU;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;IAClC;EACF,CAAC;EAED,IAAItF,OAAO,EAAE,oBAAOnE,OAAA,CAACd,MAAM;IAAAoK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAE9B,IAAIpF,KAAK,EAAE;IACT,oBACErE,OAAA;MAAK0J,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvC3J,OAAA,CAACZ,iBAAiB;QAACwK,MAAM,EAAErJ,WAAY;QAACsJ,SAAS,EAAErJ;MAAe;QAAA8I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrEzJ,OAAA;QAAK0J,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnD3J,OAAA,CAACf,MAAM;UAAC6K,aAAa,EAAEA,CAAA,KAAMtJ,cAAc,CAAC,CAACD,WAAW;QAAE;UAAA+I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DzJ,OAAA;UAAK0J,SAAS,EAAC,oEAAoE;UAAAC,QAAA,eACjF3J,OAAA;YAAK0J,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChC3J,OAAA;cAAK0J,SAAS,EAAC,+CAA+C;cAAAC,QAAA,gBAC5D3J,OAAA;gBAAI0J,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAK;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/DzJ,OAAA;gBAAG0J,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEtF;cAAK;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxCzJ,OAAA;gBACE+J,OAAO,EAAEA,CAAA,KAAM5E,QAAQ,CAAC,QAAQ,CAAE;gBAClCuE,SAAS,EAAC,sFAAsF;gBAAAC,QAAA,EACjG;cAED;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEzJ,OAAA;IAAK0J,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvC3J,OAAA,CAACZ,iBAAiB;MAACwK,MAAM,EAAErJ,WAAY;MAACsJ,SAAS,EAAErJ;IAAe;MAAA8I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAErEzJ,OAAA;MAAK0J,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnD3J,OAAA,CAACf,MAAM;QAAC6K,aAAa,EAAEA,CAAA,KAAMtJ,cAAc,CAAC,CAACD,WAAW;MAAE;QAAA+I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE7DzJ,OAAA;QAAK0J,SAAS,EAAC,0FAA0F;QAAAC,QAAA,eACvG3J,OAAA;UAAK0J,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChC3J,OAAA;YAAK0J,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBAEvC3J,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;cAACC,QAAQ,EAAEtD,IAAK;cAAC+C,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eACtH3J,OAAA;gBAAK0J,SAAS,EAAC,8DAA8D;gBAAAC,QAAA,gBAC3E3J,OAAA;kBAAA2J,QAAA,gBACE3J,OAAA;oBAAI0J,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChEzJ,OAAA;oBAAG0J,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAyD;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC,eACNzJ,OAAA;kBAAK0J,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B3J,OAAA;oBAAOkK,OAAO,EAAC,WAAW;oBAACR,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,EAAC;kBAAU;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnGzJ,OAAA;oBACEmK,EAAE,EAAC,WAAW;oBACdC,KAAK,EAAE7F,SAAU;oBACjB8F,QAAQ,EAAGC,CAAC,IAAK9F,YAAY,CAAC8F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAC9CV,SAAS,EAAC,qKAAqK;oBAAAC,QAAA,gBAE/K3J,OAAA;sBAAQoK,KAAK,EAAC,QAAQ;sBAAAT,QAAA,EAAC;oBAAU;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC1CzJ,OAAA;sBAAQoK,KAAK,EAAC,SAAS;sBAAAT,QAAA,EAAC;oBAAa;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9CzJ,OAAA;sBAAQoK,KAAK,EAAC,SAAS;sBAAAT,QAAA,EAAC;oBAAa;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9CzJ,OAAA;sBAAQoK,KAAK,EAAC,OAAO;sBAAAT,QAAA,EAAC;oBAAS;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxCzJ,OAAA;sBAAQoK,KAAK,EAAC,KAAK;sBAAAT,QAAA,EAAC;oBAAQ;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGbzJ,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;cAACC,QAAQ,EAAEtD,IAAK;cAAC+C,SAAS,EAAC,+CAA+C;cAAAC,QAAA,eACnF3J,OAAA;gBAAK0J,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnC3J,OAAA;kBACE+J,OAAO,EAAEA,CAAA,KAAMrF,YAAY,CAAC,UAAU,CAAE;kBACxCgF,SAAS,EAAE,gFACTjF,SAAS,KAAK,UAAU,GACpB,iCAAiC,GACjC,4EAA4E,EAC/E;kBAAAkF,QAAA,gBAEH3J,OAAA,CAACX,UAAU;oBAACqK,SAAS,EAAC;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,YAEjC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTzJ,OAAA;kBACE+J,OAAO,EAAEA,CAAA,KAAMrF,YAAY,CAAC,UAAU,CAAE;kBACxCgF,SAAS,EAAE,gFACTjF,SAAS,KAAK,UAAU,GACpB,iCAAiC,GACjC,4EAA4E,EAC/E;kBAAAkF,QAAA,gBAEH3J,OAAA,CAACX,UAAU;oBAACqK,SAAS,EAAC;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,YAEjC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTzJ,OAAA;kBACE+J,OAAO,EAAEA,CAAA,KAAMrF,YAAY,CAAC,UAAU,CAAE;kBACxCgF,SAAS,EAAE,gFACTjF,SAAS,KAAK,UAAU,GACpB,iCAAiC,GACjC,4EAA4E,EAC/E;kBAAAkF,QAAA,gBAEH3J,OAAA,CAACV,aAAa;oBAACoK,SAAS,EAAC;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,YAEpC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTzJ,OAAA;kBACE+J,OAAO,EAAEA,CAAA,KAAMrF,YAAY,CAAC,cAAc,CAAE;kBAC5CgF,SAAS,EAAE,gFACTjF,SAAS,KAAK,cAAc,GACxB,iCAAiC,GACjC,4EAA4E,EAC/E;kBAAAkF,QAAA,gBAEH3J,OAAA,CAACR,UAAU;oBAACkK,SAAS,EAAC;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEjC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTzJ,OAAA;kBACE+J,OAAO,EAAEA,CAAA,KAAMrF,YAAY,CAAC,UAAU,CAAE;kBACxCgF,SAAS,EAAE,gFACTjF,SAAS,KAAK,UAAU,GACpB,iCAAiC,GACjC,4EAA4E,EAC/E;kBAAAkF,QAAA,gBAEH3J,OAAA,CAACP,aAAa;oBAACiK,SAAS,EAAC;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,YAEpC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTzJ,OAAA;kBACE+J,OAAO,EAAEA,CAAA,KAAMrF,YAAY,CAAC,cAAc,CAAE;kBAC5CgF,SAAS,EAAE,gFACTjF,SAAS,KAAK,cAAc,GACxB,iCAAiC,GACjC,4EAA4E,EAC/E;kBAAAkF,QAAA,gBAEH3J,OAAA,CAACT,aAAa;oBAACmK,SAAS,EAAC;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEpC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTzJ,OAAA;kBACE+J,OAAO,EAAEA,CAAA,KAAMrF,YAAY,CAAC,aAAa,CAAE;kBAC3CgF,SAAS,EAAE,gFACTjF,SAAS,KAAK,aAAa,GACvB,iCAAiC,GACjC,4EAA4E,EAC/E;kBAAAkF,QAAA,gBAEH3J,OAAA,CAACJ,WAAW;oBAAC8J,SAAS,EAAC;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAElC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTzJ,OAAA;kBACE+J,OAAO,EAAEA,CAAA,KAAMrF,YAAY,CAAC,UAAU,CAAE;kBACxCgF,SAAS,EAAE,gFACTjF,SAAS,KAAK,UAAU,GACpB,iCAAiC,GACjC,4EAA4E,EAC/E;kBAAAkF,QAAA,gBAEH3J,OAAA,CAACN,eAAe;oBAACgK,SAAS,EAAC;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,YAEtC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGbzJ,OAAA;cAAK0J,SAAS,EAAC,MAAM;cAAAC,QAAA,GAElBlF,SAAS,KAAK,UAAU,iBACvBzE,OAAA;gBAAA2J,QAAA,gBAEE3J,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;kBAACC,QAAQ,EAAEtD,IAAK;kBAAC+C,SAAS,EAAC,2DAA2D;kBAAAC,QAAA,gBAC/F3J,OAAA;oBAAK0J,SAAS,EAAC,+DAA+D;oBAAAC,QAAA,gBAC5E3J,OAAA;sBAAI0J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAc;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrEzJ,OAAA;sBAAG0J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAElJ,SAAS,CAACE,MAAM,CAACS;oBAAa;sBAAAkI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC,eACNzJ,OAAA;oBAAK0J,SAAS,EAAC,+DAA+D;oBAAAC,QAAA,gBAC5E3J,OAAA;sBAAI0J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzEzJ,OAAA;sBAAG0J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAElJ,SAAS,CAACE,MAAM,CAACM;oBAAiB;sBAAAqI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF,CAAC,eACNzJ,OAAA;oBAAK0J,SAAS,EAAC,+DAA+D;oBAAAC,QAAA,gBAC5E3J,OAAA;sBAAI0J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAc;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrEzJ,OAAA;sBAAG0J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAElJ,SAAS,CAACE,MAAM,CAACO;oBAAa;sBAAAoI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC,eACNzJ,OAAA;oBAAK0J,SAAS,EAAC,+DAA+D;oBAAAC,QAAA,gBAC5E3J,OAAA;sBAAI0J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzEzJ,OAAA;sBAAG0J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAElJ,SAAS,CAACE,MAAM,CAACQ;oBAAiB;sBAAAmI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAGbzJ,OAAA;kBAAK0J,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,gBAEzD3J,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;oBAACC,QAAQ,EAAEtD,IAAK;oBAAC+C,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBACvE3J,OAAA;sBAAK0J,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrD3J,OAAA;wBAAI0J,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAC;sBAAoB;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC1EzJ,OAAA;wBACE+J,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,UAAU,CAAE;wBAC3CwC,SAAS,EAAC,uHAAuH;wBAAAC,QAAA,EAClI;sBAED;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACNzJ,OAAA;sBAAKwK,KAAK,EAAE;wBAAE1B,MAAM,EAAE;sBAAQ,CAAE;sBAAAa,QAAA,EAC7Bf,WAAW,CAACtB,YAAY,EAAE3C,SAAS,CAACE,QAAQ;oBAAC;sBAAAyE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eAGbzJ,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;oBAACC,QAAQ,EAAEtD,IAAK;oBAAC+C,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBACvE3J,OAAA;sBAAK0J,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrD3J,OAAA;wBAAI0J,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAC;sBAAsB;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC5EzJ,OAAA;wBACE+J,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,cAAc,CAAE;wBAC/CwC,SAAS,EAAC,uHAAuH;wBAAAC,QAAA,EAClI;sBAED;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACNzJ,OAAA;sBAAKwK,KAAK,EAAE;wBAAE1B,MAAM,EAAE;sBAAQ,CAAE;sBAAAa,QAAA,EAC7Bf,WAAW,CAACf,gBAAgB,EAAElD,SAAS,CAACG,YAAY;oBAAC;sBAAAwE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAGNzJ,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;kBAACC,QAAQ,EAAEtD,IAAK;kBAAC+C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBACvE3J,OAAA;oBAAI0J,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EAAC;kBAAe;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1EzJ,OAAA;oBAAK0J,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,eAC9B3J,OAAA;sBAAO0J,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,gBACpD3J,OAAA;wBAAO0J,SAAS,EAAC,YAAY;wBAAAC,QAAA,eAC3B3J,OAAA;0BAAA2J,QAAA,gBACE3J,OAAA;4BAAI0J,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,EAAC;0BAAM;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAC1GzJ,OAAA;4BAAI0J,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,EAAC;0BAAI;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACxGzJ,OAAA;4BAAI0J,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,EAAC;0BAAI;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtG;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACRzJ,OAAA;wBAAO0J,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EACjDlJ,SAAS,CAAC2B,cAAc,CAACqI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC3C,GAAG,CAAC,CAAC4C,QAAQ,EAAEC,KAAK,kBACxD3K,OAAA;0BAAgB0J,SAAS,EAAC,kBAAkB;0BAAAC,QAAA,gBAC1C3J,OAAA;4BAAI0J,SAAS,EAAC,+DAA+D;4BAAAC,QAAA,EAAEe,QAAQ,CAACE;0BAAM;4BAAAtB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACpGzJ,OAAA;4BAAI0J,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAAEe,QAAQ,CAACtF;0BAAI;4BAAAkE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACtFzJ,OAAA;4BAAI0J,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAC9D,IAAIkB,IAAI,CAACH,QAAQ,CAACI,IAAI,CAAC,CAACC,cAAc,CAAC;0BAAC;4BAAAzB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvC,CAAC;wBAAA,GALEkB,KAAK;0BAAArB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAMV,CACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CACN,EAGAhF,SAAS,KAAK,UAAU,iBACvBzE,OAAA;gBAAA2J,QAAA,gBAEE3J,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;kBAACC,QAAQ,EAAEtD,IAAK;kBAAC+C,SAAS,EAAC,2DAA2D;kBAAAC,QAAA,gBAC/F3J,OAAA;oBAAK0J,SAAS,EAAC,+DAA+D;oBAAAC,QAAA,gBAC5E3J,OAAA;sBAAI0J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAY;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnEzJ,OAAA;sBAAG0J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAElJ,SAAS,CAAC4B,eAAe,CAACC,WAAW,CAACC;oBAAK;sBAAA+G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClGzJ,OAAA;sBAAG0J,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACNzJ,OAAA;oBAAK0J,SAAS,EAAC,8DAA8D;oBAAAC,QAAA,gBAC3E3J,OAAA;sBAAI0J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1EzJ,OAAA;sBAAG0J,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,GAAEqB,IAAI,CAACC,KAAK,CAACxK,SAAS,CAAC4B,eAAe,CAACI,kBAAkB,CAACE,YAAY,CAAC,EAAC,GAAC;oBAAA;sBAAA2G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC5HzJ,OAAA;sBAAG0J,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,GAAElJ,SAAS,CAAC4B,eAAe,CAACI,kBAAkB,CAACC,MAAM,EAAC,MAAI,EAACjC,SAAS,CAAC4B,eAAe,CAACI,kBAAkB,CAACF,KAAK,EAAC,eAAa;oBAAA;sBAAA+G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrK,CAAC,eACNzJ,OAAA;oBAAK0J,SAAS,EAAC,gEAAgE;oBAAAC,QAAA,gBAC7E3J,OAAA;sBAAI0J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAc;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrEzJ,OAAA;sBAAG0J,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,GAAElJ,SAAS,CAAC4B,eAAe,CAACU,gBAAgB,CAACC,aAAa,EAAC,GAAC;oBAAA;sBAAAsG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACjHzJ,OAAA;sBAAG0J,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAAY;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACNzJ,OAAA;oBAAK0J,SAAS,EAAC,gEAAgE;oBAAAC,QAAA,gBAC7E3J,OAAA;sBAAI0J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAc;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrEzJ,OAAA;sBAAG0J,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAAElJ,SAAS,CAAC4B,eAAe,CAACO,UAAU,CAACC;oBAAO;sBAAAyG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpGzJ,OAAA;sBAAG0J,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAAoB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAGbzJ,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;kBAACC,QAAQ,EAAEtD,IAAK;kBAAC+C,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBAC5E3J,OAAA;oBAAI0J,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/EzJ,OAAA;oBAAK0J,SAAS,EAAC,sDAAsD;oBAAAC,QAAA,EAClEuB,MAAM,CAACC,OAAO,CAAC1K,SAAS,CAAC4B,eAAe,CAACC,WAAW,CAACE,MAAM,CAAC,CAACsF,GAAG,CAAC,CAAC,CAACsD,IAAI,EAAEnD,KAAK,CAAC,kBAC9EjI,OAAA;sBAAgB0J,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,gBAC/D3J,OAAA;wBAAK0J,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAE1B;sBAAK;wBAAAqB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAChEzJ,OAAA;wBAAK0J,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,GAAEyB,IAAI,EAAC,GAAC;sBAAA;wBAAA9B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA,GAFvD2B,IAAI;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAGT,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAGbzJ,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;kBAACC,QAAQ,EAAEtD,IAAK;kBAAC+C,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,gBAChF3J,OAAA;oBAAK0J,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChD3J,OAAA;sBAAI0J,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,EAAC;oBAAsB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjFzJ,OAAA;sBAAK0J,SAAS,EAAC,WAAW;sBAAAC,QAAA,gBACxB3J,OAAA;wBAAK0J,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChD3J,OAAA;0BAAM0J,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAAC;wBAAe;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACtDzJ,OAAA;0BAAM0J,SAAS,EAAC,0BAA0B;0BAAAC,QAAA,GAAEqB,IAAI,CAACC,KAAK,CAACxK,SAAS,CAACyC,WAAW,CAACC,kBAAkB,CAACC,cAAc,CAAC,EAAC,GAAC;wBAAA;0BAAAkG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrH,CAAC,eACNzJ,OAAA;wBAAK0J,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,eAClD3J,OAAA;0BACE0J,SAAS,EAAC,+BAA+B;0BACzCc,KAAK,EAAE;4BAAEa,KAAK,EAAE,GAAG5K,SAAS,CAACyC,WAAW,CAACC,kBAAkB,CAACC,cAAc;0BAAI;wBAAE;0BAAAkG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5E;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNzJ,OAAA;wBAAK0J,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChD3J,OAAA;0BAAM0J,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAAC;wBAAiB;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACxDzJ,OAAA;0BAAM0J,SAAS,EAAC,wBAAwB;0BAAAC,QAAA,GAAEqB,IAAI,CAACC,KAAK,CAACxK,SAAS,CAACyC,WAAW,CAACC,kBAAkB,CAACE,gBAAgB,CAAC,EAAC,GAAC;wBAAA;0BAAAiG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrH,CAAC,eACNzJ,OAAA;wBAAK0J,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,eAClD3J,OAAA;0BACE0J,SAAS,EAAC,6BAA6B;0BACvCc,KAAK,EAAE;4BAAEa,KAAK,EAAE,GAAG5K,SAAS,CAACyC,WAAW,CAACC,kBAAkB,CAACE,gBAAgB;0BAAI;wBAAE;0BAAAiG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9E;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENzJ,OAAA;oBAAK0J,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChD3J,OAAA;sBAAI0J,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC7EzJ,OAAA;sBAAK0J,SAAS,EAAC,WAAW;sBAAAC,QAAA,gBACxB3J,OAAA;wBAAK0J,SAAS,EAAC,aAAa;wBAAAC,QAAA,gBAC1B3J,OAAA;0BAAK0J,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAElJ,SAAS,CAACyC,WAAW,CAACI,cAAc,CAACC;wBAAyB;0BAAA+F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACzHzJ,OAAA;0BAAK0J,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,EAAC;wBAAwB;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClE,CAAC,eACNzJ,OAAA;wBAAK0J,SAAS,EAAC,aAAa;wBAAAC,QAAA,gBAC1B3J,OAAA;0BAAK0J,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,GAAElJ,SAAS,CAACyC,WAAW,CAACI,cAAc,CAACE,uBAAuB,EAAC,GAAC;wBAAA;0BAAA8F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACxHzJ,OAAA;0BAAK0J,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,EAAC;wBAAyB;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAGbzJ,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;kBAACC,QAAQ,EAAEtD,IAAK;kBAAC+C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBACvE3J,OAAA;oBAAI0J,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EAAC;kBAAwB;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnFzJ,OAAA;oBAAK0J,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpD3J,OAAA;sBAAA2J,QAAA,gBACE3J,OAAA;wBAAI0J,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAC;sBAAkB;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACxEzJ,OAAA;wBAAK0J,SAAS,EAAC,WAAW;wBAAAC,QAAA,EACvBlJ,SAAS,CAACyC,WAAW,CAACO,YAAY,CAACC,gBAAgB,CAAC+G,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC3C,GAAG,CAAC,CAACwD,OAAO,EAAEX,KAAK,kBAClF3K,OAAA;0BAAiB0J,SAAS,EAAC,2DAA2D;0BAAAC,QAAA,gBACpF3J,OAAA;4BAAM0J,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,EAAE2B,OAAO,CAACA;0BAAO;4BAAAhC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,eAChEzJ,OAAA;4BAAM0J,SAAS,EAAC,0BAA0B;4BAAAC,QAAA,EAAE2B,OAAO,CAACC;0BAAK;4BAAAjC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA,GAFzDkB,KAAK;0BAAArB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAGV,CACN;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNzJ,OAAA;sBAAA2J,QAAA,gBACE3J,OAAA;wBAAI0J,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAC;sBAAmB;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACzEzJ,OAAA;wBAAK0J,SAAS,EAAC,WAAW;wBAAAC,QAAA,EACvBlJ,SAAS,CAACyC,WAAW,CAACO,YAAY,CAACE,iBAAiB,CAAC8G,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC3C,GAAG,CAAC,CAACwD,OAAO,EAAEX,KAAK,kBACnF3K,OAAA;0BAAiB0J,SAAS,EAAC,yDAAyD;0BAAAC,QAAA,gBAClF3J,OAAA;4BAAM0J,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,EAAE2B,OAAO,CAACA;0BAAO;4BAAAhC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,eAChEzJ,OAAA;4BAAM0J,SAAS,EAAC,wBAAwB;4BAAAC,QAAA,EAAE2B,OAAO,CAACC;0BAAK;4BAAAjC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA,GAFvDkB,KAAK;0BAAArB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAGV,CACN;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CACN,EAGAhF,SAAS,KAAK,UAAU,iBACvBzE,OAAA;gBAAA2J,QAAA,gBAEE3J,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;kBAACC,QAAQ,EAAEtD,IAAK;kBAAC+C,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBAC5E3J,OAAA;oBAAK0J,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD3J,OAAA;sBAAI0J,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAoB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzEzJ,OAAA;sBACE+J,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,UAAU,CAAE;sBAC3CwC,SAAS,EAAC,4FAA4F;sBAAAC,QAAA,EACvG;oBAED;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNzJ,OAAA;oBAAKwK,KAAK,EAAE;sBAAE1B,MAAM,EAAE;oBAAQ,CAAE;oBAAAa,QAAA,EAC7Bf,WAAW,CAACtB,YAAY,EAAE3C,SAAS,CAACE,QAAQ;kBAAC;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAGbzJ,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;kBAACC,QAAQ,EAAEtD,IAAK;kBAAC+C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBACvE3J,OAAA;oBAAI0J,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAC;kBAAwB;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClFzJ,OAAA;oBAAK0J,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpD3J,OAAA;sBAAK0J,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,gBAC/D3J,OAAA;wBAAI0J,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC/DzJ,OAAA;wBAAG0J,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAElJ,SAAS,CAACE,MAAM,CAACC;sBAAa;wBAAA0I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjF,CAAC,eACNzJ,OAAA;sBAAK0J,SAAS,EAAC,oDAAoD;sBAAAC,QAAA,gBACjE3J,OAAA;wBAAI0J,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,EAAC;sBAAW;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACnEzJ,OAAA;wBAAG0J,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAElJ,SAAS,CAACE,MAAM,CAACE;sBAAgB;wBAAAyI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrF,CAAC,eACNzJ,OAAA;sBAAK0J,SAAS,EAAC,sDAAsD;sBAAAC,QAAA,gBACnE3J,OAAA;wBAAI0J,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,EAAC;sBAAM;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC/DzJ,OAAA;wBAAG0J,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,EAAElJ,SAAS,CAACE,MAAM,CAACG;sBAAW;wBAAAwI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CACN,EAGAhF,SAAS,KAAK,cAAc,iBAC3BzE,OAAA;gBAAA2J,QAAA,gBAEE3J,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;kBAACC,QAAQ,EAAEtD,IAAK;kBAAC+C,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBAC5E3J,OAAA;oBAAK0J,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD3J,OAAA;sBAAI0J,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAuB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC5EzJ,OAAA;sBACE+J,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,cAAc,CAAE;sBAC/CwC,SAAS,EAAC,4FAA4F;sBAAAC,QAAA,EACvG;oBAED;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNzJ,OAAA;oBAAKwK,KAAK,EAAE;sBAAE1B,MAAM,EAAE;oBAAQ,CAAE;oBAAAa,QAAA,EAC7Bf,WAAW,CAACT,cAAc,EAAExD,SAAS,CAACI,YAAY;kBAAC;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAGbzJ,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;kBAACC,QAAQ,EAAEtD,IAAK;kBAAC+C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBACvE3J,OAAA;oBAAI0J,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAC;kBAAqB;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/EzJ,OAAA;oBAAK0J,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,eAC9B3J,OAAA;sBAAO0J,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,gBACpD3J,OAAA;wBAAO0J,SAAS,EAAC,YAAY;wBAAAC,QAAA,eAC3B3J,OAAA;0BAAA2J,QAAA,gBACE3J,OAAA;4BAAI0J,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,EAAC;0BAAU;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAC9GzJ,OAAA;4BAAI0J,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,EAAC;0BAAQ;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAC5GzJ,OAAA;4BAAI0J,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,EAAC;0BAAE;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpG;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACRzJ,OAAA;wBAAO0J,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EACjDlJ,SAAS,CAACsB,sBAAsB,CAAC+F,GAAG,CAAC,CAACM,GAAG,EAAEuC,KAAK,kBAC/C3K,OAAA;0BAAgB0J,SAAS,EAAC,kBAAkB;0BAAAC,QAAA,gBAC1C3J,OAAA;4BAAI0J,SAAS,EAAC,+DAA+D;4BAAAC,QAAA,EAAEvB,GAAG,CAACC;0BAAc;4BAAAiB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACvGzJ,OAAA;4BAAI0J,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAAEvB,GAAG,CAACE;0BAAY;4BAAAgB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACzFzJ,OAAA;4BAAI0J,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAAEvB,GAAG,CAACoD;0BAAY;4BAAAlC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA,GAHlFkB,KAAK;0BAAArB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAIV,CACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CACN,EAGAhF,SAAS,KAAK,UAAU,iBACvBzE,OAAA;gBAAA2J,QAAA,gBAEE3J,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;kBAACC,QAAQ,EAAEtD,IAAK;kBAAC+C,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,gBAEhF3J,OAAA;oBAAK0J,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChD3J,OAAA;sBAAK0J,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrD3J,OAAA;wBAAI0J,SAAS,EAAC,iCAAiC;wBAAAC,QAAA,EAAC;sBAAmB;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACxEzJ,OAAA;wBACE+J,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,QAAQ,CAAE;wBACzCwC,SAAS,EAAC,4FAA4F;wBAAAC,QAAA,EACvG;sBAED;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACNzJ,OAAA;sBAAKwK,KAAK,EAAE;wBAAE1B,MAAM,EAAE;sBAAQ,CAAE;sBAAAa,QAAA,EAC7Bf,WAAW,CAACL,UAAU,EAAE5D,SAAS,CAACK,MAAM;oBAAC;sBAAAsE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNzJ,OAAA;oBAAK0J,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChD3J,OAAA;sBAAK0J,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrD3J,OAAA;wBAAI0J,SAAS,EAAC,iCAAiC;wBAAAC,QAAA,EAAC;sBAAgB;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACrEzJ,OAAA;wBACE+J,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,KAAK,CAAE;wBACtCwC,SAAS,EAAC,4FAA4F;wBAAAC,QAAA,EACvG;sBAED;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACNzJ,OAAA;sBAAKwK,KAAK,EAAE;wBAAE1B,MAAM,EAAE;sBAAQ,CAAE;sBAAAa,QAAA,EAC7Bf,WAAW,CAACJ,OAAO,EAAE7D,SAAS,CAACM,GAAG;oBAAC;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAGbzJ,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;kBAACC,QAAQ,EAAEtD,IAAK;kBAAC+C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBACvE3J,OAAA;oBAAK0J,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD3J,OAAA;sBAAI0J,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAe;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpEzJ,OAAA;sBACE+J,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,YAAY,CAAE;sBAC7CwC,SAAS,EAAC,4FAA4F;sBAAAC,QAAA,EACvG;oBAED;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNzJ,OAAA;oBAAKwK,KAAK,EAAE;sBAAE1B,MAAM,EAAE;oBAAQ,CAAE;oBAAAa,QAAA,EAC7Bf,WAAW,CAACF,aAAa,EAAE/D,SAAS,CAACO,UAAU;kBAAC;oBAAAoE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CACN,EAGAhF,SAAS,KAAK,cAAc,iBAC3BzE,OAAA;gBAAA2J,QAAA,gBAEE3J,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;kBAACC,QAAQ,EAAEtD,IAAK;kBAAC+C,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBAC5E3J,OAAA;oBAAK0J,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD3J,OAAA;sBAAI0J,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAsB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3EzJ,OAAA;sBACE+J,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,cAAc,CAAE;sBAC/CwC,SAAS,EAAC,4FAA4F;sBAAAC,QAAA,EACvG;oBAED;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNzJ,OAAA;oBAAKwK,KAAK,EAAE;sBAAE1B,MAAM,EAAE;oBAAQ,CAAE;oBAAAa,QAAA,EAC7Bf,WAAW,CAACf,gBAAgB,EAAElD,SAAS,CAACG,YAAY;kBAAC;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAGbzJ,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;kBAACC,QAAQ,EAAEtD,IAAK;kBAAC+C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBACvE3J,OAAA;oBAAI0J,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5EzJ,OAAA;oBAAK0J,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpD3J,OAAA;sBAAK0J,SAAS,EAAC,oDAAoD;sBAAAC,QAAA,gBACjE3J,OAAA;wBAAI0J,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,EAAC;sBAAS;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACjEzJ,OAAA;wBAAG0J,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAE,EAAAvJ,qBAAA,GAAAK,SAAS,CAACmB,gBAAgB,CAACC,YAAY,cAAAzB,qBAAA,uBAAvCA,qBAAA,CAAyCqL,SAAS,KAAI;sBAAC;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3G,CAAC,eACNzJ,OAAA;sBAAK0J,SAAS,EAAC,sDAAsD;sBAAAC,QAAA,gBACnE3J,OAAA;wBAAI0J,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,EAAC;sBAAO;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAChEzJ,OAAA;wBAAG0J,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,EAAE,EAAAtJ,sBAAA,GAAAI,SAAS,CAACmB,gBAAgB,CAACC,YAAY,cAAAxB,sBAAA,uBAAvCA,sBAAA,CAAyCqL,OAAO,KAAI;sBAAC;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1G,CAAC,eACNzJ,OAAA;sBAAK0J,SAAS,EAAC,gDAAgD;sBAAAC,QAAA,gBAC7D3J,OAAA;wBAAI0J,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAC;sBAAS;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC/DzJ,OAAA;wBAAG0J,SAAS,EAAC,iCAAiC;wBAAAC,QAAA,EAAE,EAAArJ,sBAAA,GAAAG,SAAS,CAACmB,gBAAgB,CAACC,YAAY,cAAAvB,sBAAA,uBAAvCA,sBAAA,CAAyCqL,SAAS,KAAI;sBAAC;wBAAArC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CACN,EAGAhF,SAAS,KAAK,aAAa,iBAC1BzE,OAAA;gBAAA2J,QAAA,gBAEE3J,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;kBAACC,QAAQ,EAAEtD,IAAK;kBAAC+C,SAAS,EAAC,2DAA2D;kBAAAC,QAAA,gBAC/F3J,OAAA;oBAAK0J,SAAS,EAAC,+DAA+D;oBAAAC,QAAA,gBAC5E3J,OAAA;sBAAI0J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAa;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpEzJ,OAAA;sBAAG0J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,GAAElJ,SAAS,CAACmD,kBAAkB,CAACC,YAAY,EAAC,IAAE;oBAAA;sBAAAyF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAClGzJ,OAAA;sBAAG0J,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAAqB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC,eACNzJ,OAAA;oBAAK0J,SAAS,EAAC,8DAA8D;oBAAAC,QAAA,gBAC3E3J,OAAA;sBAAI0J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAa;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpEzJ,OAAA;sBAAG0J,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,GAAElJ,SAAS,CAACmD,kBAAkB,CAACE,MAAM,EAAC,GAAC;oBAAA;sBAAAwF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC1FzJ,OAAA;sBAAG0J,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC,eACNzJ,OAAA;oBAAK0J,SAAS,EAAC,gEAAgE;oBAAAC,QAAA,gBAC7E3J,OAAA;sBAAI0J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAU;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjEzJ,OAAA;sBAAG0J,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,GAAElJ,SAAS,CAACmD,kBAAkB,CAACG,SAAS,EAAC,GAAC;oBAAA;sBAAAuF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC/FzJ,OAAA;sBAAG0J,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC,eACNzJ,OAAA;oBAAK0J,SAAS,EAAC,gEAAgE;oBAAAC,QAAA,gBAC7E3J,OAAA;sBAAI0J,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAY;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnEzJ,OAAA;sBAAG0J,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,GAAElJ,SAAS,CAACmD,kBAAkB,CAACI,WAAW,CAACC,YAAY,EAAC,GAAC;oBAAA;sBAAAqF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC9GzJ,OAAA;sBAAG0J,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAGbzJ,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;kBAACC,QAAQ,EAAEtD,IAAK;kBAAC+C,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,gBAChF3J,OAAA;oBAAK0J,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChD3J,OAAA;sBAAI0J,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC7EzJ,OAAA;sBAAK0J,SAAS,EAAC,WAAW;sBAAAC,QAAA,gBACxB3J,OAAA;wBAAA2J,QAAA,gBACE3J,OAAA;0BAAK0J,SAAS,EAAC,wCAAwC;0BAAAC,QAAA,gBACrD3J,OAAA;4BAAM0J,SAAS,EAAC,eAAe;4BAAAC,QAAA,EAAC;0BAAa;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACpDzJ,OAAA;4BAAM0J,SAAS,EAAC,0BAA0B;4BAAAC,QAAA,GAAElJ,SAAS,CAACmD,kBAAkB,CAACC,YAAY,EAAC,IAAE;0BAAA;4BAAAyF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5F,CAAC,eACNzJ,OAAA;0BAAK0J,SAAS,EAAC,qCAAqC;0BAAAC,QAAA,eAClD3J,OAAA;4BACE0J,SAAS,EAAC,+BAA+B;4BACzCc,KAAK,EAAE;8BAAEa,KAAK,EAAE,GAAGL,IAAI,CAACY,GAAG,CAAC,CAAC,GAAG,GAAGnL,SAAS,CAACmD,kBAAkB,CAACC,YAAY,IAAI,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC;4BAAI;0BAAE;4BAAAyF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjG;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNzJ,OAAA;wBAAA2J,QAAA,gBACE3J,OAAA;0BAAK0J,SAAS,EAAC,wCAAwC;0BAAAC,QAAA,gBACrD3J,OAAA;4BAAM0J,SAAS,EAAC,eAAe;4BAAAC,QAAA,EAAC;0BAAa;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACpDzJ,OAAA;4BAAM0J,SAAS,EAAC,yBAAyB;4BAAAC,QAAA,GAAElJ,SAAS,CAACmD,kBAAkB,CAACE,MAAM,EAAC,GAAC;0BAAA;4BAAAwF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpF,CAAC,eACNzJ,OAAA;0BAAK0J,SAAS,EAAC,qCAAqC;0BAAAC,QAAA,eAClD3J,OAAA;4BACE0J,SAAS,EAAC,8BAA8B;4BACxCc,KAAK,EAAE;8BAAEa,KAAK,EAAE,GAAG5K,SAAS,CAACmD,kBAAkB,CAACE,MAAM;4BAAI;0BAAE;4BAAAwF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxD;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNzJ,OAAA;wBAAA2J,QAAA,gBACE3J,OAAA;0BAAK0J,SAAS,EAAC,wCAAwC;0BAAAC,QAAA,gBACrD3J,OAAA;4BAAM0J,SAAS,EAAC,eAAe;4BAAAC,QAAA,EAAC;0BAAU;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACjDzJ,OAAA;4BAAM0J,SAAS,EAAC,wBAAwB;4BAAAC,QAAA,GAAElJ,SAAS,CAACmD,kBAAkB,CAACG,SAAS,EAAC,GAAC;0BAAA;4BAAAuF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtF,CAAC,eACNzJ,OAAA;0BAAK0J,SAAS,EAAC,qCAAqC;0BAAAC,QAAA,eAClD3J,OAAA;4BACE0J,SAAS,EAAC,6BAA6B;4BACvCc,KAAK,EAAE;8BAAEa,KAAK,EAAE,GAAG5K,SAAS,CAACmD,kBAAkB,CAACG,SAAS,GAAG,EAAE;4BAAI;0BAAE;4BAAAuF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENzJ,OAAA;oBAAK0J,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChD3J,OAAA;sBAAI0J,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,EAAC;oBAAoB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/EzJ,OAAA;sBAAK0J,SAAS,EAAC,WAAW;sBAAAC,QAAA,gBACxB3J,OAAA;wBAAK0J,SAAS,EAAC,aAAa;wBAAAC,QAAA,gBAC1B3J,OAAA;0BAAK0J,SAAS,EAAC,4DAA4D;0BAAAC,QAAA,gBACzE3J,OAAA;4BAAK0J,SAAS,EAAC,gCAAgC;4BAACmC,OAAO,EAAC,WAAW;4BAAAlC,QAAA,gBACjE3J,OAAA;8BACE0J,SAAS,EAAC,eAAe;8BACzBoC,MAAM,EAAC,cAAc;8BACrBC,WAAW,EAAC,GAAG;8BACfC,IAAI,EAAC,MAAM;8BACXC,CAAC,EAAC;4BAA+E;8BAAA3C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAClF,CAAC,eACFzJ,OAAA;8BACE0J,SAAS,EAAC,gBAAgB;8BAC1BoC,MAAM,EAAC,cAAc;8BACrBC,WAAW,EAAC,GAAG;8BACfG,eAAe,EAAE,GAAGzL,SAAS,CAACmD,kBAAkB,CAACI,WAAW,CAACC,YAAY,OAAQ;8BACjFkI,aAAa,EAAC,OAAO;8BACrBH,IAAI,EAAC,MAAM;8BACXC,CAAC,EAAC;4BAA+E;8BAAA3C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAClF,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC,eACNzJ,OAAA;4BAAK0J,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,eAChE3J,OAAA;8BAAM0J,SAAS,EAAC,kCAAkC;8BAAAC,QAAA,GAAElJ,SAAS,CAACmD,kBAAkB,CAACI,WAAW,CAACC,YAAY,EAAC,GAAC;4BAAA;8BAAAqF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/G,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNzJ,OAAA;0BAAK0J,SAAS,EAAC,4BAA4B;0BAAAC,QAAA,EAAC;wBAAiB;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChE,CAAC,eACNzJ,OAAA;wBAAK0J,SAAS,EAAC,aAAa;wBAAAC,QAAA,gBAC1B3J,OAAA;0BAAK0J,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,GAAElJ,SAAS,CAACmD,kBAAkB,CAACI,WAAW,CAACE,QAAQ,EAAC,GAAC;wBAAA;0BAAAoF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAC7GzJ,OAAA;0BAAK0J,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,EAAC;wBAAa;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAGbzJ,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;kBAACC,QAAQ,EAAEtD,IAAK;kBAAC+C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBACvE3J,OAAA;oBAAI0J,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EAAC;kBAA2B;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtFzJ,OAAA;oBAAK0J,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpD3J,OAAA;sBAAK0J,SAAS,EAAC,oDAAoD;sBAAAC,QAAA,gBACjE3J,OAAA;wBAAI0J,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAkB;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACzEzJ,OAAA;wBAAI0J,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,gBAC9C3J,OAAA;0BAAA2J,QAAA,GAAI,qCAA8B,EAAClJ,SAAS,CAACmD,kBAAkB,CAACE,MAAM,EAAC,IAAE;wBAAA;0BAAAwF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC9EzJ,OAAA;0BAAA2J,QAAA,EAAI;wBAA4C;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACrDzJ,OAAA;0BAAA2J,QAAA,EAAI;wBAAiC;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACNzJ,OAAA;sBAAK0J,SAAS,EAAC,sDAAsD;sBAAAC,QAAA,gBACnE3J,OAAA;wBAAI0J,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,EAAC;sBAAwB;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAChFzJ,OAAA;wBAAI0J,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAC/C3J,OAAA;0BAAA2J,QAAA,EAAI;wBAA6B;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACtCzJ,OAAA;0BAAA2J,QAAA,EAAI;wBAAsC;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC/CzJ,OAAA;0BAAA2J,QAAA,EAAI;wBAAwC;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CACN,EAGAhF,SAAS,KAAK,UAAU,iBACvBzE,OAAA;gBAAA2J,QAAA,eAEE3J,OAAA,CAAC/B,MAAM,CAAC+L,GAAG;kBAACC,QAAQ,EAAEtD,IAAK;kBAAC+C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBACvE3J,OAAA;oBAAI0J,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAC;kBAAe;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzEzJ,OAAA;oBAAK0J,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,eAC9B3J,OAAA;sBAAO0J,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,gBACpD3J,OAAA;wBAAO0J,SAAS,EAAC,YAAY;wBAAAC,QAAA,eAC3B3J,OAAA;0BAAA2J,QAAA,gBACE3J,OAAA;4BAAI0J,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,EAAC;0BAAM;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAC1GzJ,OAAA;4BAAI0J,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,EAAC;0BAAI;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACxGzJ,OAAA;4BAAI0J,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,EAAC;0BAAI;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtG;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACRzJ,OAAA;wBAAO0J,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EACjDlJ,SAAS,CAAC2B,cAAc,CAAC0F,GAAG,CAAC,CAAC4C,QAAQ,EAAEC,KAAK,kBAC5C3K,OAAA;0BAAgB0J,SAAS,EAAC,kBAAkB;0BAAAC,QAAA,gBAC1C3J,OAAA;4BAAI0J,SAAS,EAAC,+DAA+D;4BAAAC,QAAA,EAAEe,QAAQ,CAACE;0BAAM;4BAAAtB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACpGzJ,OAAA;4BAAI0J,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAAEe,QAAQ,CAACtF;0BAAI;4BAAAkE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACtFzJ,OAAA;4BAAI0J,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAC9D,IAAIkB,IAAI,CAACH,QAAQ,CAACI,IAAI,CAAC,CAACC,cAAc,CAAC;0BAAC;4BAAAzB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvC,CAAC;wBAAA,GALEkB,KAAK;0BAAArB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAMV,CACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEV,CAAC;AAACtJ,EAAA,CAr+BID,SAAS;EAAA,QAiEInC,WAAW,EACJoB,OAAO;AAAA;AAAAiN,EAAA,GAlE3BlM,SAAS;AAu+Bf,eAAeA,SAAS;AAAC,IAAAkM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}