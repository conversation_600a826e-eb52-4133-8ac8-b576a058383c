{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\student\\\\Gallery.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useRef } from 'react';\nimport Navbar from './Navbar';\nimport Sidebar from './Sidebar';\nimport PatientNav from './PatientNav';\nimport axios from 'axios';\nimport { useParams } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { motion } from 'framer-motion';\nimport { FaImages, FaXRay, FaEdit, FaTrash, FaPlus, FaDownload } from 'react-icons/fa';\nimport Loader from '../components/Loader';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Gallery = () => {\n  _s();\n  const {\n    token\n  } = useAuth();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [images, setImages] = useState([]);\n  const [xrays, setXrays] = useState([]);\n  const [editingNote, setEditingNote] = useState(null);\n  const [currentNote, setCurrentNote] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [showImageModal, setShowImageModal] = useState(false);\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\n  const [itemToDelete, setItemToDelete] = useState(null);\n  const [activeTab, setActiveTab] = useState('gallery'); // 'gallery' or 'xray'\n  const [notification, setNotification] = useState({\n    show: false,\n    message: '',\n    type: 'success'\n  });\n  const fileInputRef = useRef(null);\n  const {\n    nationalId\n  } = useParams();\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/patients/${nationalId}`, {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        });\n        if (response.data) {\n          setImages(response.data.galleryImages || []);\n          setXrays(response.data.xrays || []);\n        } else {\n          setError('No data found for this patient');\n        }\n      } catch (err) {\n        var _err$response, _err$response$data;\n        console.error('Error fetching data:', err);\n        setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'Failed to load data');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (token && nationalId) {\n      fetchData();\n    }\n  }, [nationalId, token]);\n  const openImageModal = image => {\n    setSelectedImage(image);\n    setShowImageModal(true);\n  };\n  const handleImageUpload = async e => {\n    const files = e.target.files;\n    if (!files || files.length === 0) return;\n    const formData = new FormData();\n    Array.from(files).forEach(file => {\n      if (activeTab === 'gallery') {\n        formData.append('galleryImages', file);\n      } else {\n        formData.append('xrays', file);\n      }\n    });\n    try {\n      setLoading(true);\n      const endpoint = activeTab === 'gallery' ? `${process.env.REACT_APP_API_URL}/api/patients/${nationalId}/gallery` : `${process.env.REACT_APP_API_URL}/api/patients/${nationalId}/xrays`;\n      const response = await axios.post(endpoint, formData, {\n        headers: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      if (response.data) {\n        if (activeTab === 'gallery') {\n          setImages(response.data);\n        } else {\n          setXrays(response.data);\n        }\n\n        // Show success notification\n        setNotification({\n          show: true,\n          message: `${activeTab === 'gallery' ? 'Images' : 'X-rays'} uploaded successfully!`,\n          type: 'success'\n        });\n\n        // Hide notification after 3 seconds\n        setTimeout(() => {\n          setNotification({\n            show: false,\n            message: '',\n            type: 'success'\n          });\n        }, 3000);\n      }\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      console.error('Upload error:', err);\n      setError(((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.message) || `Failed to upload ${activeTab === 'gallery' ? 'images' : 'X-rays'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAddNote = async id => {\n    if (!currentNote.trim()) {\n      setEditingNote(null);\n      return;\n    }\n    try {\n      const endpoint = activeTab === 'gallery' ? `http://localhost:5000/api/patients/${nationalId}/gallery/note` : `http://localhost:5000/api/patients/${nationalId}/xrays/note`;\n      const payload = activeTab === 'gallery' ? {\n        imageId: id,\n        note: currentNote\n      } : {\n        xrayId: id,\n        note: currentNote\n      };\n      const response = await axios.put(endpoint, payload, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (response.data) {\n        if (activeTab === 'gallery') {\n          setImages(images.map(img => img._id === id ? {\n            ...img,\n            note: currentNote\n          } : img));\n        } else {\n          setXrays(xrays.map(xray => xray._id === id ? {\n            ...xray,\n            note: currentNote\n          } : xray));\n        }\n      }\n      setEditingNote(null);\n      setCurrentNote('');\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      console.error('Note update error:', err);\n      setError(((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || 'Failed to update note');\n    }\n  };\n  const confirmDeleteItem = item => {\n    setItemToDelete(item);\n    setShowDeleteModal(true);\n  };\n  const handleDeleteItem = async () => {\n    if (!itemToDelete) return;\n    try {\n      const endpoint = activeTab === 'gallery' ? `http://localhost:5000/api/patients/${nationalId}/gallery/${itemToDelete._id}` : `http://localhost:5000/api/patients/${nationalId}/xrays/${itemToDelete._id}`;\n      await axios.delete(endpoint, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (activeTab === 'gallery') {\n        setImages(images.filter(img => img._id !== itemToDelete._id));\n      } else {\n        setXrays(xrays.filter(xray => xray._id !== itemToDelete._id));\n      }\n      setShowDeleteModal(false);\n      setItemToDelete(null);\n\n      // Show success notification\n      setNotification({\n        show: true,\n        message: `${activeTab === 'gallery' ? 'Image' : 'X-ray'} deleted successfully!`,\n        type: 'success'\n      });\n\n      // Hide notification after 3 seconds\n      setTimeout(() => {\n        setNotification({\n          show: false,\n          message: '',\n          type: 'success'\n        });\n      }, 3000);\n    } catch (err) {\n      var _err$response4, _err$response4$data;\n      console.error('Delete error:', err);\n      setError(((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.message) || `Failed to delete ${activeTab === 'gallery' ? 'image' : 'X-ray'}`);\n      setShowDeleteModal(false);\n    }\n  };\n  const triggerFileInput = () => fileInputRef.current.click();\n\n  // Animation variants\n  const container = {\n    hidden: {\n      opacity: 0\n    },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const item = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    show: {\n      opacity: 1,\n      y: 0\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 12\n    }, this);\n  }\n  const currentItems = activeTab === 'gallery' ? images : xrays;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PatientNav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: [notification.show && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: -20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            exit: {\n              opacity: 0,\n              y: -20\n            },\n            className: `mb-4 p-4 rounded-lg ${notification.type === 'success' ? 'bg-[#28A745]/20 text-[#28A745]' : 'bg-red-100 text-red-800'}`,\n            children: notification.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this), error && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            className: \"mb-6 p-4 bg-red-100 text-[#333333] rounded-lg\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\",\n                  children: \"Patient Gallery\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600\",\n                  children: \"Manage patient images and X-rays\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: triggerFileInput,\n                className: \"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-2.5 rounded-full font-medium hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaPlus, {\n                  className: \"h-5 w-5 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 19\n                }, this), \"Add \", activeTab === 'gallery' ? 'Images' : 'X-rays']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                ref: fileInputRef,\n                onChange: handleImageUpload,\n                className: \"hidden\",\n                accept: \"image/*\",\n                multiple: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-center mb-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-full p-1 shadow-md flex\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveTab('gallery'),\n                  className: `px-6 py-2 rounded-full flex items-center transition-all duration-300 ${activeTab === 'gallery' ? 'bg-[#0077B6] text-white shadow-md' : 'text-gray-700 hover:bg-gray-100'}`,\n                  children: [/*#__PURE__*/_jsxDEV(FaImages, {\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 21\n                  }, this), \"Gallery\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveTab('xray'),\n                  className: `px-6 py-2 rounded-full flex items-center transition-all duration-300 ${activeTab === 'xray' ? 'bg-[#20B2AA] text-white shadow-md' : 'text-gray-700 hover:bg-gray-100'}`,\n                  children: [/*#__PURE__*/_jsxDEV(FaXRay, {\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 21\n                  }, this), \"X-Rays\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this), currentItems.length === 0 ? /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0\n              },\n              animate: {\n                opacity: 1\n              },\n              className: \"p-8 text-center bg-white rounded-xl shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-auto h-16 w-16 text-gray-400 mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-bold text-gray-900 mb-2\",\n                children: [\"No \", activeTab === 'gallery' ? 'images' : 'X-rays', \" found\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500\",\n                children: [\"Get started by adding new \", activeTab === 'gallery' ? 'gallery images' : 'X-ray images', \".\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: container,\n              initial: \"hidden\",\n              animate: \"show\",\n              className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\",\n              children: currentItems.map(item => /*#__PURE__*/_jsxDEV(motion.div, {\n                variants: item,\n                className: \"bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: item.url.startsWith('http') ? item.url : `http://localhost:5000/${item.url}`,\n                    alt: activeTab === 'gallery' ? 'Gallery' : 'X-Ray',\n                    className: `w-full h-48 ${activeTab === 'gallery' ? 'object-cover' : 'object-contain bg-black'} cursor-pointer`,\n                    onClick: () => openImageModal(item)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => openImageModal(item),\n                      className: \"bg-white text-[#0077B6] p-2 rounded-full mx-1 hover:bg-[#0077B6]/10\",\n                      children: /*#__PURE__*/_jsxDEV(FaPlus, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 363,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 359,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => confirmDeleteItem(item),\n                      className: \"bg-white text-red-600 p-2 rounded-full mx-1 hover:bg-red-50\",\n                      children: /*#__PURE__*/_jsxDEV(FaTrash, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 369,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 365,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-gray-500\",\n                      children: new Date(item.date).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        setEditingNote(item._id);\n                        setCurrentNote(item.note || '');\n                      },\n                      className: \"text-[#0077B6] hover:text-[#20B2AA] text-sm font-medium flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(FaEdit, {\n                        className: \"h-3 w-3 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 385,\n                        columnNumber: 29\n                      }, this), item.note ? 'Edit Note' : 'Add Note']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 378,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 25\n                  }, this), editingNote === item._id ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                      value: currentNote,\n                      onChange: e => setCurrentNote(e.target.value),\n                      className: \"w-full border border-gray-300 rounded-lg p-2 text-sm focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\",\n                      rows: \"2\",\n                      placeholder: `Add a note about this ${activeTab === 'gallery' ? 'image' : 'X-ray'}...`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 391,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-end gap-2 mt-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => setEditingNote(null),\n                        className: \"text-sm text-gray-600 hover:text-gray-800 px-2 py-1\",\n                        children: \"Cancel\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 399,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleAddNote(item._id),\n                        className: \"bg-[#0077B6] text-white px-3 py-1 rounded-md text-sm hover:bg-[#0077B6]/80\",\n                        children: \"Save\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 405,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 398,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 27\n                  }, this) : item.note && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-2 text-sm text-gray-700\",\n                    children: item.note\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 40\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 23\n                }, this)]\n              }, item._id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this), showImageModal && selectedImage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative max-w-4xl w-full\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowImageModal(false),\n          className: \"absolute top-4 right-4 bg-white rounded-full p-2 text-gray-800 hover:bg-gray-200\",\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: selectedImage.url.startsWith('http') ? selectedImage.url : `http://localhost:5000/${selectedImage.url}`,\n          alt: activeTab === 'gallery' ? 'Gallery Full View' : 'X-Ray Full View',\n          className: `w-full max-h-[80vh] ${activeTab === 'gallery' ? 'object-contain' : 'object-contain bg-black'} rounded-lg`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-4 rounded-b-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: new Date(selectedImage.date).toLocaleDateString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 15\n          }, this), selectedImage.note && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-gray-700\",\n            children: selectedImage.note\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 flex justify-end\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: selectedImage.url.startsWith('http') ? selectedImage.url : `http://localhost:5000/${selectedImage.url}`,\n              download: true,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              className: \"flex items-center text-[#0077B6] hover:text-[#20B2AA]\",\n              children: [/*#__PURE__*/_jsxDEV(FaDownload, {\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 19\n              }, this), \"Download\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 431,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 430,\n      columnNumber: 9\n    }, this), showDeleteModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        className: \"bg-white rounded-xl w-full max-w-md p-6 shadow-xl\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold text-gray-800 mb-4\",\n          children: \"Confirm Deletion\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-6\",\n          children: [\"Are you sure you want to delete this \", activeTab === 'gallery' ? 'image' : 'X-ray', \"? This action cannot be undone.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setShowDeleteModal(false);\n              setItemToDelete(null);\n            },\n            className: \"px-4 py-2 text-gray-700 hover:text-gray-900\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleDeleteItem,\n            className: \"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700\",\n            children: \"Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 470,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 469,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 225,\n    columnNumber: 5\n  }, this);\n};\n_s(Gallery, \"2NPNIJiOxV33iyEE/wBQC8p0OOM=\", false, function () {\n  return [useAuth, useParams];\n});\n_c = Gallery;\nexport default Gallery;\nvar _c;\n$RefreshReg$(_c, \"Gallery\");", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "<PERSON><PERSON><PERSON>", "Sidebar", "PatientNav", "axios", "useParams", "useAuth", "motion", "FaImages", "FaXRay", "FaEdit", "FaTrash", "FaPlus", "FaDownload", "Loader", "jsxDEV", "_jsxDEV", "Gallery", "_s", "token", "sidebarOpen", "setSidebarOpen", "images", "setImages", "xrays", "setXrays", "editingNote", "setEditingNote", "currentNote", "setCurrentNote", "loading", "setLoading", "error", "setError", "selectedImage", "setSelectedImage", "showImageModal", "setShowImageModal", "showDeleteModal", "setShowDeleteModal", "itemToDelete", "setItemToDelete", "activeTab", "setActiveTab", "notification", "setNotification", "show", "message", "type", "fileInputRef", "nationalId", "fetchData", "response", "get", "process", "env", "REACT_APP_API_URL", "headers", "Authorization", "data", "galleryImages", "err", "_err$response", "_err$response$data", "console", "openImageModal", "image", "handleImageUpload", "e", "files", "target", "length", "formData", "FormData", "Array", "from", "for<PERSON>ach", "file", "append", "endpoint", "post", "setTimeout", "_err$response2", "_err$response2$data", "handleAddNote", "id", "trim", "payload", "imageId", "note", "xrayId", "put", "map", "img", "_id", "xray", "_err$response3", "_err$response3$data", "confirmDeleteItem", "item", "handleDeleteItem", "delete", "filter", "_err$response4", "_err$response4$data", "triggerFileInput", "current", "click", "container", "hidden", "opacity", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "y", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "currentItems", "className", "children", "isOpen", "setIsOpen", "toggleSidebar", "div", "initial", "animate", "exit", "duration", "button", "whileHover", "scale", "whileTap", "onClick", "ref", "onChange", "accept", "multiple", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "variants", "src", "url", "startsWith", "alt", "Date", "date", "toLocaleDateString", "value", "rows", "placeholder", "href", "download", "rel", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/student/Gallery.jsx"], "sourcesContent": ["import { useState, useEffect, useRef } from 'react';\r\nimport Navbar from './Navbar';\r\nimport Sidebar from './Sidebar';\r\nimport PatientNav from './PatientNav';\r\nimport axios from 'axios';\r\nimport { useParams } from 'react-router-dom';\r\nimport { useAuth } from '../context/AuthContext';\r\nimport { motion } from 'framer-motion';\r\nimport { FaImages, FaXRay, FaEdit, FaTrash, FaPlus, FaDownload } from 'react-icons/fa';\r\nimport Loader from '../components/Loader';\r\n\r\nconst Gallery = () => {\r\n  const { token } = useAuth();\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const [images, setImages] = useState([]);\r\n  const [xrays, setXrays] = useState([]);\r\n  const [editingNote, setEditingNote] = useState(null);\r\n  const [currentNote, setCurrentNote] = useState('');\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [selectedImage, setSelectedImage] = useState(null);\r\n  const [showImageModal, setShowImageModal] = useState(false);\r\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\r\n  const [itemToDelete, setItemToDelete] = useState(null);\r\n  const [activeTab, setActiveTab] = useState('gallery'); // 'gallery' or 'xray'\r\n  const [notification, setNotification] = useState({ show: false, message: '', type: 'success' });\r\n  const fileInputRef = useRef(null);\r\n  const { nationalId } = useParams();\r\n\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/patients/${nationalId}`, {\r\n          headers: { Authorization: `Bearer ${token}` },\r\n        });\r\n\r\n        if (response.data) {\r\n          setImages(response.data.galleryImages || []);\r\n          setXrays(response.data.xrays || []);\r\n        } else {\r\n          setError('No data found for this patient');\r\n        }\r\n      } catch (err) {\r\n        console.error('Error fetching data:', err);\r\n        setError(err.response?.data?.message || 'Failed to load data');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    if (token && nationalId) {\r\n      fetchData();\r\n    }\r\n  }, [nationalId, token]);\r\n\r\n  const openImageModal = (image) => {\r\n    setSelectedImage(image);\r\n    setShowImageModal(true);\r\n  };\r\n\r\n  const handleImageUpload = async (e) => {\r\n    const files = e.target.files;\r\n    if (!files || files.length === 0) return;\r\n\r\n    const formData = new FormData();\r\n    Array.from(files).forEach(file => {\r\n      if (activeTab === 'gallery') {\r\n        formData.append('galleryImages', file);\r\n      } else {\r\n        formData.append('xrays', file);\r\n      }\r\n    });\r\n\r\n    try {\r\n      setLoading(true);\r\n      const endpoint = activeTab === 'gallery'\r\n        ? `${process.env.REACT_APP_API_URL}/api/patients/${nationalId}/gallery`\r\n        : `${process.env.REACT_APP_API_URL}/api/patients/${nationalId}/xrays`;\r\n\r\n      const response = await axios.post(\r\n        endpoint,\r\n        formData,\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            'Content-Type': 'multipart/form-data'\r\n          },\r\n        }\r\n      );\r\n\r\n      if (response.data) {\r\n        if (activeTab === 'gallery') {\r\n          setImages(response.data);\r\n        } else {\r\n          setXrays(response.data);\r\n        }\r\n\r\n        // Show success notification\r\n        setNotification({\r\n          show: true,\r\n          message: `${activeTab === 'gallery' ? 'Images' : 'X-rays'} uploaded successfully!`,\r\n          type: 'success'\r\n        });\r\n\r\n        // Hide notification after 3 seconds\r\n        setTimeout(() => {\r\n          setNotification({ show: false, message: '', type: 'success' });\r\n        }, 3000);\r\n      }\r\n    } catch (err) {\r\n      console.error('Upload error:', err);\r\n      setError(err.response?.data?.message || `Failed to upload ${activeTab === 'gallery' ? 'images' : 'X-rays'}`);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleAddNote = async (id) => {\r\n    if (!currentNote.trim()) {\r\n      setEditingNote(null);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const endpoint = activeTab === 'gallery'\r\n        ? `http://localhost:5000/api/patients/${nationalId}/gallery/note`\r\n        : `http://localhost:5000/api/patients/${nationalId}/xrays/note`;\r\n\r\n      const payload = activeTab === 'gallery'\r\n        ? { imageId: id, note: currentNote }\r\n        : { xrayId: id, note: currentNote };\r\n\r\n      const response = await axios.put(\r\n        endpoint,\r\n        payload,\r\n        { headers: { Authorization: `Bearer ${token}` } }\r\n      );\r\n\r\n      if (response.data) {\r\n        if (activeTab === 'gallery') {\r\n          setImages(images.map(img => img._id === id ? { ...img, note: currentNote } : img));\r\n        } else {\r\n          setXrays(xrays.map(xray => xray._id === id ? { ...xray, note: currentNote } : xray));\r\n        }\r\n      }\r\n\r\n      setEditingNote(null);\r\n      setCurrentNote('');\r\n    } catch (err) {\r\n      console.error('Note update error:', err);\r\n      setError(err.response?.data?.message || 'Failed to update note');\r\n    }\r\n  };\r\n\r\n  const confirmDeleteItem = (item) => {\r\n    setItemToDelete(item);\r\n    setShowDeleteModal(true);\r\n  };\r\n\r\n  const handleDeleteItem = async () => {\r\n    if (!itemToDelete) return;\r\n\r\n    try {\r\n      const endpoint = activeTab === 'gallery'\r\n        ? `http://localhost:5000/api/patients/${nationalId}/gallery/${itemToDelete._id}`\r\n        : `http://localhost:5000/api/patients/${nationalId}/xrays/${itemToDelete._id}`;\r\n\r\n      await axios.delete(endpoint, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n\r\n      if (activeTab === 'gallery') {\r\n        setImages(images.filter(img => img._id !== itemToDelete._id));\r\n      } else {\r\n        setXrays(xrays.filter(xray => xray._id !== itemToDelete._id));\r\n      }\r\n\r\n      setShowDeleteModal(false);\r\n      setItemToDelete(null);\r\n\r\n      // Show success notification\r\n      setNotification({\r\n        show: true,\r\n        message: `${activeTab === 'gallery' ? 'Image' : 'X-ray'} deleted successfully!`,\r\n        type: 'success'\r\n      });\r\n\r\n      // Hide notification after 3 seconds\r\n      setTimeout(() => {\r\n        setNotification({ show: false, message: '', type: 'success' });\r\n      }, 3000);\r\n    } catch (err) {\r\n      console.error('Delete error:', err);\r\n      setError(err.response?.data?.message || `Failed to delete ${activeTab === 'gallery' ? 'image' : 'X-ray'}`);\r\n      setShowDeleteModal(false);\r\n    }\r\n  };\r\n\r\n  const triggerFileInput = () => fileInputRef.current.click();\r\n\r\n  // Animation variants\r\n  const container = {\r\n    hidden: { opacity: 0 },\r\n    show: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1,\r\n      },\r\n    },\r\n  };\r\n\r\n  const item = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    show: { opacity: 1, y: 0 },\r\n  };\r\n\r\n  if (loading) {\r\n    return <Loader />;\r\n  }\r\n\r\n  const currentItems = activeTab === 'gallery' ? images : xrays;\r\n\r\n  return (\r\n    <div className=\"flex h-screen bg-gray-50\">\r\n      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\r\n        <PatientNav />\r\n        <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white\">\r\n          <div className=\"max-w-7xl mx-auto\">\r\n            {notification.show && (\r\n              <motion.div\r\n                initial={{ opacity: 0, y: -20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                exit={{ opacity: 0, y: -20 }}\r\n                className={`mb-4 p-4 rounded-lg ${\r\n                  notification.type === 'success' ? 'bg-[#28A745]/20 text-[#28A745]' : 'bg-red-100 text-red-800'\r\n                }`}\r\n              >\r\n                {notification.message}\r\n              </motion.div>\r\n            )}\r\n\r\n            {error && (\r\n              <motion.div\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                className=\"mb-6 p-4 bg-red-100 text-[#333333] rounded-lg\"\r\n              >\r\n                {error}\r\n              </motion.div>\r\n            )}\r\n\r\n            <motion.div\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              transition={{ duration: 0.5 }}\r\n            >\r\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\">\r\n                <div>\r\n                  <h1 className=\"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\">\r\n                    Patient Gallery\r\n                  </h1>\r\n                  <p className=\"text-gray-600\">Manage patient images and X-rays</p>\r\n                </div>\r\n                <motion.button\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                  onClick={triggerFileInput}\r\n                  className=\"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-2.5 rounded-full font-medium hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\"\r\n                >\r\n                  <FaPlus className=\"h-5 w-5 mr-2\" />\r\n                  Add {activeTab === 'gallery' ? 'Images' : 'X-rays'}\r\n                </motion.button>\r\n                <input\r\n                  type=\"file\"\r\n                  ref={fileInputRef}\r\n                  onChange={handleImageUpload}\r\n                  className=\"hidden\"\r\n                  accept=\"image/*\"\r\n                  multiple\r\n                />\r\n              </div>\r\n\r\n              {/* Tab Switcher */}\r\n              <div className=\"flex justify-center mb-8\">\r\n                <div className=\"bg-white rounded-full p-1 shadow-md flex\">\r\n                  <button\r\n                    onClick={() => setActiveTab('gallery')}\r\n                    className={`px-6 py-2 rounded-full flex items-center transition-all duration-300 ${\r\n                      activeTab === 'gallery'\r\n                        ? 'bg-[#0077B6] text-white shadow-md'\r\n                        : 'text-gray-700 hover:bg-gray-100'\r\n                    }`}\r\n                  >\r\n                    <FaImages className=\"mr-2\" />\r\n                    Gallery\r\n                  </button>\r\n                  <button\r\n                    onClick={() => setActiveTab('xray')}\r\n                    className={`px-6 py-2 rounded-full flex items-center transition-all duration-300 ${\r\n                      activeTab === 'xray'\r\n                        ? 'bg-[#20B2AA] text-white shadow-md'\r\n                        : 'text-gray-700 hover:bg-gray-100'\r\n                    }`}\r\n                  >\r\n                    <FaXRay className=\"mr-2\" />\r\n                    X-Rays\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              {currentItems.length === 0 ? (\r\n                <motion.div\r\n                  initial={{ opacity: 0 }}\r\n                  animate={{ opacity: 1 }}\r\n                  className=\"p-8 text-center bg-white rounded-xl shadow-sm\"\r\n                >\r\n                  <div className=\"mx-auto h-16 w-16 text-gray-400 mb-4\">\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      stroke=\"currentColor\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth={2}\r\n                        d=\"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                      />\r\n                    </svg>\r\n                  </div>\r\n                  <h3 className=\"text-lg font-bold text-gray-900 mb-2\">No {activeTab === 'gallery' ? 'images' : 'X-rays'} found</h3>\r\n                  <p className=\"text-gray-500\">Get started by adding new {activeTab === 'gallery' ? 'gallery images' : 'X-ray images'}.</p>\r\n                </motion.div>\r\n              ) : (\r\n                <motion.div\r\n                  variants={container}\r\n                  initial=\"hidden\"\r\n                  animate=\"show\"\r\n                  className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\"\r\n                >\r\n                  {currentItems.map((item) => (\r\n                    <motion.div\r\n                      key={item._id}\r\n                      variants={item}\r\n                      className=\"bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden\"\r\n                    >\r\n                      <div className=\"relative group\">\r\n                        <img\r\n                          src={item.url.startsWith('http') ? item.url : `http://localhost:5000/${item.url}`}\r\n                          alt={activeTab === 'gallery' ? 'Gallery' : 'X-Ray'}\r\n                          className={`w-full h-48 ${activeTab === 'gallery' ? 'object-cover' : 'object-contain bg-black'} cursor-pointer`}\r\n                          onClick={() => openImageModal(item)}\r\n                        />\r\n                        <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100\">\r\n                          <button\r\n                            onClick={() => openImageModal(item)}\r\n                            className=\"bg-white text-[#0077B6] p-2 rounded-full mx-1 hover:bg-[#0077B6]/10\"\r\n                          >\r\n                            <FaPlus className=\"h-4 w-4\" />\r\n                          </button>\r\n                          <button\r\n                            onClick={() => confirmDeleteItem(item)}\r\n                            className=\"bg-white text-red-600 p-2 rounded-full mx-1 hover:bg-red-50\"\r\n                          >\r\n                            <FaTrash className=\"h-4 w-4\" />\r\n                          </button>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"p-4\">\r\n                        <div className=\"flex justify-between items-start\">\r\n                          <span className=\"text-sm text-gray-500\">\r\n                            {new Date(item.date).toLocaleDateString()}\r\n                          </span>\r\n                          <button\r\n                            onClick={() => {\r\n                              setEditingNote(item._id);\r\n                              setCurrentNote(item.note || '');\r\n                            }}\r\n                            className=\"text-[#0077B6] hover:text-[#20B2AA] text-sm font-medium flex items-center\"\r\n                          >\r\n                            <FaEdit className=\"h-3 w-3 mr-1\" />\r\n                            {item.note ? 'Edit Note' : 'Add Note'}\r\n                          </button>\r\n                        </div>\r\n                        {editingNote === item._id ? (\r\n                          <div className=\"mt-2\">\r\n                            <textarea\r\n                              value={currentNote}\r\n                              onChange={(e) => setCurrentNote(e.target.value)}\r\n                              className=\"w-full border border-gray-300 rounded-lg p-2 text-sm focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\"\r\n                              rows=\"2\"\r\n                              placeholder={`Add a note about this ${activeTab === 'gallery' ? 'image' : 'X-ray'}...`}\r\n                            />\r\n                            <div className=\"flex justify-end gap-2 mt-2\">\r\n                              <button\r\n                                onClick={() => setEditingNote(null)}\r\n                                className=\"text-sm text-gray-600 hover:text-gray-800 px-2 py-1\"\r\n                              >\r\n                                Cancel\r\n                              </button>\r\n                              <button\r\n                                onClick={() => handleAddNote(item._id)}\r\n                                className=\"bg-[#0077B6] text-white px-3 py-1 rounded-md text-sm hover:bg-[#0077B6]/80\"\r\n                              >\r\n                                Save\r\n                              </button>\r\n                            </div>\r\n                          </div>\r\n                        ) : (\r\n                          item.note && <p className=\"mt-2 text-sm text-gray-700\">{item.note}</p>\r\n                        )}\r\n                      </div>\r\n                    </motion.div>\r\n                  ))}\r\n                </motion.div>\r\n              )}\r\n            </motion.div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n\r\n\r\n\r\n      {/* Full-size image modal */}\r\n      {showImageModal && selectedImage && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4\">\r\n          <div className=\"relative max-w-4xl w-full\">\r\n            <button\r\n              onClick={() => setShowImageModal(false)}\r\n              className=\"absolute top-4 right-4 bg-white rounded-full p-2 text-gray-800 hover:bg-gray-200\"\r\n            >\r\n              ✕\r\n            </button>\r\n            <img\r\n              src={selectedImage.url.startsWith('http') ? selectedImage.url : `http://localhost:5000/${selectedImage.url}`}\r\n              alt={activeTab === 'gallery' ? 'Gallery Full View' : 'X-Ray Full View'}\r\n              className={`w-full max-h-[80vh] ${activeTab === 'gallery' ? 'object-contain' : 'object-contain bg-black'} rounded-lg`}\r\n            />\r\n            <div className=\"bg-white p-4 rounded-b-lg\">\r\n              <p className=\"text-sm text-gray-500\">\r\n                {new Date(selectedImage.date).toLocaleDateString()}\r\n              </p>\r\n              {selectedImage.note && (\r\n                <p className=\"mt-2 text-gray-700\">{selectedImage.note}</p>\r\n              )}\r\n              <div className=\"mt-4 flex justify-end\">\r\n                <a\r\n                  href={selectedImage.url.startsWith('http') ? selectedImage.url : `http://localhost:5000/${selectedImage.url}`}\r\n                  download\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"flex items-center text-[#0077B6] hover:text-[#20B2AA]\"\r\n                >\r\n                  <FaDownload className=\"mr-2\" />\r\n                  Download\r\n                </a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Delete confirmation modal */}\r\n      {showDeleteModal && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n          <motion.div\r\n            initial={{ scale: 0.9, opacity: 0 }}\r\n            animate={{ scale: 1, opacity: 1 }}\r\n            className=\"bg-white rounded-xl w-full max-w-md p-6 shadow-xl\"\r\n          >\r\n            <h3 className=\"text-xl font-semibold text-gray-800 mb-4\">Confirm Deletion</h3>\r\n            <p className=\"text-gray-600 mb-6\">\r\n              Are you sure you want to delete this {activeTab === 'gallery' ? 'image' : 'X-ray'}? This action cannot be undone.\r\n            </p>\r\n            <div className=\"flex justify-end space-x-4\">\r\n              <button\r\n                onClick={() => {\r\n                  setShowDeleteModal(false);\r\n                  setItemToDelete(null);\r\n                }}\r\n                className=\"px-4 py-2 text-gray-700 hover:text-gray-900\"\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                onClick={handleDeleteItem}\r\n                className=\"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700\"\r\n              >\r\n                Delete\r\n              </button>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Gallery;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnD,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,UAAU,QAAQ,gBAAgB;AACtF,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM;IAAEC;EAAM,CAAC,GAAGb,OAAO,CAAC,CAAC;EAC3B,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwB,MAAM,EAAEC,SAAS,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACsC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC;IAAEgD,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAU,CAAC,CAAC;EAC/F,MAAMC,YAAY,GAAGjD,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM;IAAEkD;EAAW,CAAC,GAAG7C,SAAS,CAAC,CAAC;EAElCN,SAAS,CAAC,MAAM;IACd,MAAMoD,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFpB,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMqB,QAAQ,GAAG,MAAMhD,KAAK,CAACiD,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,iBAAiBN,UAAU,EAAE,EAAE;UAC9FO,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAUvC,KAAK;UAAG;QAC9C,CAAC,CAAC;QAEF,IAAIiC,QAAQ,CAACO,IAAI,EAAE;UACjBpC,SAAS,CAAC6B,QAAQ,CAACO,IAAI,CAACC,aAAa,IAAI,EAAE,CAAC;UAC5CnC,QAAQ,CAAC2B,QAAQ,CAACO,IAAI,CAACnC,KAAK,IAAI,EAAE,CAAC;QACrC,CAAC,MAAM;UACLS,QAAQ,CAAC,gCAAgC,CAAC;QAC5C;MACF,CAAC,CAAC,OAAO4B,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,kBAAA;QACZC,OAAO,CAAChC,KAAK,CAAC,sBAAsB,EAAE6B,GAAG,CAAC;QAC1C5B,QAAQ,CAAC,EAAA6B,aAAA,GAAAD,GAAG,CAACT,QAAQ,cAAAU,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcH,IAAI,cAAAI,kBAAA,uBAAlBA,kBAAA,CAAoBhB,OAAO,KAAI,qBAAqB,CAAC;MAChE,CAAC,SAAS;QACRhB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIZ,KAAK,IAAI+B,UAAU,EAAE;MACvBC,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACD,UAAU,EAAE/B,KAAK,CAAC,CAAC;EAEvB,MAAM8C,cAAc,GAAIC,KAAK,IAAK;IAChC/B,gBAAgB,CAAC+B,KAAK,CAAC;IACvB7B,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM8B,iBAAiB,GAAG,MAAOC,CAAC,IAAK;IACrC,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5B,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;IAElC,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BC,KAAK,CAACC,IAAI,CAACN,KAAK,CAAC,CAACO,OAAO,CAACC,IAAI,IAAI;MAChC,IAAInC,SAAS,KAAK,SAAS,EAAE;QAC3B8B,QAAQ,CAACM,MAAM,CAAC,eAAe,EAAED,IAAI,CAAC;MACxC,CAAC,MAAM;QACLL,QAAQ,CAACM,MAAM,CAAC,OAAO,EAAED,IAAI,CAAC;MAChC;IACF,CAAC,CAAC;IAEF,IAAI;MACF9C,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMgD,QAAQ,GAAGrC,SAAS,KAAK,SAAS,GACpC,GAAGY,OAAO,CAACC,GAAG,CAACC,iBAAiB,iBAAiBN,UAAU,UAAU,GACrE,GAAGI,OAAO,CAACC,GAAG,CAACC,iBAAiB,iBAAiBN,UAAU,QAAQ;MAEvE,MAAME,QAAQ,GAAG,MAAMhD,KAAK,CAAC4E,IAAI,CAC/BD,QAAQ,EACRP,QAAQ,EACR;QACEf,OAAO,EAAE;UACPC,aAAa,EAAE,UAAUvC,KAAK,EAAE;UAChC,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,IAAIiC,QAAQ,CAACO,IAAI,EAAE;QACjB,IAAIjB,SAAS,KAAK,SAAS,EAAE;UAC3BnB,SAAS,CAAC6B,QAAQ,CAACO,IAAI,CAAC;QAC1B,CAAC,MAAM;UACLlC,QAAQ,CAAC2B,QAAQ,CAACO,IAAI,CAAC;QACzB;;QAEA;QACAd,eAAe,CAAC;UACdC,IAAI,EAAE,IAAI;UACVC,OAAO,EAAE,GAAGL,SAAS,KAAK,SAAS,GAAG,QAAQ,GAAG,QAAQ,yBAAyB;UAClFM,IAAI,EAAE;QACR,CAAC,CAAC;;QAEF;QACAiC,UAAU,CAAC,MAAM;UACfpC,eAAe,CAAC;YAAEC,IAAI,EAAE,KAAK;YAAEC,OAAO,EAAE,EAAE;YAAEC,IAAI,EAAE;UAAU,CAAC,CAAC;QAChE,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,CAAC,OAAOa,GAAG,EAAE;MAAA,IAAAqB,cAAA,EAAAC,mBAAA;MACZnB,OAAO,CAAChC,KAAK,CAAC,eAAe,EAAE6B,GAAG,CAAC;MACnC5B,QAAQ,CAAC,EAAAiD,cAAA,GAAArB,GAAG,CAACT,QAAQ,cAAA8B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcvB,IAAI,cAAAwB,mBAAA,uBAAlBA,mBAAA,CAAoBpC,OAAO,KAAI,oBAAoBL,SAAS,KAAK,SAAS,GAAG,QAAQ,GAAG,QAAQ,EAAE,CAAC;IAC9G,CAAC,SAAS;MACRX,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqD,aAAa,GAAG,MAAOC,EAAE,IAAK;IAClC,IAAI,CAACzD,WAAW,CAAC0D,IAAI,CAAC,CAAC,EAAE;MACvB3D,cAAc,CAAC,IAAI,CAAC;MACpB;IACF;IAEA,IAAI;MACF,MAAMoD,QAAQ,GAAGrC,SAAS,KAAK,SAAS,GACpC,sCAAsCQ,UAAU,eAAe,GAC/D,sCAAsCA,UAAU,aAAa;MAEjE,MAAMqC,OAAO,GAAG7C,SAAS,KAAK,SAAS,GACnC;QAAE8C,OAAO,EAAEH,EAAE;QAAEI,IAAI,EAAE7D;MAAY,CAAC,GAClC;QAAE8D,MAAM,EAAEL,EAAE;QAAEI,IAAI,EAAE7D;MAAY,CAAC;MAErC,MAAMwB,QAAQ,GAAG,MAAMhD,KAAK,CAACuF,GAAG,CAC9BZ,QAAQ,EACRQ,OAAO,EACP;QAAE9B,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUvC,KAAK;QAAG;MAAE,CAClD,CAAC;MAED,IAAIiC,QAAQ,CAACO,IAAI,EAAE;QACjB,IAAIjB,SAAS,KAAK,SAAS,EAAE;UAC3BnB,SAAS,CAACD,MAAM,CAACsE,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,GAAG,KAAKT,EAAE,GAAG;YAAE,GAAGQ,GAAG;YAAEJ,IAAI,EAAE7D;UAAY,CAAC,GAAGiE,GAAG,CAAC,CAAC;QACpF,CAAC,MAAM;UACLpE,QAAQ,CAACD,KAAK,CAACoE,GAAG,CAACG,IAAI,IAAIA,IAAI,CAACD,GAAG,KAAKT,EAAE,GAAG;YAAE,GAAGU,IAAI;YAAEN,IAAI,EAAE7D;UAAY,CAAC,GAAGmE,IAAI,CAAC,CAAC;QACtF;MACF;MAEApE,cAAc,CAAC,IAAI,CAAC;MACpBE,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC,OAAOgC,GAAG,EAAE;MAAA,IAAAmC,cAAA,EAAAC,mBAAA;MACZjC,OAAO,CAAChC,KAAK,CAAC,oBAAoB,EAAE6B,GAAG,CAAC;MACxC5B,QAAQ,CAAC,EAAA+D,cAAA,GAAAnC,GAAG,CAACT,QAAQ,cAAA4C,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcrC,IAAI,cAAAsC,mBAAA,uBAAlBA,mBAAA,CAAoBlD,OAAO,KAAI,uBAAuB,CAAC;IAClE;EACF,CAAC;EAED,MAAMmD,iBAAiB,GAAIC,IAAI,IAAK;IAClC1D,eAAe,CAAC0D,IAAI,CAAC;IACrB5D,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM6D,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAC5D,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMuC,QAAQ,GAAGrC,SAAS,KAAK,SAAS,GACpC,sCAAsCQ,UAAU,YAAYV,YAAY,CAACsD,GAAG,EAAE,GAC9E,sCAAsC5C,UAAU,UAAUV,YAAY,CAACsD,GAAG,EAAE;MAEhF,MAAM1F,KAAK,CAACiG,MAAM,CAACtB,QAAQ,EAAE;QAC3BtB,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUvC,KAAK;QAAG;MAC9C,CAAC,CAAC;MAEF,IAAIuB,SAAS,KAAK,SAAS,EAAE;QAC3BnB,SAAS,CAACD,MAAM,CAACgF,MAAM,CAACT,GAAG,IAAIA,GAAG,CAACC,GAAG,KAAKtD,YAAY,CAACsD,GAAG,CAAC,CAAC;MAC/D,CAAC,MAAM;QACLrE,QAAQ,CAACD,KAAK,CAAC8E,MAAM,CAACP,IAAI,IAAIA,IAAI,CAACD,GAAG,KAAKtD,YAAY,CAACsD,GAAG,CAAC,CAAC;MAC/D;MAEAvD,kBAAkB,CAAC,KAAK,CAAC;MACzBE,eAAe,CAAC,IAAI,CAAC;;MAErB;MACAI,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,GAAGL,SAAS,KAAK,SAAS,GAAG,OAAO,GAAG,OAAO,wBAAwB;QAC/EM,IAAI,EAAE;MACR,CAAC,CAAC;;MAEF;MACAiC,UAAU,CAAC,MAAM;QACfpC,eAAe,CAAC;UAAEC,IAAI,EAAE,KAAK;UAAEC,OAAO,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAU,CAAC,CAAC;MAChE,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOa,GAAG,EAAE;MAAA,IAAA0C,cAAA,EAAAC,mBAAA;MACZxC,OAAO,CAAChC,KAAK,CAAC,eAAe,EAAE6B,GAAG,CAAC;MACnC5B,QAAQ,CAAC,EAAAsE,cAAA,GAAA1C,GAAG,CAACT,QAAQ,cAAAmD,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc5C,IAAI,cAAA6C,mBAAA,uBAAlBA,mBAAA,CAAoBzD,OAAO,KAAI,oBAAoBL,SAAS,KAAK,SAAS,GAAG,OAAO,GAAG,OAAO,EAAE,CAAC;MAC1GH,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAMkE,gBAAgB,GAAGA,CAAA,KAAMxD,YAAY,CAACyD,OAAO,CAACC,KAAK,CAAC,CAAC;;EAE3D;EACA,MAAMC,SAAS,GAAG;IAChBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBhE,IAAI,EAAE;MACJgE,OAAO,EAAE,CAAC;MACVC,UAAU,EAAE;QACVC,eAAe,EAAE;MACnB;IACF;EACF,CAAC;EAED,MAAMb,IAAI,GAAG;IACXU,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEG,CAAC,EAAE;IAAG,CAAC;IAC7BnE,IAAI,EAAE;MAAEgE,OAAO,EAAE,CAAC;MAAEG,CAAC,EAAE;IAAE;EAC3B,CAAC;EAED,IAAInF,OAAO,EAAE;IACX,oBAAOd,OAAA,CAACF,MAAM;MAAAoG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnB;EAEA,MAAMC,YAAY,GAAG5E,SAAS,KAAK,SAAS,GAAGpB,MAAM,GAAGE,KAAK;EAE7D,oBACER,OAAA;IAAKuG,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvCxG,OAAA,CAACd,OAAO;MAACuH,MAAM,EAAErG,WAAY;MAACsG,SAAS,EAAErG;IAAe;MAAA6F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC3DrG,OAAA;MAAKuG,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDxG,OAAA,CAACf,MAAM;QAAC0H,aAAa,EAAEA,CAAA,KAAMtG,cAAc,CAAC,CAACD,WAAW;MAAE;QAAA8F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7DrG,OAAA,CAACb,UAAU;QAAA+G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACdrG,OAAA;QAAMuG,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAC7FxG,OAAA;UAAKuG,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAC/B5E,YAAY,CAACE,IAAI,iBAChB9B,OAAA,CAACT,MAAM,CAACqH,GAAG;YACTC,OAAO,EAAE;cAAEf,OAAO,EAAE,CAAC;cAAEG,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCa,OAAO,EAAE;cAAEhB,OAAO,EAAE,CAAC;cAAEG,CAAC,EAAE;YAAE,CAAE;YAC9Bc,IAAI,EAAE;cAAEjB,OAAO,EAAE,CAAC;cAAEG,CAAC,EAAE,CAAC;YAAG,CAAE;YAC7BM,SAAS,EAAE,uBACT3E,YAAY,CAACI,IAAI,KAAK,SAAS,GAAG,gCAAgC,GAAG,yBAAyB,EAC7F;YAAAwE,QAAA,EAEF5E,YAAY,CAACG;UAAO;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CACb,EAEArF,KAAK,iBACJhB,OAAA,CAACT,MAAM,CAACqH,GAAG;YACTC,OAAO,EAAE;cAAEf,OAAO,EAAE;YAAE,CAAE;YACxBgB,OAAO,EAAE;cAAEhB,OAAO,EAAE;YAAE,CAAE;YACxBS,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAExDxF;UAAK;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eAEDrG,OAAA,CAACT,MAAM,CAACqH,GAAG;YACTC,OAAO,EAAE;cAAEf,OAAO,EAAE;YAAE,CAAE;YACxBgB,OAAO,EAAE;cAAEhB,OAAO,EAAE;YAAE,CAAE;YACxBC,UAAU,EAAE;cAAEiB,QAAQ,EAAE;YAAI,CAAE;YAAAR,QAAA,gBAE9BxG,OAAA;cAAKuG,SAAS,EAAC,kFAAkF;cAAAC,QAAA,gBAC/FxG,OAAA;gBAAAwG,QAAA,gBACExG,OAAA;kBAAIuG,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAAC;gBAEnE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLrG,OAAA;kBAAGuG,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAgC;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACNrG,OAAA,CAACT,MAAM,CAAC0H,MAAM;gBACZC,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAC1BE,OAAO,EAAE5B,gBAAiB;gBAC1Bc,SAAS,EAAC,oPAAoP;gBAAAC,QAAA,gBAE9PxG,OAAA,CAACJ,MAAM;kBAAC2G,SAAS,EAAC;gBAAc;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,QAC/B,EAAC3E,SAAS,KAAK,SAAS,GAAG,QAAQ,GAAG,QAAQ;cAAA;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eAChBrG,OAAA;gBACEgC,IAAI,EAAC,MAAM;gBACXsF,GAAG,EAAErF,YAAa;gBAClBsF,QAAQ,EAAEpE,iBAAkB;gBAC5BoD,SAAS,EAAC,QAAQ;gBAClBiB,MAAM,EAAC,SAAS;gBAChBC,QAAQ;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNrG,OAAA;cAAKuG,SAAS,EAAC,0BAA0B;cAAAC,QAAA,eACvCxG,OAAA;gBAAKuG,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,gBACvDxG,OAAA;kBACEqH,OAAO,EAAEA,CAAA,KAAM1F,YAAY,CAAC,SAAS,CAAE;kBACvC4E,SAAS,EAAE,wEACT7E,SAAS,KAAK,SAAS,GACnB,mCAAmC,GACnC,iCAAiC,EACpC;kBAAA8E,QAAA,gBAEHxG,OAAA,CAACR,QAAQ;oBAAC+G,SAAS,EAAC;kBAAM;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,WAE/B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTrG,OAAA;kBACEqH,OAAO,EAAEA,CAAA,KAAM1F,YAAY,CAAC,MAAM,CAAE;kBACpC4E,SAAS,EAAE,wEACT7E,SAAS,KAAK,MAAM,GAChB,mCAAmC,GACnC,iCAAiC,EACpC;kBAAA8E,QAAA,gBAEHxG,OAAA,CAACP,MAAM;oBAAC8G,SAAS,EAAC;kBAAM;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAE7B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELC,YAAY,CAAC/C,MAAM,KAAK,CAAC,gBACxBvD,OAAA,CAACT,MAAM,CAACqH,GAAG;cACTC,OAAO,EAAE;gBAAEf,OAAO,EAAE;cAAE,CAAE;cACxBgB,OAAO,EAAE;gBAAEhB,OAAO,EAAE;cAAE,CAAE;cACxBS,SAAS,EAAC,+CAA+C;cAAAC,QAAA,gBAEzDxG,OAAA;gBAAKuG,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,eACnDxG,OAAA;kBACE0H,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnBC,MAAM,EAAC,cAAc;kBAAArB,QAAA,eAErBxG,OAAA;oBACE8H,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC,OAAO;oBACtBC,WAAW,EAAE,CAAE;oBACfC,CAAC,EAAC;kBAAoF;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNrG,OAAA;gBAAIuG,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,GAAC,KAAG,EAAC9E,SAAS,KAAK,SAAS,GAAG,QAAQ,GAAG,QAAQ,EAAC,QAAM;cAAA;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClHrG,OAAA;gBAAGuG,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,4BAA0B,EAAC9E,SAAS,KAAK,SAAS,GAAG,gBAAgB,GAAG,cAAc,EAAC,GAAC;cAAA;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/G,CAAC,gBAEbrG,OAAA,CAACT,MAAM,CAACqH,GAAG;cACTsB,QAAQ,EAAEtC,SAAU;cACpBiB,OAAO,EAAC,QAAQ;cAChBC,OAAO,EAAC,MAAM;cACdP,SAAS,EAAC,sDAAsD;cAAAC,QAAA,EAE/DF,YAAY,CAAC1B,GAAG,CAAEO,IAAI,iBACrBnF,OAAA,CAACT,MAAM,CAACqH,GAAG;gBAETsB,QAAQ,EAAE/C,IAAK;gBACfoB,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,gBAEhFxG,OAAA;kBAAKuG,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BxG,OAAA;oBACEmI,GAAG,EAAEhD,IAAI,CAACiD,GAAG,CAACC,UAAU,CAAC,MAAM,CAAC,GAAGlD,IAAI,CAACiD,GAAG,GAAG,yBAAyBjD,IAAI,CAACiD,GAAG,EAAG;oBAClFE,GAAG,EAAE5G,SAAS,KAAK,SAAS,GAAG,SAAS,GAAG,OAAQ;oBACnD6E,SAAS,EAAE,eAAe7E,SAAS,KAAK,SAAS,GAAG,cAAc,GAAG,yBAAyB,iBAAkB;oBAChH2F,OAAO,EAAEA,CAAA,KAAMpE,cAAc,CAACkC,IAAI;kBAAE;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,eACFrG,OAAA;oBAAKuG,SAAS,EAAC,iKAAiK;oBAAAC,QAAA,gBAC9KxG,OAAA;sBACEqH,OAAO,EAAEA,CAAA,KAAMpE,cAAc,CAACkC,IAAI,CAAE;sBACpCoB,SAAS,EAAC,qEAAqE;sBAAAC,QAAA,eAE/ExG,OAAA,CAACJ,MAAM;wBAAC2G,SAAS,EAAC;sBAAS;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACTrG,OAAA;sBACEqH,OAAO,EAAEA,CAAA,KAAMnC,iBAAiB,CAACC,IAAI,CAAE;sBACvCoB,SAAS,EAAC,6DAA6D;sBAAAC,QAAA,eAEvExG,OAAA,CAACL,OAAO;wBAAC4G,SAAS,EAAC;sBAAS;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrG,OAAA;kBAAKuG,SAAS,EAAC,KAAK;kBAAAC,QAAA,gBAClBxG,OAAA;oBAAKuG,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,gBAC/CxG,OAAA;sBAAMuG,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACpC,IAAI+B,IAAI,CAACpD,IAAI,CAACqD,IAAI,CAAC,CAACC,kBAAkB,CAAC;oBAAC;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC,CAAC,eACPrG,OAAA;sBACEqH,OAAO,EAAEA,CAAA,KAAM;wBACb1G,cAAc,CAACwE,IAAI,CAACL,GAAG,CAAC;wBACxBjE,cAAc,CAACsE,IAAI,CAACV,IAAI,IAAI,EAAE,CAAC;sBACjC,CAAE;sBACF8B,SAAS,EAAC,2EAA2E;sBAAAC,QAAA,gBAErFxG,OAAA,CAACN,MAAM;wBAAC6G,SAAS,EAAC;sBAAc;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EAClClB,IAAI,CAACV,IAAI,GAAG,WAAW,GAAG,UAAU;oBAAA;sBAAAyB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,EACL3F,WAAW,KAAKyE,IAAI,CAACL,GAAG,gBACvB9E,OAAA;oBAAKuG,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBxG,OAAA;sBACE0I,KAAK,EAAE9H,WAAY;sBACnB2G,QAAQ,EAAGnE,CAAC,IAAKvC,cAAc,CAACuC,CAAC,CAACE,MAAM,CAACoF,KAAK,CAAE;sBAChDnC,SAAS,EAAC,+GAA+G;sBACzHoC,IAAI,EAAC,GAAG;sBACRC,WAAW,EAAE,yBAAyBlH,SAAS,KAAK,SAAS,GAAG,OAAO,GAAG,OAAO;oBAAM;sBAAAwE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxF,CAAC,eACFrG,OAAA;sBAAKuG,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,gBAC1CxG,OAAA;wBACEqH,OAAO,EAAEA,CAAA,KAAM1G,cAAc,CAAC,IAAI,CAAE;wBACpC4F,SAAS,EAAC,qDAAqD;wBAAAC,QAAA,EAChE;sBAED;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACTrG,OAAA;wBACEqH,OAAO,EAAEA,CAAA,KAAMjD,aAAa,CAACe,IAAI,CAACL,GAAG,CAAE;wBACvCyB,SAAS,EAAC,4EAA4E;wBAAAC,QAAA,EACvF;sBAED;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,GAENlB,IAAI,CAACV,IAAI,iBAAIzE,OAAA;oBAAGuG,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAErB,IAAI,CAACV;kBAAI;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CACtE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GArEDlB,IAAI,CAACL,GAAG;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsEH,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAKLjF,cAAc,IAAIF,aAAa,iBAC9BlB,OAAA;MAAKuG,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7FxG,OAAA;QAAKuG,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxCxG,OAAA;UACEqH,OAAO,EAAEA,CAAA,KAAMhG,iBAAiB,CAAC,KAAK,CAAE;UACxCkF,SAAS,EAAC,kFAAkF;UAAAC,QAAA,EAC7F;QAED;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrG,OAAA;UACEmI,GAAG,EAAEjH,aAAa,CAACkH,GAAG,CAACC,UAAU,CAAC,MAAM,CAAC,GAAGnH,aAAa,CAACkH,GAAG,GAAG,yBAAyBlH,aAAa,CAACkH,GAAG,EAAG;UAC7GE,GAAG,EAAE5G,SAAS,KAAK,SAAS,GAAG,mBAAmB,GAAG,iBAAkB;UACvE6E,SAAS,EAAE,uBAAuB7E,SAAS,KAAK,SAAS,GAAG,gBAAgB,GAAG,yBAAyB;QAAc;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvH,CAAC,eACFrG,OAAA;UAAKuG,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCxG,OAAA;YAAGuG,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EACjC,IAAI+B,IAAI,CAACrH,aAAa,CAACsH,IAAI,CAAC,CAACC,kBAAkB,CAAC;UAAC;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,EACHnF,aAAa,CAACuD,IAAI,iBACjBzE,OAAA;YAAGuG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAEtF,aAAa,CAACuD;UAAI;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAC1D,eACDrG,OAAA;YAAKuG,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eACpCxG,OAAA;cACE6I,IAAI,EAAE3H,aAAa,CAACkH,GAAG,CAACC,UAAU,CAAC,MAAM,CAAC,GAAGnH,aAAa,CAACkH,GAAG,GAAG,yBAAyBlH,aAAa,CAACkH,GAAG,EAAG;cAC9GU,QAAQ;cACRxF,MAAM,EAAC,QAAQ;cACfyF,GAAG,EAAC,qBAAqB;cACzBxC,SAAS,EAAC,uDAAuD;cAAAC,QAAA,gBAEjExG,OAAA,CAACH,UAAU;gBAAC0G,SAAS,EAAC;cAAM;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAEjC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA/E,eAAe,iBACdtB,OAAA;MAAKuG,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7FxG,OAAA,CAACT,MAAM,CAACqH,GAAG;QACTC,OAAO,EAAE;UAAEM,KAAK,EAAE,GAAG;UAAErB,OAAO,EAAE;QAAE,CAAE;QACpCgB,OAAO,EAAE;UAAEK,KAAK,EAAE,CAAC;UAAErB,OAAO,EAAE;QAAE,CAAE;QAClCS,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAE7DxG,OAAA;UAAIuG,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAgB;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9ErG,OAAA;UAAGuG,SAAS,EAAC,oBAAoB;UAAAC,QAAA,GAAC,uCACK,EAAC9E,SAAS,KAAK,SAAS,GAAG,OAAO,GAAG,OAAO,EAAC,iCACpF;QAAA;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJrG,OAAA;UAAKuG,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzCxG,OAAA;YACEqH,OAAO,EAAEA,CAAA,KAAM;cACb9F,kBAAkB,CAAC,KAAK,CAAC;cACzBE,eAAe,CAAC,IAAI,CAAC;YACvB,CAAE;YACF8E,SAAS,EAAC,6CAA6C;YAAAC,QAAA,EACxD;UAED;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrG,OAAA;YACEqH,OAAO,EAAEjC,gBAAiB;YAC1BmB,SAAS,EAAC,6DAA6D;YAAAC,QAAA,EACxE;UAED;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACnG,EAAA,CAzeID,OAAO;EAAA,QACOX,OAAO,EAeFD,SAAS;AAAA;AAAA2J,EAAA,GAhB5B/I,OAAO;AA2eb,eAAeA,OAAO;AAAC,IAAA+I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}