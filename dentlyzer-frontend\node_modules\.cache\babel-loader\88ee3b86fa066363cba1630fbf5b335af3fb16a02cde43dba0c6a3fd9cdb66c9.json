{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\superadmin\\\\News.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { motion } from 'framer-motion';\nimport { FaNewspaper } from 'react-icons/fa';\nimport Navbar from '../student/Navbar';\nimport Loader from '../components/Loader';\nimport { useAuth } from '../context/AuthContext';\nimport SuperAdminSidebar from './SuperAdminSidebar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst News = () => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [news, setNews] = useState([]);\n  const [universities, setUniversities] = useState([]);\n  const [accounts, setAccounts] = useState([]);\n  const [showGeneralModal, setShowGeneralModal] = useState(false);\n  const [showSpecificModal, setShowSpecificModal] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  const {\n    user,\n    token\n  } = useAuth();\n  const [generalNewsForm, setGeneralNewsForm] = useState({\n    title: {\n      en: '',\n      ar: ''\n    },\n    content: {\n      en: '',\n      ar: ''\n    }\n  });\n  const [specificNewsForm, setSpecificNewsForm] = useState({\n    title: {\n      en: '',\n      ar: ''\n    },\n    content: {\n      en: '',\n      ar: ''\n    },\n    recipientType: '',\n    recipientId: ''\n  });\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!user || !token) {\n        setError('Please log in to view news.');\n        setLoading(false);\n        return;\n      }\n      try {\n        const config = {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        };\n        const [newsRes, universitiesRes, accountsRes] = await Promise.all([axios.get(`${process.env.REACT_APP_API_URL}/api/news`, config), axios.get(`${process.env.REACT_APP_API_URL}/api/universities`, config), axios.get(`${process.env.REACT_APP_API_URL}/api/accounts`, config)]);\n        setNews(newsRes.data || []);\n        setUniversities(universitiesRes.data || []);\n        setAccounts(accountsRes.data || []);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response3$data, _err$response4;\n        console.error('Fetch error:', ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n        const errorMessage = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.status) === 401 ? 'Unauthorized. Please log in again.' : ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || 'Failed to load data';\n        setError(errorMessage);\n        if (((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 401) {\n          navigate('/login');\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [user, token, navigate]);\n  const handleGeneralNewsSubmit = async e => {\n    e.preventDefault();\n    try {\n      const response = await axios.post('http://localhost:5000/api/news', generalNewsForm, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      setNews([...news, response.data]);\n      setShowGeneralModal(false);\n      setGeneralNewsForm({\n        title: {\n          en: '',\n          ar: ''\n        },\n        content: {\n          en: '',\n          ar: ''\n        }\n      });\n    } catch (err) {\n      var _err$response5, _err$response5$data;\n      setError(((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : (_err$response5$data = _err$response5.data) === null || _err$response5$data === void 0 ? void 0 : _err$response5$data.message) || 'Failed to send general news');\n    }\n  };\n  const handleSpecificNewsSubmit = async e => {\n    e.preventDefault();\n    try {\n      const response = await axios.post('http://localhost:5000/api/news/specific', {\n        ...specificNewsForm,\n        recipientType: specificNewsForm.recipientType,\n        recipientId: specificNewsForm.recipientId\n      }, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      setNews([...news, response.data]);\n      setShowSpecificModal(false);\n      setSpecificNewsForm({\n        title: {\n          en: '',\n          ar: ''\n        },\n        content: {\n          en: '',\n          ar: ''\n        },\n        recipientType: '',\n        recipientId: ''\n      });\n    } catch (err) {\n      var _err$response6, _err$response6$data;\n      setError(((_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.message) || 'Failed to send specific news');\n    }\n  };\n  const container = {\n    hidden: {\n      opacity: 0\n    },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const item = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    show: {\n      opacity: 1,\n      y: 0\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(SuperAdminSidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: [error && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: -20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-5 w-5 text-red-500 mr-3\",\n                viewBox: \"0 0 20 20\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-700 font-medium\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\",\n                  children: \"News & Announcements\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-[#333333]\",\n                  children: \"Send announcements to universities and users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-3 w-full sm:w-auto\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => setShowGeneralModal(true),\n                  className: \"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FaNewspaper, {\n                    className: \"h-5 w-5 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 157,\n                    columnNumber: 21\n                  }, this), \"Send General News\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => setShowSpecificModal(true),\n                  className: \"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FaNewspaper, {\n                    className: \"h-5 w-5 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 21\n                  }, this), \"Send Specific News\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: container,\n              initial: \"hidden\",\n              whileInView: \"show\",\n              viewport: {\n                once: true\n              },\n              className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-bold text-[#0077B6] mb-6\",\n                  children: \"Sent Announcements\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"overflow-x-auto\",\n                  children: /*#__PURE__*/_jsxDEV(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                      className: \"bg-gray-50\",\n                      children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Title (EN)\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 185,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Title (AR)\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 186,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Recipient\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 187,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Date\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 188,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 184,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 183,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                      className: \"bg-white divide-y divide-gray-200\",\n                      children: news.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: /*#__PURE__*/_jsxDEV(\"td\", {\n                          colSpan: \"4\",\n                          className: \"px-6 py-8 text-center\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex flex-col items-center justify-center\",\n                            children: [/*#__PURE__*/_jsxDEV(FaNewspaper, {\n                              className: \"h-12 w-12 text-gray-400 mb-4\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 196,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                              className: \"text-lg font-medium text-gray-900\",\n                              children: \"No news sent\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 197,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"mt-1 text-gray-500\",\n                              children: \"Send a new announcement to get started.\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 198,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 195,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 194,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 193,\n                        columnNumber: 27\n                      }, this) : news.map(item => /*#__PURE__*/_jsxDEV(motion.tr, {\n                        initial: {\n                          opacity: 0\n                        },\n                        animate: {\n                          opacity: 1\n                        },\n                        className: \"hover:bg-gray-50\",\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                          children: item.title.en\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 210,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                          children: item.title.ar\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 211,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                          children: item.recipientType && item.recipientId ? `${item.recipientType}: ${item.recipientId}` : 'General'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 212,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                          children: new Date(item.createdAt).toLocaleDateString('en-US', {\n                            weekday: 'short',\n                            month: 'short',\n                            day: 'numeric'\n                          })\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 217,\n                          columnNumber: 31\n                        }, this)]\n                      }, item._id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 204,\n                        columnNumber: 29\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 191,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this), showGeneralModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        className: \"bg-white rounded-2xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-[#0077B6]\",\n              children: \"Send General News\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowGeneralModal(false),\n              className: \"text-gray-400 hover:text-gray-500\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleGeneralNewsSubmit,\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Title (English)*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: generalNewsForm.title.en,\n                onChange: e => setGeneralNewsForm({\n                  ...generalNewsForm,\n                  title: {\n                    ...generalNewsForm.title,\n                    en: e.target.value\n                  }\n                }),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Title (Arabic)*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: generalNewsForm.title.ar,\n                onChange: e => setGeneralNewsForm({\n                  ...generalNewsForm,\n                  title: {\n                    ...generalNewsForm.title,\n                    ar: e.target.value\n                  }\n                }),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Content (English)*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: generalNewsForm.content.en,\n                onChange: e => setGeneralNewsForm({\n                  ...generalNewsForm,\n                  content: {\n                    ...generalNewsForm.content,\n                    en: e.target.value\n                  }\n                }),\n                rows: \"4\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Content (Arabic)*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: generalNewsForm.content.ar,\n                onChange: e => setGeneralNewsForm({\n                  ...generalNewsForm,\n                  content: {\n                    ...generalNewsForm.content,\n                    ar: e.target.value\n                  }\n                }),\n                rows: \"4\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end space-x-4 pt-4\",\n              children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                type: \"button\",\n                onClick: () => setShowGeneralModal(false),\n                className: \"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\",\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                type: \"submit\",\n                className: \"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg font-medium transition-colors shadow-md hover:shadow-lg\",\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: \"Send News\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 9\n    }, this), showSpecificModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        className: \"bg-white rounded-2xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-[#0077B6]\",\n              children: \"Send Specific News\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowSpecificModal(false),\n              className: \"text-gray-400 hover:text-gray-500\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSpecificNewsSubmit,\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Title (English)*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: specificNewsForm.title.en,\n                onChange: e => setSpecificNewsForm({\n                  ...specificNewsForm,\n                  title: {\n                    ...specificNewsForm.title,\n                    en: e.target.value\n                  }\n                }),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Title (Arabic)*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: specificNewsForm.title.ar,\n                onChange: e => setSpecificNewsForm({\n                  ...specificNewsForm,\n                  title: {\n                    ...specificNewsForm.title,\n                    ar: e.target.value\n                  }\n                }),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Content (English)*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: specificNewsForm.content.en,\n                onChange: e => setSpecificNewsForm({\n                  ...specificNewsForm,\n                  content: {\n                    ...specificNewsForm.content,\n                    en: e.target.value\n                  }\n                }),\n                rows: \"4\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Content (Arabic)*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: specificNewsForm.content.ar,\n                onChange: e => setSpecificNewsForm({\n                  ...specificNewsForm,\n                  content: {\n                    ...specificNewsForm.content,\n                    ar: e.target.value\n                  }\n                }),\n                rows: \"4\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Recipient Type*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: specificNewsForm.recipientType,\n                onChange: e => setSpecificNewsForm({\n                  ...specificNewsForm,\n                  recipientType: e.target.value,\n                  recipientId: ''\n                }),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Recipient Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"account\",\n                  children: \"Account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"university\",\n                  children: \"University\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Recipient*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: specificNewsForm.recipientId,\n                onChange: e => setSpecificNewsForm({\n                  ...specificNewsForm,\n                  recipientId: e.target.value\n                }),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                required: true,\n                disabled: !specificNewsForm.recipientType,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Recipient\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 21\n                }, this), specificNewsForm.recipientType === 'account' && accounts.map(account => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: account.email,\n                  children: account.email\n                }, account.email, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 23\n                }, this)), specificNewsForm.recipientType === 'university' && universities.map(university => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: university.universityId,\n                  children: university.name.en\n                }, university.universityId, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end space-x-4 pt-4\",\n              children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                type: \"button\",\n                onClick: () => setShowSpecificModal(false),\n                className: \"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\",\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                type: \"submit\",\n                className: \"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg font-medium transition-colors shadow-md hover:shadow-lg\",\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: \"Send News\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 117,\n    columnNumber: 5\n  }, this);\n};\n_s(News, \"O+H93cW4PCURmeBqPqpxIqOXkgE=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = News;\nexport default News;\nvar _c;\n$RefreshReg$(_c, \"News\");", "map": {"version": 3, "names": ["useState", "useEffect", "useNavigate", "axios", "motion", "FaNewspaper", "<PERSON><PERSON><PERSON>", "Loader", "useAuth", "SuperAdminSidebar", "jsxDEV", "_jsxDEV", "News", "_s", "sidebarOpen", "setSidebarOpen", "news", "setNews", "universities", "setUniversities", "accounts", "setAccounts", "showGeneralModal", "setShowGeneralModal", "showSpecificModal", "setShowSpecificModal", "loading", "setLoading", "error", "setError", "navigate", "user", "token", "generalNewsForm", "setGeneralNewsForm", "title", "en", "ar", "content", "specificNewsForm", "setSpecificNewsForm", "recipientType", "recipientId", "fetchData", "config", "headers", "Authorization", "newsRes", "universitiesRes", "accountsRes", "Promise", "all", "get", "process", "env", "REACT_APP_API_URL", "data", "err", "_err$response", "_err$response2", "_err$response3", "_err$response3$data", "_err$response4", "console", "response", "message", "errorMessage", "status", "handleGeneralNewsSubmit", "e", "preventDefault", "post", "_err$response5", "_err$response5$data", "handleSpecificNewsSubmit", "_err$response6", "_err$response6$data", "container", "hidden", "opacity", "show", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "item", "y", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "isOpen", "setIsOpen", "toggleSidebar", "div", "initial", "animate", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "duration", "button", "whileHover", "scale", "whileTap", "onClick", "variants", "whileInView", "viewport", "once", "length", "colSpan", "map", "tr", "Date", "createdAt", "toLocaleDateString", "weekday", "month", "day", "_id", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "onSubmit", "type", "value", "onChange", "target", "required", "rows", "disabled", "account", "email", "university", "universityId", "name", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/superadmin/News.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { motion } from 'framer-motion';\nimport { FaNewspaper } from 'react-icons/fa';\nimport Navbar from '../student/Navbar';\nimport Loader from '../components/Loader';\nimport { useAuth } from '../context/AuthContext';\nimport SuperAdminSidebar from './SuperAdminSidebar';\n\nconst News = () => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [news, setNews] = useState([]);\n  const [universities, setUniversities] = useState([]);\n  const [accounts, setAccounts] = useState([]);\n  const [showGeneralModal, setShowGeneralModal] = useState(false);\n  const [showSpecificModal, setShowSpecificModal] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  const { user, token } = useAuth();\n\n  const [generalNewsForm, setGeneralNewsForm] = useState({\n    title: { en: '', ar: '' },\n    content: { en: '', ar: '' },\n  });\n\n  const [specificNewsForm, setSpecificNewsForm] = useState({\n    title: { en: '', ar: '' },\n    content: { en: '', ar: '' },\n    recipientType: '',\n    recipientId: '',\n  });\n\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!user || !token) {\n        setError('Please log in to view news.');\n        setLoading(false);\n        return;\n      }\n\n      try {\n        const config = { headers: { Authorization: `Bearer ${token}` } };\n        const [newsRes, universitiesRes, accountsRes] = await Promise.all([\n          axios.get(`${process.env.REACT_APP_API_URL}/api/news`, config),\n          axios.get(`${process.env.REACT_APP_API_URL}/api/universities`, config),\n          axios.get(`${process.env.REACT_APP_API_URL}/api/accounts`, config),\n        ]);\n\n        setNews(newsRes.data || []);\n        setUniversities(universitiesRes.data || []);\n        setAccounts(accountsRes.data || []);\n      } catch (err) {\n        console.error('Fetch error:', err.response?.data || err.message);\n        const errorMessage = err.response?.status === 401\n          ? 'Unauthorized. Please log in again.'\n          : err.response?.data?.message || 'Failed to load data';\n        setError(errorMessage);\n        if (err.response?.status === 401) {\n          navigate('/login');\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [user, token, navigate]);\n\n  const handleGeneralNewsSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      const response = await axios.post('http://localhost:5000/api/news', generalNewsForm, {\n        headers: { Authorization: `Bearer ${token}` },\n      });\n      setNews([...news, response.data]);\n      setShowGeneralModal(false);\n      setGeneralNewsForm({ title: { en: '', ar: '' }, content: { en: '', ar: '' } });\n    } catch (err) {\n      setError(err.response?.data?.message || 'Failed to send general news');\n    }\n  };\n\n  const handleSpecificNewsSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      const response = await axios.post('http://localhost:5000/api/news/specific', {\n        ...specificNewsForm,\n        recipientType: specificNewsForm.recipientType,\n        recipientId: specificNewsForm.recipientId,\n      }, {\n        headers: { Authorization: `Bearer ${token}` },\n      });\n      setNews([...news, response.data]);\n      setShowSpecificModal(false);\n      setSpecificNewsForm({ title: { en: '', ar: '' }, content: { en: '', ar: '' }, recipientType: '', recipientId: '' });\n    } catch (err) {\n      setError(err.response?.data?.message || 'Failed to send specific news');\n    }\n  };\n\n  const container = {\n    hidden: { opacity: 0 },\n    show: { opacity: 1, transition: { staggerChildren: 0.1 } },\n  };\n\n  const item = {\n    hidden: { opacity: 0, y: 20 },\n    show: { opacity: 1, y: 0 },\n  };\n\n  if (loading) {\n    return <Loader />;\n  }\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\">\n      <SuperAdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n      \n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\n        \n        <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\">\n          <div className=\"max-w-7xl mx-auto\">\n            {error && (\n              <motion.div\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                className=\"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\"\n              >\n                <div className=\"flex items-center\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-red-500 mr-3\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <p className=\"text-red-700 font-medium\">{error}</p>\n                </div>\n              </motion.div>\n            )}\n\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.5 }}\n            >\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\">\n                <div>\n                  <h1 className=\"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\">News & Announcements</h1>\n                  <p className=\"text-[#333333]\">Send announcements to universities and users</p>\n                </div>\n                <div className=\"flex flex-col sm:flex-row gap-3 w-full sm:w-auto\">\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => setShowGeneralModal(true)}\n                    className=\"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\"\n                  >\n                    <FaNewspaper className=\"h-5 w-5 mr-2\" />\n                    Send General News\n                  </motion.button>\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => setShowSpecificModal(true)}\n                    className=\"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\"\n                  >\n                    <FaNewspaper className=\"h-5 w-5 mr-2\" />\n                    Send Specific News\n                  </motion.button>\n                </div>\n              </div>\n\n              <motion.div \n                variants={container}\n                initial=\"hidden\"\n                whileInView=\"show\"\n                viewport={{ once: true }}\n                className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden\"\n              >\n                <div className=\"p-6\">\n                  <h2 className=\"text-xl font-bold text-[#0077B6] mb-6\">Sent Announcements</h2>\n                  <div className=\"overflow-x-auto\">\n                    <table className=\"min-w-full divide-y divide-gray-200\">\n                      <thead className=\"bg-gray-50\">\n                        <tr>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Title (EN)</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Title (AR)</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Recipient</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Date</th>\n                        </tr>\n                      </thead>\n                      <tbody className=\"bg-white divide-y divide-gray-200\">\n                        {news.length === 0 ? (\n                          <tr>\n                            <td colSpan=\"4\" className=\"px-6 py-8 text-center\">\n                              <div className=\"flex flex-col items-center justify-center\">\n                                <FaNewspaper className=\"h-12 w-12 text-gray-400 mb-4\" />\n                                <h3 className=\"text-lg font-medium text-gray-900\">No news sent</h3>\n                                <p className=\"mt-1 text-gray-500\">Send a new announcement to get started.</p>\n                              </div>\n                            </td>\n                          </tr>\n                        ) : (\n                          news.map((item) => (\n                            <motion.tr \n                              key={item._id} \n                              initial={{ opacity: 0 }}\n                              animate={{ opacity: 1 }}\n                              className=\"hover:bg-gray-50\"\n                            >\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{item.title.en}</td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{item.title.ar}</td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                                {item.recipientType && item.recipientId \n                                  ? `${item.recipientType}: ${item.recipientId}` \n                                  : 'General'}\n                              </td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                                {new Date(item.createdAt).toLocaleDateString('en-US', {\n                                  weekday: 'short', month: 'short', day: 'numeric'\n                                })}\n                              </td>\n                            </motion.tr>\n                          ))\n                        )}\n                      </tbody>\n                    </table>\n                  </div>\n                </div>\n              </motion.div>\n            </motion.div>\n          </div>\n        </main>\n      </div>\n\n      {showGeneralModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <motion.div \n            initial={{ scale: 0.9, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            className=\"bg-white rounded-2xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-y-auto\"\n          >\n            <div className=\"p-6\">\n              <div className=\"flex justify-between items-center mb-6\">\n                <h2 className=\"text-2xl font-bold text-[#0077B6]\">Send General News</h2>\n                <button onClick={() => setShowGeneralModal(false)} className=\"text-gray-400 hover:text-gray-500\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n              <form onSubmit={handleGeneralNewsSubmit} className=\"space-y-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Title (English)*</label>\n                  <input \n                    type=\"text\" \n                    value={generalNewsForm.title.en} \n                    onChange={(e) => setGeneralNewsForm({ ...generalNewsForm, title: { ...generalNewsForm.title, en: e.target.value } })} \n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                    required \n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Title (Arabic)*</label>\n                  <input \n                    type=\"text\" \n                    value={generalNewsForm.title.ar} \n                    onChange={(e) => setGeneralNewsForm({ ...generalNewsForm, title: { ...generalNewsForm.title, ar: e.target.value } })} \n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                    required \n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Content (English)*</label>\n                  <textarea \n                    value={generalNewsForm.content.en} \n                    onChange={(e) => setGeneralNewsForm({ ...generalNewsForm, content: { ...generalNewsForm.content, en: e.target.value } })} \n                    rows=\"4\" \n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                    required \n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Content (Arabic)*</label>\n                  <textarea \n                    value={generalNewsForm.content.ar} \n                    onChange={(e) => setGeneralNewsForm({ ...generalNewsForm, content: { ...generalNewsForm.content, ar: e.target.value } })} \n                    rows=\"4\" \n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                    required \n                  />\n                </div>\n                <div className=\"flex justify-end space-x-4 pt-4\">\n                  <motion.button \n                    type=\"button\" \n                    onClick={() => setShowGeneralModal(false)} \n                    className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    Cancel\n                  </motion.button>\n                  <motion.button \n                    type=\"submit\" \n                    className=\"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg font-medium transition-colors shadow-md hover:shadow-lg\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    Send News\n                  </motion.button>\n                </div>\n              </form>\n            </div>\n          </motion.div>\n        </div>\n      )}\n\n      {showSpecificModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <motion.div \n            initial={{ scale: 0.9, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            className=\"bg-white rounded-2xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-y-auto\"\n          >\n            <div className=\"p-6\">\n              <div className=\"flex justify-between items-center mb-6\">\n                <h2 className=\"text-2xl font-bold text-[#0077B6]\">Send Specific News</h2>\n                <button onClick={() => setShowSpecificModal(false)} className=\"text-gray-400 hover:text-gray-500\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n              <form onSubmit={handleSpecificNewsSubmit} className=\"space-y-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Title (English)*</label>\n                  <input \n                    type=\"text\" \n                    value={specificNewsForm.title.en} \n                    onChange={(e) => setSpecificNewsForm({ ...specificNewsForm, title: { ...specificNewsForm.title, en: e.target.value } })} \n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                    required \n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Title (Arabic)*</label>\n                  <input \n                    type=\"text\" \n                    value={specificNewsForm.title.ar} \n                    onChange={(e) => setSpecificNewsForm({ ...specificNewsForm, title: { ...specificNewsForm.title, ar: e.target.value } })} \n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                    required \n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Content (English)*</label>\n                  <textarea \n                    value={specificNewsForm.content.en} \n                    onChange={(e) => setSpecificNewsForm({ ...specificNewsForm, content: { ...specificNewsForm.content, en: e.target.value } })} \n                    rows=\"4\" \n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                    required \n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Content (Arabic)*</label>\n                  <textarea \n                    value={specificNewsForm.content.ar} \n                    onChange={(e) => setSpecificNewsForm({ ...specificNewsForm, content: { ...specificNewsForm.content, ar: e.target.value } })} \n                    rows=\"4\" \n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                    required \n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Recipient Type*</label>\n                  <select \n                    value={specificNewsForm.recipientType} \n                    onChange={(e) => setSpecificNewsForm({ ...specificNewsForm, recipientType: e.target.value, recipientId: '' })} \n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                    required\n                  >\n                    <option value=\"\">Select Recipient Type</option>\n                    <option value=\"account\">Account</option>\n                    <option value=\"university\">University</option>\n                  </select>\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Recipient*</label>\n                  <select \n                    value={specificNewsForm.recipientId} \n                    onChange={(e) => setSpecificNewsForm({ ...specificNewsForm, recipientId: e.target.value })} \n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                    required\n                    disabled={!specificNewsForm.recipientType}\n                  >\n                    <option value=\"\">Select Recipient</option>\n                    {specificNewsForm.recipientType === 'account' && accounts.map((account) => (\n                      <option key={account.email} value={account.email}>{account.email}</option>\n                    ))}\n                    {specificNewsForm.recipientType === 'university' && universities.map((university) => (\n                      <option key={university.universityId} value={university.universityId}>{university.name.en}</option>\n                    ))}\n                  </select>\n                </div>\n                <div className=\"flex justify-end space-x-4 pt-4\">\n                  <motion.button \n                    type=\"button\" \n                    onClick={() => setShowSpecificModal(false)} \n                    className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    Cancel\n                  </motion.button>\n                  <motion.button \n                    type=\"submit\" \n                    className=\"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg font-medium transition-colors shadow-md hover:shadow-lg\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    Send News\n                  </motion.button>\n                </div>\n              </form>\n            </div>\n          </motion.div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default News;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,iBAAiB,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgB,IAAI,EAAEC,OAAO,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACwB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM8B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6B,IAAI;IAAEC;EAAM,CAAC,GAAGxB,OAAO,CAAC,CAAC;EAEjC,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC;IACrDmC,KAAK,EAAE;MAAEC,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG,CAAC;IACzBC,OAAO,EAAE;MAAEF,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG;EAC5B,CAAC,CAAC;EAEF,MAAM,CAACE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxC,QAAQ,CAAC;IACvDmC,KAAK,EAAE;MAAEC,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG,CAAC;IACzBC,OAAO,EAAE;MAAEF,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG,CAAC;IAC3BI,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE;EACf,CAAC,CAAC;EAEFzC,SAAS,CAAC,MAAM;IACd,MAAM0C,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI,CAACZ,IAAI,IAAI,CAACC,KAAK,EAAE;QACnBH,QAAQ,CAAC,6BAA6B,CAAC;QACvCF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,IAAI;QACF,MAAMiB,MAAM,GAAG;UAAEC,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAUd,KAAK;UAAG;QAAE,CAAC;QAChE,MAAM,CAACe,OAAO,EAAEC,eAAe,EAAEC,WAAW,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChEhD,KAAK,CAACiD,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,WAAW,EAAEX,MAAM,CAAC,EAC9DzC,KAAK,CAACiD,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,mBAAmB,EAAEX,MAAM,CAAC,EACtEzC,KAAK,CAACiD,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,eAAe,EAAEX,MAAM,CAAC,CACnE,CAAC;QAEF3B,OAAO,CAAC8B,OAAO,CAACS,IAAI,IAAI,EAAE,CAAC;QAC3BrC,eAAe,CAAC6B,eAAe,CAACQ,IAAI,IAAI,EAAE,CAAC;QAC3CnC,WAAW,CAAC4B,WAAW,CAACO,IAAI,IAAI,EAAE,CAAC;MACrC,CAAC,CAAC,OAAOC,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA,EAAAC,cAAA;QACZC,OAAO,CAACnC,KAAK,CAAC,cAAc,EAAE,EAAA8B,aAAA,GAAAD,GAAG,CAACO,QAAQ,cAAAN,aAAA,uBAAZA,aAAA,CAAcF,IAAI,KAAIC,GAAG,CAACQ,OAAO,CAAC;QAChE,MAAMC,YAAY,GAAG,EAAAP,cAAA,GAAAF,GAAG,CAACO,QAAQ,cAAAL,cAAA,uBAAZA,cAAA,CAAcQ,MAAM,MAAK,GAAG,GAC7C,oCAAoC,GACpC,EAAAP,cAAA,GAAAH,GAAG,CAACO,QAAQ,cAAAJ,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcJ,IAAI,cAAAK,mBAAA,uBAAlBA,mBAAA,CAAoBI,OAAO,KAAI,qBAAqB;QACxDpC,QAAQ,CAACqC,YAAY,CAAC;QACtB,IAAI,EAAAJ,cAAA,GAAAL,GAAG,CAACO,QAAQ,cAAAF,cAAA,uBAAZA,cAAA,CAAcK,MAAM,MAAK,GAAG,EAAE;UAChCrC,QAAQ,CAAC,QAAQ,CAAC;QACpB;MACF,CAAC,SAAS;QACRH,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDgB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACZ,IAAI,EAAEC,KAAK,EAAEF,QAAQ,CAAC,CAAC;EAE3B,MAAMsC,uBAAuB,GAAG,MAAOC,CAAC,IAAK;IAC3CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAM7D,KAAK,CAACoE,IAAI,CAAC,gCAAgC,EAAEtC,eAAe,EAAE;QACnFY,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUd,KAAK;QAAG;MAC9C,CAAC,CAAC;MACFf,OAAO,CAAC,CAAC,GAAGD,IAAI,EAAEgD,QAAQ,CAACR,IAAI,CAAC,CAAC;MACjCjC,mBAAmB,CAAC,KAAK,CAAC;MAC1BW,kBAAkB,CAAC;QAAEC,KAAK,EAAE;UAAEC,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QAAEC,OAAO,EAAE;UAAEF,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG;MAAE,CAAC,CAAC;IAChF,CAAC,CAAC,OAAOoB,GAAG,EAAE;MAAA,IAAAe,cAAA,EAAAC,mBAAA;MACZ5C,QAAQ,CAAC,EAAA2C,cAAA,GAAAf,GAAG,CAACO,QAAQ,cAAAQ,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAchB,IAAI,cAAAiB,mBAAA,uBAAlBA,mBAAA,CAAoBR,OAAO,KAAI,6BAA6B,CAAC;IACxE;EACF,CAAC;EAED,MAAMS,wBAAwB,GAAG,MAAOL,CAAC,IAAK;IAC5CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAM7D,KAAK,CAACoE,IAAI,CAAC,yCAAyC,EAAE;QAC3E,GAAGhC,gBAAgB;QACnBE,aAAa,EAAEF,gBAAgB,CAACE,aAAa;QAC7CC,WAAW,EAAEH,gBAAgB,CAACG;MAChC,CAAC,EAAE;QACDG,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUd,KAAK;QAAG;MAC9C,CAAC,CAAC;MACFf,OAAO,CAAC,CAAC,GAAGD,IAAI,EAAEgD,QAAQ,CAACR,IAAI,CAAC,CAAC;MACjC/B,oBAAoB,CAAC,KAAK,CAAC;MAC3Be,mBAAmB,CAAC;QAAEL,KAAK,EAAE;UAAEC,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QAAEC,OAAO,EAAE;UAAEF,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QAAEI,aAAa,EAAE,EAAE;QAAEC,WAAW,EAAE;MAAG,CAAC,CAAC;IACrH,CAAC,CAAC,OAAOe,GAAG,EAAE;MAAA,IAAAkB,cAAA,EAAAC,mBAAA;MACZ/C,QAAQ,CAAC,EAAA8C,cAAA,GAAAlB,GAAG,CAACO,QAAQ,cAAAW,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcnB,IAAI,cAAAoB,mBAAA,uBAAlBA,mBAAA,CAAoBX,OAAO,KAAI,8BAA8B,CAAC;IACzE;EACF,CAAC;EAED,MAAMY,SAAS,GAAG;IAChBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEE,UAAU,EAAE;QAAEC,eAAe,EAAE;MAAI;IAAE;EAC3D,CAAC;EAED,MAAMC,IAAI,GAAG;IACXL,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAG,CAAC;IAC7BJ,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAE;EAC3B,CAAC;EAED,IAAI1D,OAAO,EAAE;IACX,oBAAOf,OAAA,CAACJ,MAAM;MAAA8E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnB;EAEA,oBACE7E,OAAA;IAAK8E,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvC/E,OAAA,CAACF,iBAAiB;MAACkF,MAAM,EAAE7E,WAAY;MAAC8E,SAAS,EAAE7E;IAAe;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAErE7E,OAAA;MAAK8E,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnD/E,OAAA,CAACL,MAAM;QAACuF,aAAa,EAAEA,CAAA,KAAM9E,cAAc,CAAC,CAACD,WAAW;MAAE;QAAAuE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE7D7E,OAAA;QAAM8E,SAAS,EAAC,0FAA0F;QAAAC,QAAA,eACxG/E,OAAA;UAAK8E,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAC/B9D,KAAK,iBACJjB,OAAA,CAACP,MAAM,CAAC0F,GAAG;YACTC,OAAO,EAAE;cAAEhB,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCY,OAAO,EAAE;cAAEjB,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE;YAAE,CAAE;YAC9BK,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAE7E/E,OAAA;cAAK8E,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC/E,OAAA;gBAAKsF,KAAK,EAAC,4BAA4B;gBAACR,SAAS,EAAC,2BAA2B;gBAACS,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAT,QAAA,eACnH/E,OAAA;kBAAMyF,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,mNAAmN;kBAACC,QAAQ,EAAC;gBAAS;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjQ,CAAC,eACN7E,OAAA;gBAAG8E,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAE9D;cAAK;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eAED7E,OAAA,CAACP,MAAM,CAAC0F,GAAG;YACTC,OAAO,EAAE;cAAEhB,OAAO,EAAE;YAAE,CAAE;YACxBiB,OAAO,EAAE;cAAEjB,OAAO,EAAE;YAAE,CAAE;YACxBE,UAAU,EAAE;cAAEsB,QAAQ,EAAE;YAAI,CAAE;YAAAb,QAAA,gBAE9B/E,OAAA;cAAK8E,SAAS,EAAC,kFAAkF;cAAAC,QAAA,gBAC/F/E,OAAA;gBAAA+E,QAAA,gBACE/E,OAAA;kBAAI8E,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5F7E,OAAA;kBAAG8E,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAA4C;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC,eACN7E,OAAA;gBAAK8E,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,gBAC/D/E,OAAA,CAACP,MAAM,CAACoG,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAMrF,mBAAmB,CAAC,IAAI,CAAE;kBACzCkE,SAAS,EAAC,oMAAoM;kBAAAC,QAAA,gBAE9M/E,OAAA,CAACN,WAAW;oBAACoF,SAAS,EAAC;kBAAc;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,qBAE1C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAChB7E,OAAA,CAACP,MAAM,CAACoG,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAEA,CAAA,KAAMnF,oBAAoB,CAAC,IAAI,CAAE;kBAC1CgE,SAAS,EAAC,oMAAoM;kBAAAC,QAAA,gBAE9M/E,OAAA,CAACN,WAAW;oBAACoF,SAAS,EAAC;kBAAc;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,sBAE1C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7E,OAAA,CAACP,MAAM,CAAC0F,GAAG;cACTe,QAAQ,EAAEhC,SAAU;cACpBkB,OAAO,EAAC,QAAQ;cAChBe,WAAW,EAAC,MAAM;cAClBC,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBvB,SAAS,EAAC,kHAAkH;cAAAC,QAAA,eAE5H/E,OAAA;gBAAK8E,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClB/E,OAAA;kBAAI8E,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7E7E,OAAA;kBAAK8E,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAC9B/E,OAAA;oBAAO8E,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,gBACpD/E,OAAA;sBAAO8E,SAAS,EAAC,YAAY;sBAAAC,QAAA,eAC3B/E,OAAA;wBAAA+E,QAAA,gBACE/E,OAAA;0BAAI8E,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAU;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC9G7E,OAAA;0BAAI8E,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAU;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC9G7E,OAAA;0BAAI8E,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAS;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC7G7E,OAAA;0BAAI8E,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAI;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACR7E,OAAA;sBAAO8E,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EACjD1E,IAAI,CAACiG,MAAM,KAAK,CAAC,gBAChBtG,OAAA;wBAAA+E,QAAA,eACE/E,OAAA;0BAAIuG,OAAO,EAAC,GAAG;0BAACzB,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,eAC/C/E,OAAA;4BAAK8E,SAAS,EAAC,2CAA2C;4BAAAC,QAAA,gBACxD/E,OAAA,CAACN,WAAW;8BAACoF,SAAS,EAAC;4BAA8B;8BAAAJ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eACxD7E,OAAA;8BAAI8E,SAAS,EAAC,mCAAmC;8BAAAC,QAAA,EAAC;4BAAY;8BAAAL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACnE7E,OAAA;8BAAG8E,SAAS,EAAC,oBAAoB;8BAAAC,QAAA,EAAC;4BAAuC;8BAAAL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1E;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,GAELxE,IAAI,CAACmG,GAAG,CAAEhC,IAAI,iBACZxE,OAAA,CAACP,MAAM,CAACgH,EAAE;wBAERrB,OAAO,EAAE;0BAAEhB,OAAO,EAAE;wBAAE,CAAE;wBACxBiB,OAAO,EAAE;0BAAEjB,OAAO,EAAE;wBAAE,CAAE;wBACxBU,SAAS,EAAC,kBAAkB;wBAAAC,QAAA,gBAE5B/E,OAAA;0BAAI8E,SAAS,EAAC,mDAAmD;0BAAAC,QAAA,EAAEP,IAAI,CAAChD,KAAK,CAACC;wBAAE;0BAAAiD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACtF7E,OAAA;0BAAI8E,SAAS,EAAC,mDAAmD;0BAAAC,QAAA,EAAEP,IAAI,CAAChD,KAAK,CAACE;wBAAE;0BAAAgD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACtF7E,OAAA;0BAAI8E,SAAS,EAAC,mDAAmD;0BAAAC,QAAA,EAC9DP,IAAI,CAAC1C,aAAa,IAAI0C,IAAI,CAACzC,WAAW,GACnC,GAAGyC,IAAI,CAAC1C,aAAa,KAAK0C,IAAI,CAACzC,WAAW,EAAE,GAC5C;wBAAS;0BAAA2C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACX,CAAC,eACL7E,OAAA;0BAAI8E,SAAS,EAAC,mDAAmD;0BAAAC,QAAA,EAC9D,IAAI2B,IAAI,CAAClC,IAAI,CAACmC,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;4BACpDC,OAAO,EAAE,OAAO;4BAAEC,KAAK,EAAE,OAAO;4BAAEC,GAAG,EAAE;0BACzC,CAAC;wBAAC;0BAAArC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC;sBAAA,GAhBAL,IAAI,CAACwC,GAAG;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAiBJ,CACZ;oBACF;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAELlE,gBAAgB,iBACfX,OAAA;MAAK8E,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F/E,OAAA,CAACP,MAAM,CAAC0F,GAAG;QACTC,OAAO,EAAE;UAAEW,KAAK,EAAE,GAAG;UAAE3B,OAAO,EAAE;QAAE,CAAE;QACpCiB,OAAO,EAAE;UAAEU,KAAK,EAAE,CAAC;UAAE3B,OAAO,EAAE;QAAE,CAAE;QAClCU,SAAS,EAAC,8EAA8E;QAAAC,QAAA,eAExF/E,OAAA;UAAK8E,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB/E,OAAA;YAAK8E,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD/E,OAAA;cAAI8E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAiB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxE7E,OAAA;cAAQiG,OAAO,EAAEA,CAAA,KAAMrF,mBAAmB,CAAC,KAAK,CAAE;cAACkE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eAC9F/E,OAAA;gBAAKsF,KAAK,EAAC,4BAA4B;gBAACR,SAAS,EAAC,SAAS;gBAACU,IAAI,EAAC,MAAM;gBAACD,OAAO,EAAC,WAAW;gBAAC0B,MAAM,EAAC,cAAc;gBAAAlC,QAAA,eAC/G/E,OAAA;kBAAMkH,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAAC1B,CAAC,EAAC;gBAAsB;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN7E,OAAA;YAAMqH,QAAQ,EAAE5D,uBAAwB;YAACqB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAC5D/E,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAO8E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAgB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxF7E,OAAA;gBACEsH,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAEjG,eAAe,CAACE,KAAK,CAACC,EAAG;gBAChC+F,QAAQ,EAAG9D,CAAC,IAAKnC,kBAAkB,CAAC;kBAAE,GAAGD,eAAe;kBAAEE,KAAK,EAAE;oBAAE,GAAGF,eAAe,CAACE,KAAK;oBAAEC,EAAE,EAAEiC,CAAC,CAAC+D,MAAM,CAACF;kBAAM;gBAAE,CAAC,CAAE;gBACrHzC,SAAS,EAAC,6GAA6G;gBACvH4C,QAAQ;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7E,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAO8E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvF7E,OAAA;gBACEsH,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAEjG,eAAe,CAACE,KAAK,CAACE,EAAG;gBAChC8F,QAAQ,EAAG9D,CAAC,IAAKnC,kBAAkB,CAAC;kBAAE,GAAGD,eAAe;kBAAEE,KAAK,EAAE;oBAAE,GAAGF,eAAe,CAACE,KAAK;oBAAEE,EAAE,EAAEgC,CAAC,CAAC+D,MAAM,CAACF;kBAAM;gBAAE,CAAC,CAAE;gBACrHzC,SAAS,EAAC,6GAA6G;gBACvH4C,QAAQ;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7E,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAO8E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAkB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1F7E,OAAA;gBACEuH,KAAK,EAAEjG,eAAe,CAACK,OAAO,CAACF,EAAG;gBAClC+F,QAAQ,EAAG9D,CAAC,IAAKnC,kBAAkB,CAAC;kBAAE,GAAGD,eAAe;kBAAEK,OAAO,EAAE;oBAAE,GAAGL,eAAe,CAACK,OAAO;oBAAEF,EAAE,EAAEiC,CAAC,CAAC+D,MAAM,CAACF;kBAAM;gBAAE,CAAC,CAAE;gBACzHI,IAAI,EAAC,GAAG;gBACR7C,SAAS,EAAC,6GAA6G;gBACvH4C,QAAQ;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7E,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAO8E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAiB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzF7E,OAAA;gBACEuH,KAAK,EAAEjG,eAAe,CAACK,OAAO,CAACD,EAAG;gBAClC8F,QAAQ,EAAG9D,CAAC,IAAKnC,kBAAkB,CAAC;kBAAE,GAAGD,eAAe;kBAAEK,OAAO,EAAE;oBAAE,GAAGL,eAAe,CAACK,OAAO;oBAAED,EAAE,EAAEgC,CAAC,CAAC+D,MAAM,CAACF;kBAAM;gBAAE,CAAC,CAAE;gBACzHI,IAAI,EAAC,GAAG;gBACR7C,SAAS,EAAC,6GAA6G;gBACvH4C,QAAQ;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7E,OAAA;cAAK8E,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9C/E,OAAA,CAACP,MAAM,CAACoG,MAAM;gBACZyB,IAAI,EAAC,QAAQ;gBACbrB,OAAO,EAAEA,CAAA,KAAMrF,mBAAmB,CAAC,KAAK,CAAE;gBAC1CkE,SAAS,EAAC,0GAA0G;gBACpHgB,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAAAhB,QAAA,EAC3B;cAED;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC,eAChB7E,OAAA,CAACP,MAAM,CAACoG,MAAM;gBACZyB,IAAI,EAAC,QAAQ;gBACbxC,SAAS,EAAC,sIAAsI;gBAChJgB,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAAAhB,QAAA,EAC3B;cAED;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,EAEAhE,iBAAiB,iBAChBb,OAAA;MAAK8E,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F/E,OAAA,CAACP,MAAM,CAAC0F,GAAG;QACTC,OAAO,EAAE;UAAEW,KAAK,EAAE,GAAG;UAAE3B,OAAO,EAAE;QAAE,CAAE;QACpCiB,OAAO,EAAE;UAAEU,KAAK,EAAE,CAAC;UAAE3B,OAAO,EAAE;QAAE,CAAE;QAClCU,SAAS,EAAC,8EAA8E;QAAAC,QAAA,eAExF/E,OAAA;UAAK8E,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB/E,OAAA;YAAK8E,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD/E,OAAA;cAAI8E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAkB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzE7E,OAAA;cAAQiG,OAAO,EAAEA,CAAA,KAAMnF,oBAAoB,CAAC,KAAK,CAAE;cAACgE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eAC/F/E,OAAA;gBAAKsF,KAAK,EAAC,4BAA4B;gBAACR,SAAS,EAAC,SAAS;gBAACU,IAAI,EAAC,MAAM;gBAACD,OAAO,EAAC,WAAW;gBAAC0B,MAAM,EAAC,cAAc;gBAAAlC,QAAA,eAC/G/E,OAAA;kBAAMkH,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAAC1B,CAAC,EAAC;gBAAsB;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN7E,OAAA;YAAMqH,QAAQ,EAAEtD,wBAAyB;YAACe,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAC7D/E,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAO8E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAgB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxF7E,OAAA;gBACEsH,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAE3F,gBAAgB,CAACJ,KAAK,CAACC,EAAG;gBACjC+F,QAAQ,EAAG9D,CAAC,IAAK7B,mBAAmB,CAAC;kBAAE,GAAGD,gBAAgB;kBAAEJ,KAAK,EAAE;oBAAE,GAAGI,gBAAgB,CAACJ,KAAK;oBAAEC,EAAE,EAAEiC,CAAC,CAAC+D,MAAM,CAACF;kBAAM;gBAAE,CAAC,CAAE;gBACxHzC,SAAS,EAAC,6GAA6G;gBACvH4C,QAAQ;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7E,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAO8E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvF7E,OAAA;gBACEsH,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAE3F,gBAAgB,CAACJ,KAAK,CAACE,EAAG;gBACjC8F,QAAQ,EAAG9D,CAAC,IAAK7B,mBAAmB,CAAC;kBAAE,GAAGD,gBAAgB;kBAAEJ,KAAK,EAAE;oBAAE,GAAGI,gBAAgB,CAACJ,KAAK;oBAAEE,EAAE,EAAEgC,CAAC,CAAC+D,MAAM,CAACF;kBAAM;gBAAE,CAAC,CAAE;gBACxHzC,SAAS,EAAC,6GAA6G;gBACvH4C,QAAQ;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7E,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAO8E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAkB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1F7E,OAAA;gBACEuH,KAAK,EAAE3F,gBAAgB,CAACD,OAAO,CAACF,EAAG;gBACnC+F,QAAQ,EAAG9D,CAAC,IAAK7B,mBAAmB,CAAC;kBAAE,GAAGD,gBAAgB;kBAAED,OAAO,EAAE;oBAAE,GAAGC,gBAAgB,CAACD,OAAO;oBAAEF,EAAE,EAAEiC,CAAC,CAAC+D,MAAM,CAACF;kBAAM;gBAAE,CAAC,CAAE;gBAC5HI,IAAI,EAAC,GAAG;gBACR7C,SAAS,EAAC,6GAA6G;gBACvH4C,QAAQ;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7E,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAO8E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAiB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzF7E,OAAA;gBACEuH,KAAK,EAAE3F,gBAAgB,CAACD,OAAO,CAACD,EAAG;gBACnC8F,QAAQ,EAAG9D,CAAC,IAAK7B,mBAAmB,CAAC;kBAAE,GAAGD,gBAAgB;kBAAED,OAAO,EAAE;oBAAE,GAAGC,gBAAgB,CAACD,OAAO;oBAAED,EAAE,EAAEgC,CAAC,CAAC+D,MAAM,CAACF;kBAAM;gBAAE,CAAC,CAAE;gBAC5HI,IAAI,EAAC,GAAG;gBACR7C,SAAS,EAAC,6GAA6G;gBACvH4C,QAAQ;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7E,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAO8E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvF7E,OAAA;gBACEuH,KAAK,EAAE3F,gBAAgB,CAACE,aAAc;gBACtC0F,QAAQ,EAAG9D,CAAC,IAAK7B,mBAAmB,CAAC;kBAAE,GAAGD,gBAAgB;kBAAEE,aAAa,EAAE4B,CAAC,CAAC+D,MAAM,CAACF,KAAK;kBAAExF,WAAW,EAAE;gBAAG,CAAC,CAAE;gBAC9G+C,SAAS,EAAC,6GAA6G;gBACvH4C,QAAQ;gBAAA3C,QAAA,gBAER/E,OAAA;kBAAQuH,KAAK,EAAC,EAAE;kBAAAxC,QAAA,EAAC;gBAAqB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/C7E,OAAA;kBAAQuH,KAAK,EAAC,SAAS;kBAAAxC,QAAA,EAAC;gBAAO;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxC7E,OAAA;kBAAQuH,KAAK,EAAC,YAAY;kBAAAxC,QAAA,EAAC;gBAAU;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN7E,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAO8E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAU;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClF7E,OAAA;gBACEuH,KAAK,EAAE3F,gBAAgB,CAACG,WAAY;gBACpCyF,QAAQ,EAAG9D,CAAC,IAAK7B,mBAAmB,CAAC;kBAAE,GAAGD,gBAAgB;kBAAEG,WAAW,EAAE2B,CAAC,CAAC+D,MAAM,CAACF;gBAAM,CAAC,CAAE;gBAC3FzC,SAAS,EAAC,6GAA6G;gBACvH4C,QAAQ;gBACRE,QAAQ,EAAE,CAAChG,gBAAgB,CAACE,aAAc;gBAAAiD,QAAA,gBAE1C/E,OAAA;kBAAQuH,KAAK,EAAC,EAAE;kBAAAxC,QAAA,EAAC;gBAAgB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACzCjD,gBAAgB,CAACE,aAAa,KAAK,SAAS,IAAIrB,QAAQ,CAAC+F,GAAG,CAAEqB,OAAO,iBACpE7H,OAAA;kBAA4BuH,KAAK,EAAEM,OAAO,CAACC,KAAM;kBAAA/C,QAAA,EAAE8C,OAAO,CAACC;gBAAK,GAAnDD,OAAO,CAACC,KAAK;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA+C,CAC1E,CAAC,EACDjD,gBAAgB,CAACE,aAAa,KAAK,YAAY,IAAIvB,YAAY,CAACiG,GAAG,CAAEuB,UAAU,iBAC9E/H,OAAA;kBAAsCuH,KAAK,EAAEQ,UAAU,CAACC,YAAa;kBAAAjD,QAAA,EAAEgD,UAAU,CAACE,IAAI,CAACxG;gBAAE,GAA5EsG,UAAU,CAACC,YAAY;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA8D,CACnG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN7E,OAAA;cAAK8E,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9C/E,OAAA,CAACP,MAAM,CAACoG,MAAM;gBACZyB,IAAI,EAAC,QAAQ;gBACbrB,OAAO,EAAEA,CAAA,KAAMnF,oBAAoB,CAAC,KAAK,CAAE;gBAC3CgE,SAAS,EAAC,0GAA0G;gBACpHgB,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAAAhB,QAAA,EAC3B;cAED;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC,eAChB7E,OAAA,CAACP,MAAM,CAACoG,MAAM;gBACZyB,IAAI,EAAC,QAAQ;gBACbxC,SAAS,EAAC,sIAAsI;gBAChJgB,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAAAhB,QAAA,EAC3B;cAED;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC3E,EAAA,CApaID,IAAI;EAAA,QASSV,WAAW,EACJM,OAAO;AAAA;AAAAqI,EAAA,GAV3BjI,IAAI;AAsaV,eAAeA,IAAI;AAAC,IAAAiI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}