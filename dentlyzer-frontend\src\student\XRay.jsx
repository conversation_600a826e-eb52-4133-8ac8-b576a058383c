import { useState, useEffect, useRef } from 'react';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import PatientNav from './PatientNav';
import axios from 'axios';
import { useParams } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { motion } from 'framer-motion';
import { FaXRay, FaEdit, FaTrash, FaPlus, FaDownload } from 'react-icons/fa';
import Loader from '../components/Loader';

const XRays = () => {
  const { token } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [xrays, setXrays] = useState([]);
  const [editingNote, setEditingNote] = useState(null);
  const [currentNote, setCurrentNote] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedImage, setSelectedImage] = useState(null);
  const [showImageModal, setShowImageModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [xrayToDelete, setXrayToDelete] = useState(null);
  const [notification, setNotification] = useState({ show: false, message: '', type: 'success' });
  const fileInputRef = useRef(null);
  const { nationalId } = useParams();

  useEffect(() => {
    const fetchXrays = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/patients/${nationalId}`, {
          headers: { Authorization: `Bearer ${token}` },
        });

        if (response.data && response.data.xrays) {
          setXrays(response.data.xrays || []);
        } else {
          setError('No X-ray data found for this patient');
        }
      } catch (err) {
        console.error('Error fetching X-rays:', err);
        setError(err.response?.data?.message || 'Failed to load X-rays');
      } finally {
        setLoading(false);
      }
    };

    if (token && nationalId) {
      fetchXrays();
    }
  }, [nationalId, token]);

  const handleXrayUpload = async (e) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const formData = new FormData();
    Array.from(files).forEach(file => formData.append('xrays', file));

    try {
      setLoading(true);
      const response = await axios.post(
        `${process.env.REACT_APP_API_URL}/api/patients/${nationalId}/xrays`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'multipart/form-data'
          },
        }
      );

      if (response.data) {
        setXrays(response.data);
        // Show success notification
        setNotification({
          show: true,
          message: 'X-rays uploaded successfully!',
          type: 'success'
        });

        // Hide notification after 3 seconds
        setTimeout(() => {
          setNotification({ show: false, message: '', type: 'success' });
        }, 3000);
      }
    } catch (err) {
      console.error('Upload error:', err);
      setError(err.response?.data?.message || 'Failed to upload X-rays');
    } finally {
      setLoading(false);
    }
  };

  const handleAddNote = async (id) => {
    if (!currentNote.trim()) {
      setEditingNote(null);
      return;
    }

    try {
      const response = await axios.put(
        `${process.env.REACT_APP_API_URL}/api/patients/${nationalId}/xrays/note`,
        { xrayId: id, note: currentNote },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      if (response.data) {
        // Update the specific X-ray with the new note
        setXrays(xrays.map(xray =>
          xray._id === id ? { ...xray, note: currentNote } : xray
        ));
      }

      setEditingNote(null);
      setCurrentNote('');
    } catch (err) {
      console.error('Note update error:', err);
      setError(err.response?.data?.message || 'Failed to update note');
    }
  };

  const confirmDeleteXray = (xray) => {
    setXrayToDelete(xray);
    setShowDeleteModal(true);
  };

  const handleDeleteXray = async () => {
    if (!xrayToDelete) return;

    try {
      await axios.delete(
        `${process.env.REACT_APP_API_URL}/api/patients/${nationalId}/xrays/${xrayToDelete._id}`,
        { headers: { Authorization: `Bearer ${token}` } }
      );

      // Remove the deleted X-ray from state
      setXrays(xrays.filter(xray => xray._id !== xrayToDelete._id));

      // Show success notification
      setNotification({
        show: true,
        message: 'X-ray deleted successfully',
        type: 'success'
      });

      // Hide notification after 3 seconds
      setTimeout(() => {
        setNotification({ show: false, message: '', type: 'success' });
      }, 3000);

      // Close the delete modal
      setShowDeleteModal(false);
      setXrayToDelete(null);
    } catch (err) {
      console.error('Delete error:', err);
      setError(err.response?.data?.message || 'Failed to delete X-ray');
      setShowDeleteModal(false);
    }
  };

  const openImageModal = (xray) => {
    setSelectedImage(xray);
    setShowImageModal(true);
  };

  const triggerFileInput = () => fileInputRef.current.click();

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  if (loading) {
    return <Loader />;
  }

  if (error) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
          <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-blue-50 to-white">
            <div className="max-w-7xl mx-auto">
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                className="text-center max-w-md bg-white p-8 rounded-xl shadow-sm border border-gray-100"
              >
                <div className="text-red-500 mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-12 w-12 mx-auto"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">Error loading X-rays</h3>
                <p className="text-gray-600 mb-6">{error}</p>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => window.location.reload()}
                  className="px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-full hover:from-blue-600 hover:to-blue-800 font-medium shadow-md"
                >
                  Try Again
                </motion.button>
              </motion.div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <PatientNav />
        <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-blue-50 to-white">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-blue-900 mb-1">
                    X-Ray Images
                  </h1>
                  <p className="text-gray-600">Manage patient X-ray images</p>
                </div>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={triggerFileInput}
                  className="w-full md:w-auto bg-gradient-to-r from-blue-500 to-blue-700 text-white px-6 py-2.5 rounded-full font-medium hover:from-blue-600 hover:to-blue-800 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center"
                >
                  <FaXRay className="h-5 w-5 mr-2" />
                  Add X-Rays
                </motion.button>
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleXrayUpload}
                  className="hidden"
                  accept="image/*"
                  multiple
                />
              </div>
              {xrays.length === 0 ? (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="bg-white rounded-xl shadow-sm p-8 text-center border border-gray-100"
                >
                  <div className="mx-auto h-16 w-16 text-gray-400 mb-4">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">No X-rays found</h3>
                  <p className="text-gray-500">Get started by adding new X-ray images.</p>
                </motion.div>
              ) : (
                <motion.div
                  variants={container}
                  initial="hidden"
                  animate="show"
                  className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
                >
                  {xrays.map((xray) => (
                    <motion.div
                      key={xray._id}
                      variants={item}
                      className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden"
                    >
                      <div className="relative group">
                        <img
                          src={xray.url.startsWith('http') ? xray.url : `${process.env.REACT_APP_API_URL}/${xray.url}`}
                          alt="X-Ray"
                          className="w-full h-48 object-contain bg-black cursor-pointer"
                          onClick={() => openImageModal(xray)}
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                          <button
                            onClick={() => openImageModal(xray)}
                            className="bg-white text-blue-600 p-2 rounded-full mx-1 hover:bg-blue-50"
                          >
                            <FaPlus className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => confirmDeleteXray(xray)}
                            className="bg-white text-red-600 p-2 rounded-full mx-1 hover:bg-red-50"
                          >
                            <FaTrash className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                      <div className="p-4">
                        <div className="flex justify-between items-start">
                          <span className="text-sm text-gray-500">
                            {new Date(xray.date).toLocaleDateString()}
                          </span>
                          <button
                            onClick={() => {
                              setEditingNote(xray._id);
                              setCurrentNote(xray.note || '');
                            }}
                            className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
                          >
                            <FaEdit className="h-3 w-3 mr-1" />
                            {xray.note ? 'Edit Note' : 'Add Note'}
                          </button>
                        </div>
                        {editingNote === xray._id ? (
                          <div className="mt-2">
                            <textarea
                              value={currentNote}
                              onChange={(e) => setCurrentNote(e.target.value)}
                              className="w-full border border-gray-300 rounded-md p-2 text-sm"
                              rows="2"
                              placeholder="Add a note about this X-ray..."
                            />
                            <div className="flex justify-end gap-2 mt-2">
                              <button
                                onClick={() => setEditingNote(null)}
                                className="text-sm text-gray-600 hover:text-gray-800 px-2 py-1"
                              >
                                Cancel
                              </button>
                              <button
                                onClick={() => handleAddNote(xray._id)}
                                className="bg-blue-600 text-white px-3 py-1 rounded-md text-sm hover:bg-blue-700"
                              >
                                Save
                              </button>
                            </div>
                          </div>
                        ) : (
                          xray.note && <p className="mt-2 text-sm text-gray-700">{xray.note}</p>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              )}
            </motion.div>
          </div>
        </main>
      </div>

      {/* Success notification */}
      {notification.show && (
        <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
          notification.type === 'success' ? 'bg-green-100 border-l-4 border-green-500' :
          notification.type === 'error' ? 'bg-red-100 border-l-4 border-red-500' :
          'bg-blue-100 border-l-4 border-blue-500'
        }`}>
          <div className="flex items-center">
            <div className={`flex-shrink-0 ${
              notification.type === 'success' ? 'text-green-500' :
              notification.type === 'error' ? 'text-red-500' :
              'text-blue-500'
            }`}>
              {notification.type === 'success' ? (
                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              ) : notification.type === 'error' ? (
                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              )}
            </div>
            <div className="ml-3">
              <p className={`text-sm font-medium ${
                notification.type === 'success' ? 'text-green-800' :
                notification.type === 'error' ? 'text-red-800' :
                'text-blue-800'
              }`}>
                {notification.message}
              </p>
            </div>
            <div className="ml-auto pl-3">
              <div className="-mx-1.5 -my-1.5">
                <button
                  onClick={() => setNotification({ show: false, message: '', type: 'success' })}
                  className={`inline-flex rounded-md p-1.5 ${
                    notification.type === 'success' ? 'text-green-500 hover:bg-green-200' :
                    notification.type === 'error' ? 'text-red-500 hover:bg-red-200' :
                    'text-blue-500 hover:bg-blue-200'
                  } focus:outline-none`}
                >
                  <span className="sr-only">Dismiss</span>
                  <svg className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Full-size image modal */}
      {showImageModal && selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl w-full">
            <button
              onClick={() => setShowImageModal(false)}
              className="absolute top-4 right-4 bg-white rounded-full p-2 text-gray-800 hover:bg-gray-200"
            >
              ✕
            </button>
            <img
              src={selectedImage.url.startsWith('http') ? selectedImage.url : `${process.env.REACT_APP_API_URL}/${selectedImage.url}`}
              alt="X-Ray Full View"
              className="w-full max-h-[80vh] object-contain bg-black rounded-lg"
            />
            <div className="bg-white p-4 rounded-b-lg">
              <p className="text-sm text-gray-500">
                {new Date(selectedImage.date).toLocaleDateString()}
              </p>
              {selectedImage.note && (
                <p className="mt-2 text-gray-700">{selectedImage.note}</p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Delete confirmation modal */}
      {showDeleteModal && xrayToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-xl p-6 w-full max-w-md shadow-2xl border border-gray-100"
          >
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-blue-900">Confirm Deletion</h2>
              <button
                onClick={() => setShowDeleteModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <p className="text-gray-700 mb-6">
              Are you sure you want to delete this X-ray? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setShowDeleteModal(false)}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 font-medium transition-colors"
              >
                Cancel
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleDeleteXray}
                className="px-6 py-2 bg-gradient-to-r from-red-500 to-red-700 text-white rounded-full hover:from-red-600 hover:to-red-800 font-medium transition-colors shadow-md"
              >
                Delete X-ray
              </motion.button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default XRays;