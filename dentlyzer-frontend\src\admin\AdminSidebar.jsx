import { Link } from 'react-router-dom';
import {
  FaHome,
  FaUsers,
  FaCalendarAlt,
  FaChartLine,
  FaNewspaper,
  FaStar,
  FaTooth,
  FaFlask,
} from 'react-icons/fa';

// Website color palette
const colorPalette = {
  primary: '#0077B6',
  secondary: '#20B2AA',
  background: '#FFFFFF',
  text: '#333333',
  accent: '#28A745'
};

const AdminSidebar = ({ isOpen, setIsOpen }) => {
  const navItems = [
    { name: 'Dashboard', icon: <FaHome className="h-5 w-5" />, path: '/admin/dashboard' },
    { name: 'People', icon: <FaUsers className="h-5 w-5" />, path: '/admin/people' },
    { name: 'Appointments', icon: <FaCalendarAlt className="h-5 w-5" />, path: '/admin/appointments' },
    { name: 'Analytics', icon: <FaChartLine className="h-5 w-5" />, path: '/admin/analytics' },
    { name: 'Reviews', icon: <FaStar className="h-5 w-5" />, path: '/admin/reviews' },
    { name: 'Lab Requests', icon: <FaFlask className="h-5 w-5" />, path: '/admin/lab-requests' },
    { name: 'News', icon: <FaNewspaper className="h-5 w-5" />, path: '/admin/news' },
  ];

  const user = JSON.parse(localStorage.getItem('user')) || { name: 'Admin', role: 'admin' };

  return (
    <>
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-10 md:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}
      <div
        className={`fixed md:relative z-20 h-full transition-all duration-300 ease-in-out ${
          isOpen ? 'translate-x-0 w-64' : '-translate-x-full md:translate-x-0 w-20'
        } bg-white shadow-lg flex flex-col`}
      >
        <div className="p-4 flex items-center justify-center">
          {isOpen && (
          <Link to="/">
                    <img 
                      src="/imgs/odenta-logo2.jpg" // Update this path based on your project structure
                      alt="ODenta Logo"
                      className="h-10 w-auto" // Adjust size as needed
                    />
            </Link>
          )}
        </div>
        <nav className="flex-1 px-2 py-4 overflow-y-auto">
          <ul className="space-y-2">
            {navItems.map((item) => (
              <li key={item.name}>
                <Link
                  to={item.path}
                  className={`flex items-center p-3 text-[${colorPalette.text}] hover:text-[${colorPalette.primary}] hover:bg-blue-50 rounded-lg transition-colors group`}
                  onClick={() => window.innerWidth < 768 && setIsOpen(false)}
                >
                  <span className={`text-gray-500 group-hover:text-[${colorPalette.primary}]`}>{item.icon}</span>
                  {isOpen && <span className="ml-3">{item.name}</span>}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
        <div className="p-4 border-t border-gray-200">
          <Link to="/profile" className="flex items-center">
            <div className={`h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center text-[${colorPalette.primary}]`}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            {isOpen && (
              <div className="ml-3">
                <p className={`text-sm font-medium text-[${colorPalette.text}]`}>{user.name}</p>
                <p className={`text-xs text-[${colorPalette.primary}] capitalize`}>{user.role}</p>
              </div>
            )}
          </Link>
        </div>
      </div>
    </>
  );
};

export default AdminSidebar;