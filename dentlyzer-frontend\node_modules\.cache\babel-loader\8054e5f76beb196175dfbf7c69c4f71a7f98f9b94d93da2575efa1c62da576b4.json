{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\components\\\\Support.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { FaEnvelope, FaPhone, FaWhatsapp, FaTooth, FaArrowLeft } from 'react-icons/fa';\nimport DOMPurify from 'dompurify';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Support = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [previousPath, setPreviousPath] = useState('/');\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    message: ''\n  });\n  const [formStatus, setFormStatus] = useState('');\n  const [isSubmitDisabled, setIsSubmitDisabled] = useState(false);\n\n  // Get the previous path from the location state or default to dashboard\n  useEffect(() => {\n    if (location.state && location.state.from) {\n      setPreviousPath(location.state.from);\n    } else {\n      // If no previous path is provided, try to determine a sensible default\n      const user = JSON.parse(localStorage.getItem('user')) || {};\n      const role = user.role || 'student';\n      switch (role) {\n        case 'student':\n          setPreviousPath('/student/dashboard');\n          break;\n        case 'supervisor':\n          setPreviousPath('/supervisor/dashboard');\n          break;\n        case 'admin':\n          setPreviousPath('/admin/dashboard');\n          break;\n        case 'superadmin':\n          setPreviousPath('/superadmin/dashboard');\n          break;\n        case 'dentist':\n          setPreviousPath('/dentist/dashboard');\n          break;\n        default:\n          setPreviousPath('/');\n      }\n    }\n  }, [location]);\n\n  // Sanitize input to prevent XSS\n  const sanitizeInput = input => {\n    return DOMPurify.sanitize(input, {\n      ALLOWED_TAGS: [],\n      ALLOWED_ATTR: []\n    });\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (isSubmitDisabled) return;\n    setIsSubmitDisabled(true);\n    setFormStatus('');\n\n    // Sanitize form inputs\n    const sanitizedFormData = {\n      name: sanitizeInput(formData.name),\n      email: sanitizeInput(formData.email),\n      message: sanitizeInput(formData.message)\n    };\n\n    // Validate email format\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!emailRegex.test(sanitizedFormData.email)) {\n      setFormStatus('Please enter a valid email address.');\n      setIsSubmitDisabled(false);\n      return;\n    }\n    try {\n      // Simulate API call\n      setTimeout(() => {\n        setFormStatus('Your message has been sent successfully!');\n        setFormData({\n          name: '',\n          email: '',\n          message: ''\n        });\n        setTimeout(() => {\n          setFormStatus('');\n          setIsSubmitDisabled(false);\n        }, 3000);\n      }, 1000);\n    } catch (error) {\n      setFormStatus('There was an error sending your message. Please try again.');\n      setIsSubmitDisabled(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white py-12 px-4 sm:px-6 lg:px-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-5xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate(previousPath),\n          className: \"inline-flex items-center text-[#0077B6] hover:text-[#20B2AA] transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(FaArrowLeft, {\n            className: \"mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), \"Back to Previous Page\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.5\n        },\n        className: \"bg-white rounded-xl shadow-lg overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-[#0077B6] to-[#20B2AA] p-8 text-white text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center items-center mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/imgs/odenta-logo2.jpg\" // Update this path based on your project structure\n              ,\n              alt: \"ODenta Logo\",\n              className: \"h-10 w-auto\" // Adjust size as needed\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-white text-opacity-90 text-lg max-w-2xl mx-auto\",\n            children: \"We're here to help you with any questions or issues you might have. Our support team is ready to assist you.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl font-bold text-[#0077B6] mb-6\",\n                children: \"Contact Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[rgba(0,119,182,0.1)] rounded-full p-3 mr-4\",\n                    children: /*#__PURE__*/_jsxDEV(FaEnvelope, {\n                      className: \"h-6 w-6 text-[#0077B6]\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 140,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-semibold text-[#333333]\",\n                      children: \"Email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 143,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"mailto:<EMAIL>\",\n                      className: \"text-[#0077B6] hover:text-[#20B2AA] transition-colors\",\n                      children: \"<EMAIL>\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 144,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 142,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[rgba(0,119,182,0.1)] rounded-full p-3 mr-4\",\n                    children: /*#__PURE__*/_jsxDEV(FaPhone, {\n                      className: \"h-6 w-6 text-[#0077B6]\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 155,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 154,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-semibold text-[#333333]\",\n                      children: \"Phone\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 158,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"tel:+201276902211\",\n                      className: \"text-[#0077B6] hover:text-[#20B2AA] transition-colors\",\n                      children: \"+20 ************\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 159,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 157,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[rgba(0,119,182,0.1)] rounded-full p-3 mr-4\",\n                    children: /*#__PURE__*/_jsxDEV(FaWhatsapp, {\n                      className: \"h-6 w-6 text-[#0077B6]\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 170,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"font-semibold text-[#333333]\",\n                      children: \"WhatsApp\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 173,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"https://wa.me/201276902211\",\n                      target: \"_blank\",\n                      rel: \"noopener noreferrer\",\n                      className: \"text-[#0077B6] hover:text-[#20B2AA] transition-colors\",\n                      children: \"+20 ************\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 174,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 172,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-8 p-6 bg-[rgba(0,119,182,0.05)] rounded-lg border border-[rgba(0,119,182,0.1)]\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-bold text-[#0077B6] mb-3\",\n                  children: \"About Dentlyzer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-[#333333] mb-4\",\n                  children: \"Dentlyzer is a comprehensive dental management system designed to streamline dental education and practice. Our platform connects dental students, supervisors, and clinics to provide a seamless experience.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-[#333333]\",\n                  children: \"Our support team is available Monday through Friday, 9 AM to 5 PM (EET). We typically respond to inquiries within 24 hours.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-2xl font-bold text-[#0077B6] mb-6\",\n                children: \"Send Us a Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"name\",\n                    className: \"block text-[#333333] font-medium mb-2\",\n                    children: \"Your Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    id: \"name\",\n                    name: \"name\",\n                    value: formData.name,\n                    onChange: handleInputChange,\n                    required: true,\n                    className: \"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#20B2AA]\",\n                    placeholder: \"Enter your name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"email\",\n                    className: \"block text-[#333333] font-medium mb-2\",\n                    children: \"Email Address\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"email\",\n                    id: \"email\",\n                    name: \"email\",\n                    value: formData.email,\n                    onChange: handleInputChange,\n                    required: true,\n                    className: \"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#20B2AA]\",\n                    placeholder: \"Enter your email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"message\",\n                    className: \"block text-[#333333] font-medium mb-2\",\n                    children: \"Message\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    id: \"message\",\n                    name: \"message\",\n                    value: formData.message,\n                    onChange: handleInputChange,\n                    required: true,\n                    className: \"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#20B2AA] h-32\",\n                    placeholder: \"How can we help you?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this), formStatus && /*#__PURE__*/_jsxDEV(motion.p, {\n                  initial: {\n                    opacity: 0\n                  },\n                  animate: {\n                    opacity: 1\n                  },\n                  className: `${formStatus.includes('error') ? 'text-red-600' : 'text-green-600'}`,\n                  children: formStatus\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: isSubmitDisabled ? 1 : 1.05\n                  },\n                  whileTap: {\n                    scale: isSubmitDisabled ? 1 : 0.95\n                  },\n                  type: \"submit\",\n                  disabled: isSubmitDisabled,\n                  className: `w-full bg-gradient-to-r ${isSubmitDisabled ? 'from-[#0077B6]/70 to-[#20B2AA]/70 cursor-not-allowed' : 'from-[#0077B6] to-[#20B2AA] hover:from-[#0066A0] hover:to-[#1A9E98]'} text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 shadow-md`,\n                  children: \"Send Message\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n};\n_s(Support, \"iZPR3NVXqh7ijfTmosjt5/ALZ6k=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = Support;\nexport default Support;\nvar _c;\n$RefreshReg$(_c, \"Support\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useNavigate", "useLocation", "FaEnvelope", "FaPhone", "FaWhatsapp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaArrowLeft", "DOMPurify", "jsxDEV", "_jsxDEV", "Support", "_s", "navigate", "location", "previousPath", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "formData", "setFormData", "name", "email", "message", "formStatus", "setFormStatus", "isSubmitDisabled", "setIsSubmitDisabled", "state", "from", "user", "JSON", "parse", "localStorage", "getItem", "role", "sanitizeInput", "input", "sanitize", "ALLOWED_TAGS", "ALLOWED_ATTR", "handleInputChange", "e", "value", "target", "handleSubmit", "preventDefault", "sanitizedFormData", "emailRegex", "test", "setTimeout", "error", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "transition", "duration", "src", "alt", "href", "rel", "onSubmit", "htmlFor", "type", "id", "onChange", "required", "placeholder", "p", "includes", "button", "whileHover", "scale", "whileTap", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/components/Support.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { FaEnvelope, FaPhone, FaWhatsapp, FaTooth, FaArrowLeft } from 'react-icons/fa';\nimport DOMPurify from 'dompurify';\n\nconst Support = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [previousPath, setPreviousPath] = useState('/');\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    message: ''\n  });\n  const [formStatus, setFormStatus] = useState('');\n  const [isSubmitDisabled, setIsSubmitDisabled] = useState(false);\n\n  // Get the previous path from the location state or default to dashboard\n  useEffect(() => {\n    if (location.state && location.state.from) {\n      setPreviousPath(location.state.from);\n    } else {\n      // If no previous path is provided, try to determine a sensible default\n      const user = JSON.parse(localStorage.getItem('user')) || {};\n      const role = user.role || 'student';\n\n      switch (role) {\n        case 'student':\n          setPreviousPath('/student/dashboard');\n          break;\n        case 'supervisor':\n          setPreviousPath('/supervisor/dashboard');\n          break;\n        case 'admin':\n          setPreviousPath('/admin/dashboard');\n          break;\n        case 'superadmin':\n          setPreviousPath('/superadmin/dashboard');\n          break;\n        case 'dentist':\n          setPreviousPath('/dentist/dashboard');\n          break;\n        default:\n          setPreviousPath('/');\n      }\n    }\n  }, [location]);\n\n  // Sanitize input to prevent XSS\n  const sanitizeInput = (input) => {\n    return DOMPurify.sanitize(input, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] });\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({ ...formData, [name]: value });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    if (isSubmitDisabled) return;\n\n    setIsSubmitDisabled(true);\n    setFormStatus('');\n\n    // Sanitize form inputs\n    const sanitizedFormData = {\n      name: sanitizeInput(formData.name),\n      email: sanitizeInput(formData.email),\n      message: sanitizeInput(formData.message)\n    };\n\n    // Validate email format\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!emailRegex.test(sanitizedFormData.email)) {\n      setFormStatus('Please enter a valid email address.');\n      setIsSubmitDisabled(false);\n      return;\n    }\n\n    try {\n      // Simulate API call\n      setTimeout(() => {\n        setFormStatus('Your message has been sent successfully!');\n        setFormData({ name: '', email: '', message: '' });\n        setTimeout(() => {\n          setFormStatus('');\n          setIsSubmitDisabled(false);\n        }, 3000);\n      }, 1000);\n    } catch (error) {\n      setFormStatus('There was an error sending your message. Please try again.');\n      setIsSubmitDisabled(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-5xl mx-auto\">\n        <div className=\"mb-8\">\n          <button\n            onClick={() => navigate(previousPath)}\n            className=\"inline-flex items-center text-[#0077B6] hover:text-[#20B2AA] transition-colors\"\n          >\n            <FaArrowLeft className=\"mr-2\" />\n            Back to Previous Page\n          </button>\n        </div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n          className=\"bg-white rounded-xl shadow-lg overflow-hidden\"\n        >\n          {/* Header */}\n          <div className=\"bg-gradient-to-r from-[#0077B6] to-[#20B2AA] p-8 text-white text-center\">\n            <div className=\"flex justify-center items-center mb-4\">\n                    <img \n                      src=\"/imgs/odenta-logo2.jpg\" // Update this path based on your project structure\n                      alt=\"ODenta Logo\"\n                      className=\"h-10 w-auto\" // Adjust size as needed\n                    />\n            </div>\n            <p className=\"text-white text-opacity-90 text-lg max-w-2xl mx-auto\">\n              We're here to help you with any questions or issues you might have.\n              Our support team is ready to assist you.\n            </p>\n          </div>\n\n          <div className=\"p-8\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-12\">\n              {/* Contact Information */}\n              <div>\n                <h2 className=\"text-2xl font-bold text-[#0077B6] mb-6\">Contact Information</h2>\n                <div className=\"space-y-6\">\n                  <div className=\"flex items-start\">\n                    <div className=\"bg-[rgba(0,119,182,0.1)] rounded-full p-3 mr-4\">\n                      <FaEnvelope className=\"h-6 w-6 text-[#0077B6]\" />\n                    </div>\n                    <div>\n                      <h3 className=\"font-semibold text-[#333333]\">Email</h3>\n                      <a\n                        href=\"mailto:<EMAIL>\"\n                        className=\"text-[#0077B6] hover:text-[#20B2AA] transition-colors\"\n                      >\n                        <EMAIL>\n                      </a>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-start\">\n                    <div className=\"bg-[rgba(0,119,182,0.1)] rounded-full p-3 mr-4\">\n                      <FaPhone className=\"h-6 w-6 text-[#0077B6]\" />\n                    </div>\n                    <div>\n                      <h3 className=\"font-semibold text-[#333333]\">Phone</h3>\n                      <a\n                        href=\"tel:+201276902211\"\n                        className=\"text-[#0077B6] hover:text-[#20B2AA] transition-colors\"\n                      >\n                        +20 ************\n                      </a>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-start\">\n                    <div className=\"bg-[rgba(0,119,182,0.1)] rounded-full p-3 mr-4\">\n                      <FaWhatsapp className=\"h-6 w-6 text-[#0077B6]\" />\n                    </div>\n                    <div>\n                      <h3 className=\"font-semibold text-[#333333]\">WhatsApp</h3>\n                      <a\n                        href=\"https://wa.me/201276902211\"\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"text-[#0077B6] hover:text-[#20B2AA] transition-colors\"\n                      >\n                        +20 ************\n                      </a>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"mt-8 p-6 bg-[rgba(0,119,182,0.05)] rounded-lg border border-[rgba(0,119,182,0.1)]\">\n                  <h3 className=\"text-xl font-bold text-[#0077B6] mb-3\">About Dentlyzer</h3>\n                  <p className=\"text-[#333333] mb-4\">\n                    Dentlyzer is a comprehensive dental management system designed to streamline\n                    dental education and practice. Our platform connects dental students,\n                    supervisors, and clinics to provide a seamless experience.\n                  </p>\n                  <p className=\"text-[#333333]\">\n                    Our support team is available Monday through Friday, 9 AM to 5 PM (EET).\n                    We typically respond to inquiries within 24 hours.\n                  </p>\n                </div>\n              </div>\n\n              {/* Contact Form */}\n              <div>\n                <h2 className=\"text-2xl font-bold text-[#0077B6] mb-6\">Send Us a Message</h2>\n                <form onSubmit={handleSubmit} className=\"space-y-6\">\n                  <div>\n                    <label htmlFor=\"name\" className=\"block text-[#333333] font-medium mb-2\">\n                      Your Name\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"name\"\n                      name=\"name\"\n                      value={formData.name}\n                      onChange={handleInputChange}\n                      required\n                      className=\"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#20B2AA]\"\n                      placeholder=\"Enter your name\"\n                    />\n                  </div>\n                  <div>\n                    <label htmlFor=\"email\" className=\"block text-[#333333] font-medium mb-2\">\n                      Email Address\n                    </label>\n                    <input\n                      type=\"email\"\n                      id=\"email\"\n                      name=\"email\"\n                      value={formData.email}\n                      onChange={handleInputChange}\n                      required\n                      className=\"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#20B2AA]\"\n                      placeholder=\"Enter your email\"\n                    />\n                  </div>\n                  <div>\n                    <label htmlFor=\"message\" className=\"block text-[#333333] font-medium mb-2\">\n                      Message\n                    </label>\n                    <textarea\n                      id=\"message\"\n                      name=\"message\"\n                      value={formData.message}\n                      onChange={handleInputChange}\n                      required\n                      className=\"w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#20B2AA] h-32\"\n                      placeholder=\"How can we help you?\"\n                    ></textarea>\n                  </div>\n                  {formStatus && (\n                    <motion.p\n                      initial={{ opacity: 0 }}\n                      animate={{ opacity: 1 }}\n                      className={`${formStatus.includes('error') ? 'text-red-600' : 'text-green-600'}`}\n                    >\n                      {formStatus}\n                    </motion.p>\n                  )}\n                  <motion.button\n                    whileHover={{ scale: isSubmitDisabled ? 1 : 1.05 }}\n                    whileTap={{ scale: isSubmitDisabled ? 1 : 0.95 }}\n                    type=\"submit\"\n                    disabled={isSubmitDisabled}\n                    className={`w-full bg-gradient-to-r ${\n                      isSubmitDisabled\n                        ? 'from-[#0077B6]/70 to-[#20B2AA]/70 cursor-not-allowed'\n                        : 'from-[#0077B6] to-[#20B2AA] hover:from-[#0066A0] hover:to-[#1A9E98]'\n                    } text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 shadow-md`}\n                  >\n                    Send Message\n                  </motion.button>\n                </form>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\nexport default Support;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,UAAU,EAAEC,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAEC,WAAW,QAAQ,gBAAgB;AACtF,OAAOC,SAAS,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAMa,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,GAAG,CAAC;EACrD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC;IACvCqB,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACAC,SAAS,CAAC,MAAM;IACd,IAAIe,QAAQ,CAACY,KAAK,IAAIZ,QAAQ,CAACY,KAAK,CAACC,IAAI,EAAE;MACzCX,eAAe,CAACF,QAAQ,CAACY,KAAK,CAACC,IAAI,CAAC;IACtC,CAAC,MAAM;MACL;MACA,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;MAC3D,MAAMC,IAAI,GAAGL,IAAI,CAACK,IAAI,IAAI,SAAS;MAEnC,QAAQA,IAAI;QACV,KAAK,SAAS;UACZjB,eAAe,CAAC,oBAAoB,CAAC;UACrC;QACF,KAAK,YAAY;UACfA,eAAe,CAAC,uBAAuB,CAAC;UACxC;QACF,KAAK,OAAO;UACVA,eAAe,CAAC,kBAAkB,CAAC;UACnC;QACF,KAAK,YAAY;UACfA,eAAe,CAAC,uBAAuB,CAAC;UACxC;QACF,KAAK,SAAS;UACZA,eAAe,CAAC,oBAAoB,CAAC;UACrC;QACF;UACEA,eAAe,CAAC,GAAG,CAAC;MACxB;IACF;EACF,CAAC,EAAE,CAACF,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMoB,aAAa,GAAIC,KAAK,IAAK;IAC/B,OAAO3B,SAAS,CAAC4B,QAAQ,CAACD,KAAK,EAAE;MAAEE,YAAY,EAAE,EAAE;MAAEC,YAAY,EAAE;IAAG,CAAC,CAAC;EAC1E,CAAC;EAED,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAErB,IAAI;MAAEsB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCxB,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACE,IAAI,GAAGsB;IAAM,CAAC,CAAC;EAC7C,CAAC;EAED,MAAME,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB,IAAIpB,gBAAgB,EAAE;IAEtBC,mBAAmB,CAAC,IAAI,CAAC;IACzBF,aAAa,CAAC,EAAE,CAAC;;IAEjB;IACA,MAAMsB,iBAAiB,GAAG;MACxB1B,IAAI,EAAEe,aAAa,CAACjB,QAAQ,CAACE,IAAI,CAAC;MAClCC,KAAK,EAAEc,aAAa,CAACjB,QAAQ,CAACG,KAAK,CAAC;MACpCC,OAAO,EAAEa,aAAa,CAACjB,QAAQ,CAACI,OAAO;IACzC,CAAC;;IAED;IACA,MAAMyB,UAAU,GAAG,4BAA4B;IAC/C,IAAI,CAACA,UAAU,CAACC,IAAI,CAACF,iBAAiB,CAACzB,KAAK,CAAC,EAAE;MAC7CG,aAAa,CAAC,qCAAqC,CAAC;MACpDE,mBAAmB,CAAC,KAAK,CAAC;MAC1B;IACF;IAEA,IAAI;MACF;MACAuB,UAAU,CAAC,MAAM;QACfzB,aAAa,CAAC,0CAA0C,CAAC;QACzDL,WAAW,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAG,CAAC,CAAC;QACjD2B,UAAU,CAAC,MAAM;UACfzB,aAAa,CAAC,EAAE,CAAC;UACjBE,mBAAmB,CAAC,KAAK,CAAC;QAC5B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACd1B,aAAa,CAAC,4DAA4D,CAAC;MAC3EE,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,oBACEf,OAAA;IAAKwC,SAAS,EAAC,gGAAgG;IAAAC,QAAA,eAC7GzC,OAAA;MAAKwC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCzC,OAAA;QAAKwC,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBzC,OAAA;UACE0C,OAAO,EAAEA,CAAA,KAAMvC,QAAQ,CAACE,YAAY,CAAE;UACtCmC,SAAS,EAAC,gFAAgF;UAAAC,QAAA,gBAE1FzC,OAAA,CAACH,WAAW;YAAC2C,SAAS,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,yBAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN9C,OAAA,CAACV,MAAM,CAACyD,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9Bb,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAGzDzC,OAAA;UAAKwC,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACtFzC,OAAA;YAAKwC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eAC9CzC,OAAA;cACEsD,GAAG,EAAC,wBAAwB,CAAC;cAAA;cAC7BC,GAAG,EAAC,aAAa;cACjBf,SAAS,EAAC,aAAa,CAAC;YAAA;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACN9C,OAAA;YAAGwC,SAAS,EAAC,sDAAsD;YAAAC,QAAA,EAAC;UAGpE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN9C,OAAA;UAAKwC,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBzC,OAAA;YAAKwC,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBAErDzC,OAAA;cAAAyC,QAAA,gBACEzC,OAAA;gBAAIwC,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/E9C,OAAA;gBAAKwC,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBzC,OAAA;kBAAKwC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BzC,OAAA;oBAAKwC,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,eAC7DzC,OAAA,CAACP,UAAU;sBAAC+C,SAAS,EAAC;oBAAwB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC,eACN9C,OAAA;oBAAAyC,QAAA,gBACEzC,OAAA;sBAAIwC,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACvD9C,OAAA;sBACEwD,IAAI,EAAC,8BAA8B;sBACnChB,SAAS,EAAC,uDAAuD;sBAAAC,QAAA,EAClE;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN9C,OAAA;kBAAKwC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BzC,OAAA;oBAAKwC,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,eAC7DzC,OAAA,CAACN,OAAO;sBAAC8C,SAAS,EAAC;oBAAwB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,eACN9C,OAAA;oBAAAyC,QAAA,gBACEzC,OAAA;sBAAIwC,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACvD9C,OAAA;sBACEwD,IAAI,EAAC,mBAAmB;sBACxBhB,SAAS,EAAC,uDAAuD;sBAAAC,QAAA,EAClE;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN9C,OAAA;kBAAKwC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BzC,OAAA;oBAAKwC,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,eAC7DzC,OAAA,CAACL,UAAU;sBAAC6C,SAAS,EAAC;oBAAwB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC,eACN9C,OAAA;oBAAAyC,QAAA,gBACEzC,OAAA;sBAAIwC,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1D9C,OAAA;sBACEwD,IAAI,EAAC,4BAA4B;sBACjCxB,MAAM,EAAC,QAAQ;sBACfyB,GAAG,EAAC,qBAAqB;sBACzBjB,SAAS,EAAC,uDAAuD;sBAAAC,QAAA,EAClE;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN9C,OAAA;gBAAKwC,SAAS,EAAC,mFAAmF;gBAAAC,QAAA,gBAChGzC,OAAA;kBAAIwC,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1E9C,OAAA;kBAAGwC,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAInC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ9C,OAAA;kBAAGwC,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAG9B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN9C,OAAA;cAAAyC,QAAA,gBACEzC,OAAA;gBAAIwC,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7E9C,OAAA;gBAAM0D,QAAQ,EAAEzB,YAAa;gBAACO,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACjDzC,OAAA;kBAAAyC,QAAA,gBACEzC,OAAA;oBAAO2D,OAAO,EAAC,MAAM;oBAACnB,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EAAC;kBAExE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR9C,OAAA;oBACE4D,IAAI,EAAC,MAAM;oBACXC,EAAE,EAAC,MAAM;oBACTpD,IAAI,EAAC,MAAM;oBACXsB,KAAK,EAAExB,QAAQ,CAACE,IAAK;oBACrBqD,QAAQ,EAAEjC,iBAAkB;oBAC5BkC,QAAQ;oBACRvB,SAAS,EAAC,yGAAyG;oBACnHwB,WAAW,EAAC;kBAAiB;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN9C,OAAA;kBAAAyC,QAAA,gBACEzC,OAAA;oBAAO2D,OAAO,EAAC,OAAO;oBAACnB,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EAAC;kBAEzE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR9C,OAAA;oBACE4D,IAAI,EAAC,OAAO;oBACZC,EAAE,EAAC,OAAO;oBACVpD,IAAI,EAAC,OAAO;oBACZsB,KAAK,EAAExB,QAAQ,CAACG,KAAM;oBACtBoD,QAAQ,EAAEjC,iBAAkB;oBAC5BkC,QAAQ;oBACRvB,SAAS,EAAC,yGAAyG;oBACnHwB,WAAW,EAAC;kBAAkB;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN9C,OAAA;kBAAAyC,QAAA,gBACEzC,OAAA;oBAAO2D,OAAO,EAAC,SAAS;oBAACnB,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EAAC;kBAE3E;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR9C,OAAA;oBACE6D,EAAE,EAAC,SAAS;oBACZpD,IAAI,EAAC,SAAS;oBACdsB,KAAK,EAAExB,QAAQ,CAACI,OAAQ;oBACxBmD,QAAQ,EAAEjC,iBAAkB;oBAC5BkC,QAAQ;oBACRvB,SAAS,EAAC,8GAA8G;oBACxHwB,WAAW,EAAC;kBAAsB;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,EACLlC,UAAU,iBACTZ,OAAA,CAACV,MAAM,CAAC2E,CAAC;kBACPjB,OAAO,EAAE;oBAAEC,OAAO,EAAE;kBAAE,CAAE;kBACxBE,OAAO,EAAE;oBAAEF,OAAO,EAAE;kBAAE,CAAE;kBACxBT,SAAS,EAAE,GAAG5B,UAAU,CAACsD,QAAQ,CAAC,OAAO,CAAC,GAAG,cAAc,GAAG,gBAAgB,EAAG;kBAAAzB,QAAA,EAEhF7B;gBAAU;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACX,eACD9C,OAAA,CAACV,MAAM,CAAC6E,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAEvD,gBAAgB,GAAG,CAAC,GAAG;kBAAK,CAAE;kBACnDwD,QAAQ,EAAE;oBAAED,KAAK,EAAEvD,gBAAgB,GAAG,CAAC,GAAG;kBAAK,CAAE;kBACjD8C,IAAI,EAAC,QAAQ;kBACbW,QAAQ,EAAEzD,gBAAiB;kBAC3B0B,SAAS,EAAE,2BACT1B,gBAAgB,GACZ,sDAAsD,GACtD,qEAAqE,oFACU;kBAAA2B,QAAA,EACtF;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5C,EAAA,CA/QID,OAAO;EAAA,QACMV,WAAW,EACXC,WAAW;AAAA;AAAAgF,EAAA,GAFxBvE,OAAO;AAiRb,eAAeA,OAAO;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}