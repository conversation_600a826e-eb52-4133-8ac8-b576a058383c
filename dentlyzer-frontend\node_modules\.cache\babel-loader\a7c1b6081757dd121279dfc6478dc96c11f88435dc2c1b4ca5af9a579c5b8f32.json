{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\superadmin\\\\Universities.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { motion } from 'framer-motion';\nimport { FaUniversity, FaEdit, FaTrash, FaPlus } from 'react-icons/fa';\nimport Navbar from '../student/Navbar';\nimport Loader from '../components/Loader';\nimport ConfirmModal from '../components/ConfirmModal';\nimport UniversityDetailsModal from '../components/UniversityDetailsModal';\nimport { useAuth } from '../context/AuthContext';\nimport SuperAdminSidebar from './SuperAdminSidebar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Universities = () => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [universities, setUniversities] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\n  const [showDetailsModal, setShowDetailsModal] = useState(false);\n  const [showUniversityModal, setShowUniversityModal] = useState(false);\n  const [selectedUniversityId, setSelectedUniversityId] = useState(null);\n  const [selectedUniversity, setSelectedUniversity] = useState(null);\n  const [isEditing, setIsEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    universityId: '',\n    name: {\n      en: '',\n      ar: ''\n    },\n    description: {\n      en: '',\n      ar: ''\n    },\n    dentistryInfo: {\n      en: '',\n      ar: ''\n    },\n    facilities: {\n      en: '',\n      ar: ''\n    },\n    program: {\n      en: '',\n      ar: ''\n    },\n    dentistryServices: [{\n      en: '',\n      ar: ''\n    }],\n    address: {\n      street: {\n        en: '',\n        ar: ''\n      },\n      city: {\n        en: '',\n        ar: ''\n      },\n      country: {\n        en: '',\n        ar: ''\n      },\n      postalCode: ''\n    },\n    contactInfo: {\n      phone: '',\n      email: '',\n      website: ''\n    },\n    slotBeginDate: '',\n    slotEndDate: '',\n    slotDuration: 120,\n    // Default slot duration in minutes\n    availableSlots: ['09:00', '11:30', '14:00'],\n    // Default available time slots\n    holidays: ['Friday', 'Sunday'],\n    // Default holidays\n    logo: '',\n    image: '',\n    mapUrl: ''\n  });\n  const navigate = useNavigate();\n  const {\n    user,\n    token\n  } = useAuth();\n  useEffect(() => {\n    const fetchUniversities = async () => {\n      if (!user || !token) {\n        setError('Please log in to view universities.');\n        setLoading(false);\n        return;\n      }\n      try {\n        const config = {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        };\n        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/universities`, config);\n        console.log('Universities response:', response.data); // Debug API response\n        setUniversities(response.data || []);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response3$data, _err$response4;\n        console.error('Fetch error:', ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n        const errorMessage = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.status) === 401 ? 'Unauthorized. Please log in again.' : ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || 'Failed to load universities';\n        setError(errorMessage);\n        if (((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 401) {\n          navigate('/login');\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchUniversities();\n  }, [user, token, navigate]);\n  const handleRowClick = async universityId => {\n    try {\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/universities/${universityId}`, config);\n      setSelectedUniversity(response.data);\n      setShowDetailsModal(true);\n    } catch (err) {\n      var _err$response5, _err$response5$data;\n      setError(((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : (_err$response5$data = _err$response5.data) === null || _err$response5$data === void 0 ? void 0 : _err$response5$data.message) || 'Failed to load university details');\n    }\n  };\n  const handleEditClick = (e, university) => {\n    var _university$name, _university$name2, _university$descripti, _university$descripti2, _university$dentistry, _university$dentistry2, _university$facilitie, _university$facilitie2, _university$program, _university$program2, _university$address, _university$address$s, _university$address2, _university$address2$, _university$address3, _university$address3$, _university$address4, _university$address4$, _university$address5, _university$address5$, _university$address6, _university$address6$, _university$address7, _university$contactIn, _university$contactIn2, _university$contactIn3;\n    e.stopPropagation();\n    console.log('Editing university:', university); // Debug university object\n\n    // Determine slot duration from university data\n    let slotDuration = 120; // Default value\n    if (university.slotDuration) {\n      // If slotDuration is directly available\n      slotDuration = university.slotDuration;\n    } else if (Array.isArray(university.timeSlots) && university.timeSlots.length > 0 && university.timeSlots[0].duration) {\n      // Extract from the first time slot if available\n      slotDuration = university.timeSlots[0].duration;\n    }\n\n    // Extract available time slots\n    let availableSlots = ['09:00', '11:30', '14:00']; // Default values\n    if (Array.isArray(university.timeSlots) && university.timeSlots.length > 0) {\n      // Get unique time slots\n      availableSlots = [...new Set(university.timeSlots.map(slot => slot.time))];\n    }\n    setFormData({\n      universityId: university.universityId || '',\n      name: {\n        en: ((_university$name = university.name) === null || _university$name === void 0 ? void 0 : _university$name.en) || '',\n        ar: ((_university$name2 = university.name) === null || _university$name2 === void 0 ? void 0 : _university$name2.ar) || ''\n      },\n      description: {\n        en: ((_university$descripti = university.description) === null || _university$descripti === void 0 ? void 0 : _university$descripti.en) || '',\n        ar: ((_university$descripti2 = university.description) === null || _university$descripti2 === void 0 ? void 0 : _university$descripti2.ar) || ''\n      },\n      dentistryInfo: {\n        en: ((_university$dentistry = university.dentistryInfo) === null || _university$dentistry === void 0 ? void 0 : _university$dentistry.en) || '',\n        ar: ((_university$dentistry2 = university.dentistryInfo) === null || _university$dentistry2 === void 0 ? void 0 : _university$dentistry2.ar) || ''\n      },\n      facilities: {\n        en: ((_university$facilitie = university.facilities) === null || _university$facilitie === void 0 ? void 0 : _university$facilitie.en) || '',\n        ar: ((_university$facilitie2 = university.facilities) === null || _university$facilitie2 === void 0 ? void 0 : _university$facilitie2.ar) || ''\n      },\n      program: {\n        en: ((_university$program = university.program) === null || _university$program === void 0 ? void 0 : _university$program.en) || '',\n        ar: ((_university$program2 = university.program) === null || _university$program2 === void 0 ? void 0 : _university$program2.ar) || ''\n      },\n      dentistryServices: Array.isArray(university.dentistryServices) && university.dentistryServices.length > 0 ? university.dentistryServices.map(service => ({\n        en: (service === null || service === void 0 ? void 0 : service.en) || '',\n        ar: (service === null || service === void 0 ? void 0 : service.ar) || ''\n      })) : [{\n        en: '',\n        ar: ''\n      }],\n      address: {\n        street: {\n          en: ((_university$address = university.address) === null || _university$address === void 0 ? void 0 : (_university$address$s = _university$address.street) === null || _university$address$s === void 0 ? void 0 : _university$address$s.en) || '',\n          ar: ((_university$address2 = university.address) === null || _university$address2 === void 0 ? void 0 : (_university$address2$ = _university$address2.street) === null || _university$address2$ === void 0 ? void 0 : _university$address2$.ar) || ''\n        },\n        city: {\n          en: ((_university$address3 = university.address) === null || _university$address3 === void 0 ? void 0 : (_university$address3$ = _university$address3.city) === null || _university$address3$ === void 0 ? void 0 : _university$address3$.en) || '',\n          ar: ((_university$address4 = university.address) === null || _university$address4 === void 0 ? void 0 : (_university$address4$ = _university$address4.city) === null || _university$address4$ === void 0 ? void 0 : _university$address4$.ar) || ''\n        },\n        country: {\n          en: ((_university$address5 = university.address) === null || _university$address5 === void 0 ? void 0 : (_university$address5$ = _university$address5.country) === null || _university$address5$ === void 0 ? void 0 : _university$address5$.en) || '',\n          ar: ((_university$address6 = university.address) === null || _university$address6 === void 0 ? void 0 : (_university$address6$ = _university$address6.country) === null || _university$address6$ === void 0 ? void 0 : _university$address6$.ar) || ''\n        },\n        postalCode: ((_university$address7 = university.address) === null || _university$address7 === void 0 ? void 0 : _university$address7.postalCode) || ''\n      },\n      contactInfo: {\n        phone: ((_university$contactIn = university.contactInfo) === null || _university$contactIn === void 0 ? void 0 : _university$contactIn.phone) || '',\n        email: ((_university$contactIn2 = university.contactInfo) === null || _university$contactIn2 === void 0 ? void 0 : _university$contactIn2.email) || '',\n        website: ((_university$contactIn3 = university.contactInfo) === null || _university$contactIn3 === void 0 ? void 0 : _university$contactIn3.website) || ''\n      },\n      slotBeginDate: university.slotBeginDate ? new Date(university.slotBeginDate).toISOString().split('T')[0] : '',\n      slotEndDate: university.slotEndDate ? new Date(university.slotEndDate).toISOString().split('T')[0] : '',\n      slotDuration: slotDuration,\n      availableSlots: availableSlots,\n      holidays: Array.isArray(university.holidays) ? university.holidays : ['Friday', 'Sunday'],\n      logo: university.logo || '',\n      image: university.image || '',\n      mapUrl: university.mapUrl || ''\n    });\n    setIsEditing(true);\n    setShowUniversityModal(true);\n  };\n  const handleAddUniversity = () => {\n    setFormData({\n      universityId: '',\n      name: {\n        en: '',\n        ar: ''\n      },\n      description: {\n        en: '',\n        ar: ''\n      },\n      dentistryInfo: {\n        en: '',\n        ar: ''\n      },\n      facilities: {\n        en: '',\n        ar: ''\n      },\n      program: {\n        en: '',\n        ar: ''\n      },\n      dentistryServices: [{\n        en: '',\n        ar: ''\n      }],\n      address: {\n        street: {\n          en: '',\n          ar: ''\n        },\n        city: {\n          en: '',\n          ar: ''\n        },\n        country: {\n          en: '',\n          ar: ''\n        },\n        postalCode: ''\n      },\n      contactInfo: {\n        phone: '',\n        email: '',\n        website: ''\n      },\n      slotBeginDate: '',\n      slotEndDate: '',\n      slotDuration: 120,\n      // Default slot duration in minutes\n      availableSlots: ['09:00', '11:30', '14:00'],\n      // Default available time slots\n      holidays: ['Friday', 'Sunday'],\n      // Default holidays\n      logo: '',\n      image: '',\n      mapUrl: ''\n    });\n    setIsEditing(false);\n    setShowUniversityModal(true);\n  };\n  const handleDeleteClick = (e, universityId) => {\n    e.stopPropagation();\n    setSelectedUniversityId(universityId);\n    setShowConfirmModal(true);\n  };\n  const handleConfirmDelete = async () => {\n    try {\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n      await axios.delete(`${process.env.REACT_APP_API_URL}/api/universities/${selectedUniversityId}`, config);\n      setUniversities(universities.filter(u => u.universityId !== selectedUniversityId));\n      setShowConfirmModal(false);\n      setSelectedUniversityId(null);\n      setSuccess('University deleted successfully!');\n      setTimeout(() => setSuccess(''), 3000);\n    } catch (err) {\n      var _err$response6, _err$response6$data;\n      setError(((_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.message) || 'Failed to delete university');\n      setShowConfirmModal(false);\n    }\n  };\n  const handleInputChange = (e, field, subField, lang) => {\n    const value = e.target.value;\n    setFormData(prev => {\n      // Handle address fields with language parameters (address.street.en, address.city.ar, etc.)\n      if (field === 'address' && subField && lang) {\n        return {\n          ...prev,\n          address: {\n            ...prev.address,\n            [subField]: {\n              ...prev.address[subField],\n              [lang]: value\n            }\n          }\n        };\n      }\n      // Handle regular bilingual fields (name.en, description.ar, etc.)\n      else if (lang) {\n        return {\n          ...prev,\n          [field]: {\n            ...prev[field],\n            [lang]: value\n          }\n        };\n      }\n      // Handle object fields with subfields (contactInfo.phone, contactInfo.email, etc.)\n      else if (subField) {\n        return {\n          ...prev,\n          [field]: {\n            ...prev[field],\n            [subField]: value\n          }\n        };\n      }\n      // Handle simple fields (universityId, slotBeginDate, etc.)\n      return {\n        ...prev,\n        [field]: value\n      };\n    });\n  };\n  const handleServiceChange = (index, lang, value) => {\n    setFormData(prev => {\n      const newServices = [...prev.dentistryServices];\n      newServices[index] = {\n        ...newServices[index],\n        [lang]: value\n      };\n      return {\n        ...prev,\n        dentistryServices: newServices\n      };\n    });\n  };\n  const addService = () => {\n    setFormData(prev => ({\n      ...prev,\n      dentistryServices: [...prev.dentistryServices, {\n        en: '',\n        ar: ''\n      }]\n    }));\n  };\n  const removeService = index => {\n    setFormData(prev => ({\n      ...prev,\n      dentistryServices: prev.dentistryServices.filter((_, i) => i !== index)\n    }));\n  };\n  const handleSlotDurationChange = e => {\n    setFormData(prev => ({\n      ...prev,\n      slotDuration: parseInt(e.target.value)\n    }));\n  };\n  const handleTimeSlotChange = (index, value) => {\n    setFormData(prev => {\n      const newSlots = [...prev.availableSlots];\n      newSlots[index] = value;\n      return {\n        ...prev,\n        availableSlots: newSlots\n      };\n    });\n  };\n  const addTimeSlot = () => {\n    setFormData(prev => ({\n      ...prev,\n      availableSlots: [...prev.availableSlots, '09:00']\n    }));\n  };\n  const removeTimeSlot = index => {\n    setFormData(prev => ({\n      ...prev,\n      availableSlots: prev.availableSlots.filter((_, i) => i !== index)\n    }));\n  };\n  const handleHolidayChange = day => {\n    setFormData(prev => {\n      const currentHolidays = [...prev.holidays];\n      if (currentHolidays.includes(day)) {\n        // Remove the day if it's already in the holidays array\n        return {\n          ...prev,\n          holidays: currentHolidays.filter(holiday => holiday !== day)\n        };\n      } else {\n        // Add the day if it's not in the holidays array\n        return {\n          ...prev,\n          holidays: [...currentHolidays, day]\n        };\n      }\n    });\n  };\n  const handleUniversitySubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n\n    // Validate required fields\n    if (!formData.universityId.trim()) {\n      setError('University ID is required');\n      return;\n    }\n    if (!formData.name.en.trim() || !formData.name.ar.trim()) {\n      setError('University name is required in both English and Arabic');\n      return;\n    }\n\n    // Validate address fields\n    if (!formData.address.street.en.trim() || !formData.address.street.ar.trim()) {\n      setError('Street address is required in both English and Arabic');\n      return;\n    }\n    if (!formData.address.city.en.trim() || !formData.address.city.ar.trim()) {\n      setError('City is required in both English and Arabic');\n      return;\n    }\n    if (!formData.address.country.en.trim() || !formData.address.country.ar.trim()) {\n      setError('Country is required in both English and Arabic');\n      return;\n    }\n    if (!formData.contactInfo.phone.trim() || !formData.contactInfo.email.trim()) {\n      setError('Contact information (phone and email) is required');\n      return;\n    }\n    if (!formData.slotBeginDate || !formData.slotEndDate) {\n      setError('Slot begin and end dates are required');\n      return;\n    }\n    if (!formData.slotDuration || formData.slotDuration < 30) {\n      setError('Valid slot duration is required (minimum 30 minutes)');\n      return;\n    }\n    if (!formData.availableSlots || formData.availableSlots.length === 0) {\n      setError('At least one available time slot is required');\n      return;\n    }\n\n    // Prepare form data with default values for required fields\n    const formToSubmit = {\n      ...formData\n    };\n\n    // Set default values for empty bilingual fields\n    ['description', 'dentistryInfo', 'facilities', 'program'].forEach(field => {\n      if (!formToSubmit[field].en.trim() || !formToSubmit[field].ar.trim()) {\n        formToSubmit[field] = {\n          en: formToSubmit[field].en.trim() || 'Not provided',\n          ar: formToSubmit[field].ar.trim() || 'غير متوفر'\n        };\n      }\n    });\n\n    // Ensure address fields have values\n    ['street', 'city', 'country'].forEach(field => {\n      if (!formToSubmit.address[field].en.trim() || !formToSubmit.address[field].ar.trim()) {\n        formToSubmit.address[field] = {\n          en: formToSubmit.address[field].en.trim() || 'Not provided',\n          ar: formToSubmit.address[field].ar.trim() || 'غير متوفر'\n        };\n      }\n    });\n\n    // Ensure dentistry services have values\n    if (formToSubmit.dentistryServices.length === 0) {\n      formToSubmit.dentistryServices = [{\n        en: 'General Dentistry',\n        ar: 'طب الأسنان العام'\n      }];\n    } else {\n      formToSubmit.dentistryServices = formToSubmit.dentistryServices.map(service => ({\n        en: service.en.trim() || 'General Dentistry',\n        ar: service.ar.trim() || 'طب الأسنان العام'\n      }));\n    }\n    try {\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n      let response;\n      console.log('Submitting university data:', formToSubmit);\n      if (isEditing) {\n        response = await axios.put(`${process.env.REACT_APP_API_URL}/api/universities/${formToSubmit.universityId}`, formToSubmit, config);\n        setUniversities(universities.map(u => u.universityId === formToSubmit.universityId ? response.data : u));\n        setSuccess('University updated successfully!');\n      } else {\n        response = await axios.post(`${process.env.REACT_APP_API_URL}/api/universities`, formToSubmit, config);\n        setUniversities([...universities, response.data]);\n        setSuccess('University created successfully!');\n      }\n      setShowUniversityModal(false);\n      setTimeout(() => setSuccess(''), 3000);\n    } catch (err) {\n      var _err$response7, _err$response7$data, _err$response8, _err$response8$data;\n      console.error('University submission error:', err);\n\n      // Create a more detailed error message\n      let errorMessage = 'Failed to save university. ';\n      if ((_err$response7 = err.response) !== null && _err$response7 !== void 0 && (_err$response7$data = _err$response7.data) !== null && _err$response7$data !== void 0 && _err$response7$data.error) {\n        // If the backend sends a detailed error message\n        errorMessage += err.response.data.error;\n      } else if ((_err$response8 = err.response) !== null && _err$response8 !== void 0 && (_err$response8$data = _err$response8.data) !== null && _err$response8$data !== void 0 && _err$response8$data.message) {\n        // If the backend sends a simple message\n        errorMessage += err.response.data.message;\n      } else if (err.message) {\n        // If there's a general error message\n        errorMessage += err.message;\n      }\n      setError(errorMessage);\n    }\n  };\n  const container = {\n    hidden: {\n      opacity: 0\n    },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const item = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    show: {\n      opacity: 1,\n      y: 0\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 489,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(SuperAdminSidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 494,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 497,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: [error && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: -20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-5 w-5 text-red-500 mr-3\",\n                viewBox: \"0 0 20 20\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-700 font-medium\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 15\n          }, this), success && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: -20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"mb-6 p-4 bg-green-50 border-l-4 border-green-500 rounded-lg shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-5 w-5 text-green-500 mr-3\",\n                viewBox: \"0 0 20 20\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-green-700 font-medium\",\n                children: success\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\",\n                  children: \"Universities\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-[#333333]\",\n                  children: \"Manage university information and settings\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: handleAddUniversity,\n                className: \"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaPlus, {\n                  className: \"h-5 w-5 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 19\n                }, this), \"Add University\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: container,\n              initial: \"hidden\",\n              whileInView: \"show\",\n              viewport: {\n                once: true\n              },\n              className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"overflow-x-auto\",\n                  children: /*#__PURE__*/_jsxDEV(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                      className: \"bg-gray-50\",\n                      children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"ID\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 563,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Name (EN)\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 564,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Name (AR)\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 565,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Phone\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 566,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Email\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 567,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Actions\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 568,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 562,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 561,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                      className: \"bg-white divide-y divide-gray-200\",\n                      children: universities.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: /*#__PURE__*/_jsxDEV(\"td\", {\n                          colSpan: \"6\",\n                          className: \"px-6 py-8 text-center\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex flex-col items-center justify-center\",\n                            children: [/*#__PURE__*/_jsxDEV(FaUniversity, {\n                              className: \"h-12 w-12 text-gray-400 mb-4\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 576,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                              className: \"text-lg font-medium text-gray-900\",\n                              children: \"No universities found\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 577,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"mt-1 text-gray-500\",\n                              children: \"Add a university using the button above.\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 578,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 575,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 574,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 573,\n                        columnNumber: 27\n                      }, this) : universities.map(university => {\n                        var _university$name3, _university$name4, _university$contactIn4, _university$contactIn5;\n                        return /*#__PURE__*/_jsxDEV(motion.tr, {\n                          variants: item,\n                          onClick: () => handleRowClick(university.universityId),\n                          className: \"hover:bg-gray-50 cursor-pointer\",\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: university.universityId || 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 590,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: ((_university$name3 = university.name) === null || _university$name3 === void 0 ? void 0 : _university$name3.en) || 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 591,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: ((_university$name4 = university.name) === null || _university$name4 === void 0 ? void 0 : _university$name4.ar) || 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 592,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: ((_university$contactIn4 = university.contactInfo) === null || _university$contactIn4 === void 0 ? void 0 : _university$contactIn4.phone) || 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 593,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: ((_university$contactIn5 = university.contactInfo) === null || _university$contactIn5 === void 0 ? void 0 : _university$contactIn5.email) || 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 594,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500 flex space-x-2\",\n                            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: e => handleEditClick(e, university),\n                              className: \"text-[#0077B6] hover:text-[#20B2AA]\",\n                              title: \"Edit\",\n                              children: /*#__PURE__*/_jsxDEV(FaEdit, {\n                                className: \"h-5 w-5\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 601,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 596,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: e => handleDeleteClick(e, university.universityId),\n                              className: \"text-red-600 hover:text-red-800\",\n                              title: \"Delete\",\n                              children: /*#__PURE__*/_jsxDEV(FaTrash, {\n                                className: \"h-5 w-5\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 608,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 603,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 595,\n                            columnNumber: 31\n                          }, this)]\n                        }, university.universityId, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 584,\n                          columnNumber: 29\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 571,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 560,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 496,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmModal, {\n      isOpen: showConfirmModal,\n      onClose: () => setShowConfirmModal(false),\n      onConfirm: handleConfirmDelete,\n      title: \"Confirm Delete\",\n      message: \"Are you sure you want to delete this university? This action cannot be undone.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 624,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(UniversityDetailsModal, {\n      isOpen: showDetailsModal,\n      onClose: () => setShowDetailsModal(false),\n      university: selectedUniversity\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 632,\n      columnNumber: 7\n    }, this), showUniversityModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        className: \"bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-[#0077B6]\",\n              children: isEditing ? 'Edit University' : 'Add University'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowUniversityModal(false),\n              className: \"text-gray-400 hover:text-gray-500\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleUniversitySubmit,\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 sm:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"University ID*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 657,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: formData.universityId,\n                  onChange: e => handleInputChange(e, 'universityId'),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                  required: true,\n                  disabled: isEditing\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 656,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Name (English)*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: formData.name.en,\n                  onChange: e => handleInputChange(e, 'name', null, 'en'),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 669,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Name (Arabic)*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: formData.name.ar,\n                  onChange: e => handleInputChange(e, 'name', null, 'ar'),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 679,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Phone*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 688,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"tel\",\n                  value: formData.contactInfo.phone,\n                  onChange: e => handleInputChange(e, 'contactInfo', 'phone'),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 689,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Email*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 698,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  value: formData.contactInfo.email,\n                  onChange: e => handleInputChange(e, 'contactInfo', 'email'),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 699,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Website\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 708,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"url\",\n                  value: formData.contactInfo.website,\n                  onChange: e => handleInputChange(e, 'contactInfo', 'website'),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 709,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 707,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 655,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 sm:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Street (English)*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 719,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: formData.address.street.en,\n                  onChange: e => handleInputChange(e, 'address', 'street', 'en'),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 720,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Street (Arabic)*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: formData.address.street.ar,\n                  onChange: e => handleInputChange(e, 'address', 'street', 'ar'),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 730,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"City (English)*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 739,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: formData.address.city.en,\n                  onChange: e => handleInputChange(e, 'address', 'city', 'en'),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 740,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"City (Arabic)*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 749,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: formData.address.city.ar,\n                  onChange: e => handleInputChange(e, 'address', 'city', 'ar'),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 750,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 748,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Country (English)*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 759,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: formData.address.country.en,\n                  onChange: e => handleInputChange(e, 'address', 'country', 'en'),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 760,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Country (Arabic)*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 769,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: formData.address.country.ar,\n                  onChange: e => handleInputChange(e, 'address', 'country', 'ar'),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 770,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Postal Code\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 779,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: formData.address.postalCode,\n                  onChange: e => handleInputChange(e, 'address', 'postalCode'),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 780,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 sm:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Slot Begin Date*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 790,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: formData.slotBeginDate,\n                  onChange: e => handleInputChange(e, 'slotBeginDate'),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 791,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Slot End Date*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 800,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: formData.slotEndDate,\n                  onChange: e => handleInputChange(e, 'slotEndDate'),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 801,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 799,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 788,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Slot Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 812,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2 p-4 border border-gray-200 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-xs font-medium text-gray-700 mb-1\",\n                    children: \"Slot Duration (minutes)*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 815,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    value: formData.slotDuration,\n                    onChange: handleSlotDurationChange,\n                    min: \"30\",\n                    step: \"30\",\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 816,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 814,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-xs font-medium text-gray-700 mb-1\",\n                    children: \"Available Time Slots*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 827,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-2\",\n                    children: [formData.availableSlots.map((slot, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"time\",\n                        value: slot,\n                        onChange: e => handleTimeSlotChange(index, e.target.value),\n                        className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                        required: true\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 831,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        type: \"button\",\n                        onClick: () => removeTimeSlot(index),\n                        className: \"text-red-500 hover:text-red-700\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          className: \"h-5 w-5\",\n                          viewBox: \"0 0 20 20\",\n                          fill: \"currentColor\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z\",\n                            clipRule: \"evenodd\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 844,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 843,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 838,\n                        columnNumber: 29\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 830,\n                      columnNumber: 27\n                    }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: addTimeSlot,\n                      className: \"px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors w-full\",\n                      children: \"+ Add Time Slot\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 849,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 828,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 811,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Holidays\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 862,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 sm:grid-cols-4 gap-3 mt-2 p-4 border border-gray-200 rounded-lg\",\n                children: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map(day => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    id: `holiday-${day}`,\n                    checked: formData.holidays.includes(day),\n                    onChange: () => handleHolidayChange(day),\n                    className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 866,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: `holiday-${day}`,\n                    className: \"ml-2 block text-sm text-gray-700\",\n                    children: day\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 873,\n                    columnNumber: 25\n                  }, this)]\n                }, day, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 865,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 863,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-gray-500\",\n                children: \"Select days when the university is closed.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 879,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 861,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Description (English)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 882,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: formData.description.en,\n                onChange: e => handleInputChange(e, 'description', null, 'en'),\n                rows: \"3\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 883,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 881,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Description (Arabic)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 891,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: formData.description.ar,\n                onChange: e => handleInputChange(e, 'description', null, 'ar'),\n                rows: \"3\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 892,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 890,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Dentistry Info (English)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 900,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: formData.dentistryInfo.en,\n                onChange: e => handleInputChange(e, 'dentistryInfo', null, 'en'),\n                rows: \"3\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 901,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 899,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Dentistry Info (Arabic)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 909,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: formData.dentistryInfo.ar,\n                onChange: e => handleInputChange(e, 'dentistryInfo', null, 'ar'),\n                rows: \"3\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 910,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 908,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Facilities (English)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 918,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: formData.facilities.en,\n                onChange: e => handleInputChange(e, 'facilities', null, 'en'),\n                rows: \"3\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 919,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 917,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Facilities (Arabic)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 927,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: formData.facilities.ar,\n                onChange: e => handleInputChange(e, 'facilities', null, 'ar'),\n                rows: \"3\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 928,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 926,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Program (English)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 936,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: formData.program.en,\n                onChange: e => handleInputChange(e, 'program', null, 'en'),\n                rows: \"3\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 937,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 935,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Program (Arabic)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 945,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: formData.program.ar,\n                onChange: e => handleInputChange(e, 'program', null, 'ar'),\n                rows: \"3\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 946,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 944,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-700 mb-2\",\n                children: \"Dentistry Services\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 954,\n                columnNumber: 19\n              }, this), formData.dentistryServices.map((service, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-4 p-4 border border-gray-200 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-2\",\n                      children: \"Service (English)*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 959,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: service.en,\n                      onChange: e => handleServiceChange(index, 'en', e.target.value),\n                      className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                      required: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 960,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 958,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-2\",\n                      children: \"Service (Arabic)*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 969,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: service.ar,\n                      onChange: e => handleServiceChange(index, 'ar', e.target.value),\n                      className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                      required: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 970,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 968,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 957,\n                  columnNumber: 23\n                }, this), formData.dentistryServices.length > 1 && /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => removeService(index),\n                  className: \"mt-2 text-red-600 hover:text-red-800 text-sm font-medium\",\n                  children: \"Remove Service\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 980,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 956,\n                columnNumber: 21\n              }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: addService,\n                className: \"mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium\",\n                children: \"Add Another Service\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 990,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 953,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 sm:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Logo URL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1000,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"url\",\n                  value: formData.logo,\n                  onChange: e => handleInputChange(e, 'logo'),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1001,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 999,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Image URL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1009,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"url\",\n                  value: formData.image,\n                  onChange: e => handleInputChange(e, 'image'),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1010,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1008,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Map URL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1018,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"url\",\n                  value: formData.mapUrl,\n                  onChange: e => handleInputChange(e, 'mapUrl'),\n                  className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1019,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1017,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 998,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end space-x-4 pt-4\",\n              children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                type: \"button\",\n                onClick: () => setShowUniversityModal(false),\n                className: \"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\",\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1028,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                type: \"submit\",\n                className: \"px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-lg hover:from-blue-600 hover:to-blue-800 font-medium transition-colors shadow-md hover:shadow-lg\",\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: isEditing ? 'Update University' : 'Add University'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1037,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1027,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 654,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 645,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 640,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 639,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 493,\n    columnNumber: 5\n  }, this);\n};\n_s(Universities, \"My4EFb7UYWa6eBL6q4JPXq7YSM8=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Universities;\nexport default Universities;\nvar _c;\n$RefreshReg$(_c, \"Universities\");", "map": {"version": 3, "names": ["useState", "useEffect", "useNavigate", "axios", "motion", "FaUniversity", "FaEdit", "FaTrash", "FaPlus", "<PERSON><PERSON><PERSON>", "Loader", "ConfirmModal", "UniversityDetailsModal", "useAuth", "SuperAdminSidebar", "jsxDEV", "_jsxDEV", "Universities", "_s", "sidebarOpen", "setSidebarOpen", "universities", "setUniversities", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showConfirmModal", "setShowConfirmModal", "showDetailsModal", "setShowDetailsModal", "showUniversityModal", "setShowUniversityModal", "selectedUniversityId", "setSelectedUniversityId", "selectedUniversity", "setSelectedUniversity", "isEditing", "setIsEditing", "formData", "setFormData", "universityId", "name", "en", "ar", "description", "dentistryInfo", "facilities", "program", "dentistryServices", "address", "street", "city", "country", "postalCode", "contactInfo", "phone", "email", "website", "slotBeginDate", "slotEndDate", "slotDuration", "availableSlots", "holidays", "logo", "image", "mapUrl", "navigate", "user", "token", "fetchUniversities", "config", "headers", "Authorization", "response", "get", "process", "env", "REACT_APP_API_URL", "console", "log", "data", "err", "_err$response", "_err$response2", "_err$response3", "_err$response3$data", "_err$response4", "message", "errorMessage", "status", "handleRowClick", "_err$response5", "_err$response5$data", "handleEditClick", "e", "university", "_university$name", "_university$name2", "_university$descripti", "_university$descripti2", "_university$dentistry", "_university$dentistry2", "_university$facilitie", "_university$facilitie2", "_university$program", "_university$program2", "_university$address", "_university$address$s", "_university$address2", "_university$address2$", "_university$address3", "_university$address3$", "_university$address4", "_university$address4$", "_university$address5", "_university$address5$", "_university$address6", "_university$address6$", "_university$address7", "_university$contactIn", "_university$contactIn2", "_university$contactIn3", "stopPropagation", "Array", "isArray", "timeSlots", "length", "duration", "Set", "map", "slot", "time", "service", "Date", "toISOString", "split", "handleAddUniversity", "handleDeleteClick", "handleConfirmDelete", "delete", "filter", "u", "setTimeout", "_err$response6", "_err$response6$data", "handleInputChange", "field", "subField", "lang", "value", "target", "prev", "handleServiceChange", "index", "newServices", "addService", "removeService", "_", "i", "handleSlotDurationChange", "parseInt", "handleTimeSlotChange", "newSlots", "addTimeSlot", "removeTimeSlot", "handleHolidayChange", "day", "currentHolidays", "includes", "holiday", "handleUniversitySubmit", "preventDefault", "trim", "formToSubmit", "for<PERSON>ach", "put", "post", "_err$response7", "_err$response7$data", "_err$response8", "_err$response8$data", "container", "hidden", "opacity", "show", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "item", "y", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "isOpen", "setIsOpen", "toggleSidebar", "div", "initial", "animate", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "button", "whileHover", "scale", "whileTap", "onClick", "variants", "whileInView", "viewport", "once", "colSpan", "_university$name3", "_university$name4", "_university$contactIn4", "_university$contactIn5", "tr", "title", "onClose", "onConfirm", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "onSubmit", "type", "onChange", "required", "disabled", "min", "step", "id", "checked", "htmlFor", "rows", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/superadmin/Universities.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { motion } from 'framer-motion';\nimport { FaUniversity, FaEdit, FaTrash, FaPlus } from 'react-icons/fa';\nimport Navbar from '../student/Navbar';\nimport Loader from '../components/Loader';\nimport ConfirmModal from '../components/ConfirmModal';\nimport UniversityDetailsModal from '../components/UniversityDetailsModal';\nimport { useAuth } from '../context/AuthContext';\nimport SuperAdminSidebar from './SuperAdminSidebar';\n\nconst Universities = () => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [universities, setUniversities] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\n  const [showDetailsModal, setShowDetailsModal] = useState(false);\n  const [showUniversityModal, setShowUniversityModal] = useState(false);\n  const [selectedUniversityId, setSelectedUniversityId] = useState(null);\n  const [selectedUniversity, setSelectedUniversity] = useState(null);\n  const [isEditing, setIsEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    universityId: '',\n    name: { en: '', ar: '' },\n    description: { en: '', ar: '' },\n    dentistryInfo: { en: '', ar: '' },\n    facilities: { en: '', ar: '' },\n    program: { en: '', ar: '' },\n    dentistryServices: [{ en: '', ar: '' }],\n    address: {\n      street: { en: '', ar: '' },\n      city: { en: '', ar: '' },\n      country: { en: '', ar: '' },\n      postalCode: '',\n    },\n    contactInfo: {\n      phone: '',\n      email: '',\n      website: '',\n    },\n    slotBeginDate: '',\n    slotEndDate: '',\n    slotDuration: 120, // Default slot duration in minutes\n    availableSlots: ['09:00', '11:30', '14:00'], // Default available time slots\n    holidays: ['Friday', 'Sunday'], // Default holidays\n    logo: '',\n    image: '',\n    mapUrl: '',\n  });\n  const navigate = useNavigate();\n  const { user, token } = useAuth();\n\n  useEffect(() => {\n    const fetchUniversities = async () => {\n      if (!user || !token) {\n        setError('Please log in to view universities.');\n        setLoading(false);\n        return;\n      }\n\n      try {\n        const config = { headers: { Authorization: `Bearer ${token}` } };\n        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/universities`, config);\n        console.log('Universities response:', response.data); // Debug API response\n        setUniversities(response.data || []);\n      } catch (err) {\n        console.error('Fetch error:', err.response?.data || err.message);\n        const errorMessage = err.response?.status === 401\n          ? 'Unauthorized. Please log in again.'\n          : err.response?.data?.message || 'Failed to load universities';\n        setError(errorMessage);\n        if (err.response?.status === 401) {\n          navigate('/login');\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchUniversities();\n  }, [user, token, navigate]);\n\n  const handleRowClick = async (universityId) => {\n    try {\n      const config = { headers: { Authorization: `Bearer ${token}` } };\n      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/universities/${universityId}`, config);\n      setSelectedUniversity(response.data);\n      setShowDetailsModal(true);\n    } catch (err) {\n      setError(err.response?.data?.message || 'Failed to load university details');\n    }\n  };\n\n  const handleEditClick = (e, university) => {\n    e.stopPropagation();\n    console.log('Editing university:', university); // Debug university object\n\n    // Determine slot duration from university data\n    let slotDuration = 120; // Default value\n    if (university.slotDuration) {\n      // If slotDuration is directly available\n      slotDuration = university.slotDuration;\n    } else if (Array.isArray(university.timeSlots) && university.timeSlots.length > 0 && university.timeSlots[0].duration) {\n      // Extract from the first time slot if available\n      slotDuration = university.timeSlots[0].duration;\n    }\n\n    // Extract available time slots\n    let availableSlots = ['09:00', '11:30', '14:00']; // Default values\n    if (Array.isArray(university.timeSlots) && university.timeSlots.length > 0) {\n      // Get unique time slots\n      availableSlots = [...new Set(university.timeSlots.map(slot => slot.time))];\n    }\n\n    setFormData({\n      universityId: university.universityId || '',\n      name: {\n        en: university.name?.en || '',\n        ar: university.name?.ar || '',\n      },\n      description: {\n        en: university.description?.en || '',\n        ar: university.description?.ar || '',\n      },\n      dentistryInfo: {\n        en: university.dentistryInfo?.en || '',\n        ar: university.dentistryInfo?.ar || '',\n      },\n      facilities: {\n        en: university.facilities?.en || '',\n        ar: university.facilities?.ar || '',\n      },\n      program: {\n        en: university.program?.en || '',\n        ar: university.program?.ar || '',\n      },\n      dentistryServices: Array.isArray(university.dentistryServices) && university.dentistryServices.length > 0\n        ? university.dentistryServices.map(service => ({\n            en: service?.en || '',\n            ar: service?.ar || '',\n          }))\n        : [{ en: '', ar: '' }],\n      address: {\n        street: {\n          en: university.address?.street?.en || '',\n          ar: university.address?.street?.ar || '',\n        },\n        city: {\n          en: university.address?.city?.en || '',\n          ar: university.address?.city?.ar || '',\n        },\n        country: {\n          en: university.address?.country?.en || '',\n          ar: university.address?.country?.ar || '',\n        },\n        postalCode: university.address?.postalCode || '',\n      },\n      contactInfo: {\n        phone: university.contactInfo?.phone || '',\n        email: university.contactInfo?.email || '',\n        website: university.contactInfo?.website || '',\n      },\n      slotBeginDate: university.slotBeginDate\n        ? new Date(university.slotBeginDate).toISOString().split('T')[0]\n        : '',\n      slotEndDate: university.slotEndDate\n        ? new Date(university.slotEndDate).toISOString().split('T')[0]\n        : '',\n      slotDuration: slotDuration,\n      availableSlots: availableSlots,\n      holidays: Array.isArray(university.holidays) ? university.holidays : ['Friday', 'Sunday'],\n      logo: university.logo || '',\n      image: university.image || '',\n      mapUrl: university.mapUrl || '',\n    });\n\n    setIsEditing(true);\n    setShowUniversityModal(true);\n  };\n\n  const handleAddUniversity = () => {\n    setFormData({\n      universityId: '',\n      name: { en: '', ar: '' },\n      description: { en: '', ar: '' },\n      dentistryInfo: { en: '', ar: '' },\n      facilities: { en: '', ar: '' },\n      program: { en: '', ar: '' },\n      dentistryServices: [{ en: '', ar: '' }],\n      address: {\n        street: { en: '', ar: '' },\n        city: { en: '', ar: '' },\n        country: { en: '', ar: '' },\n        postalCode: '',\n      },\n      contactInfo: {\n        phone: '',\n        email: '',\n        website: '',\n      },\n      slotBeginDate: '',\n      slotEndDate: '',\n      slotDuration: 120, // Default slot duration in minutes\n      availableSlots: ['09:00', '11:30', '14:00'], // Default available time slots\n      holidays: ['Friday', 'Sunday'], // Default holidays\n      logo: '',\n      image: '',\n      mapUrl: '',\n    });\n    setIsEditing(false);\n    setShowUniversityModal(true);\n  };\n\n  const handleDeleteClick = (e, universityId) => {\n    e.stopPropagation();\n    setSelectedUniversityId(universityId);\n    setShowConfirmModal(true);\n  };\n\n  const handleConfirmDelete = async () => {\n    try {\n      const config = { headers: { Authorization: `Bearer ${token}` } };\n      await axios.delete(`${process.env.REACT_APP_API_URL}/api/universities/${selectedUniversityId}`, config);\n      setUniversities(universities.filter((u) => u.universityId !== selectedUniversityId));\n      setShowConfirmModal(false);\n      setSelectedUniversityId(null);\n      setSuccess('University deleted successfully!');\n      setTimeout(() => setSuccess(''), 3000);\n    } catch (err) {\n      setError(err.response?.data?.message || 'Failed to delete university');\n      setShowConfirmModal(false);\n    }\n  };\n\n  const handleInputChange = (e, field, subField, lang) => {\n    const value = e.target.value;\n    setFormData((prev) => {\n      // Handle address fields with language parameters (address.street.en, address.city.ar, etc.)\n      if (field === 'address' && subField && lang) {\n        return {\n          ...prev,\n          address: {\n            ...prev.address,\n            [subField]: {\n              ...prev.address[subField],\n              [lang]: value\n            }\n          }\n        };\n      }\n      // Handle regular bilingual fields (name.en, description.ar, etc.)\n      else if (lang) {\n        return {\n          ...prev,\n          [field]: {\n            ...prev[field],\n            [lang]: value,\n          },\n        };\n      }\n      // Handle object fields with subfields (contactInfo.phone, contactInfo.email, etc.)\n      else if (subField) {\n        return {\n          ...prev,\n          [field]: {\n            ...prev[field],\n            [subField]: value,\n          },\n        };\n      }\n      // Handle simple fields (universityId, slotBeginDate, etc.)\n      return {\n        ...prev,\n        [field]: value,\n      };\n    });\n  };\n\n  const handleServiceChange = (index, lang, value) => {\n    setFormData((prev) => {\n      const newServices = [...prev.dentistryServices];\n      newServices[index] = { ...newServices[index], [lang]: value };\n      return { ...prev, dentistryServices: newServices };\n    });\n  };\n\n  const addService = () => {\n    setFormData((prev) => ({\n      ...prev,\n      dentistryServices: [...prev.dentistryServices, { en: '', ar: '' }],\n    }));\n  };\n\n  const removeService = (index) => {\n    setFormData((prev) => ({\n      ...prev,\n      dentistryServices: prev.dentistryServices.filter((_, i) => i !== index),\n    }));\n  };\n\n  const handleSlotDurationChange = (e) => {\n    setFormData((prev) => ({\n      ...prev,\n      slotDuration: parseInt(e.target.value),\n    }));\n  };\n\n  const handleTimeSlotChange = (index, value) => {\n    setFormData((prev) => {\n      const newSlots = [...prev.availableSlots];\n      newSlots[index] = value;\n      return { ...prev, availableSlots: newSlots };\n    });\n  };\n\n  const addTimeSlot = () => {\n    setFormData((prev) => ({\n      ...prev,\n      availableSlots: [...prev.availableSlots, '09:00'],\n    }));\n  };\n\n  const removeTimeSlot = (index) => {\n    setFormData((prev) => ({\n      ...prev,\n      availableSlots: prev.availableSlots.filter((_, i) => i !== index),\n    }));\n  };\n\n  const handleHolidayChange = (day) => {\n    setFormData((prev) => {\n      const currentHolidays = [...prev.holidays];\n      if (currentHolidays.includes(day)) {\n        // Remove the day if it's already in the holidays array\n        return {\n          ...prev,\n          holidays: currentHolidays.filter(holiday => holiday !== day)\n        };\n      } else {\n        // Add the day if it's not in the holidays array\n        return {\n          ...prev,\n          holidays: [...currentHolidays, day]\n        };\n      }\n    });\n  };\n\n  const handleUniversitySubmit = async (e) => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n\n    // Validate required fields\n    if (!formData.universityId.trim()) {\n      setError('University ID is required');\n      return;\n    }\n\n    if (!formData.name.en.trim() || !formData.name.ar.trim()) {\n      setError('University name is required in both English and Arabic');\n      return;\n    }\n\n    // Validate address fields\n    if (!formData.address.street.en.trim() || !formData.address.street.ar.trim()) {\n      setError('Street address is required in both English and Arabic');\n      return;\n    }\n\n    if (!formData.address.city.en.trim() || !formData.address.city.ar.trim()) {\n      setError('City is required in both English and Arabic');\n      return;\n    }\n\n    if (!formData.address.country.en.trim() || !formData.address.country.ar.trim()) {\n      setError('Country is required in both English and Arabic');\n      return;\n    }\n\n    if (!formData.contactInfo.phone.trim() || !formData.contactInfo.email.trim()) {\n      setError('Contact information (phone and email) is required');\n      return;\n    }\n\n    if (!formData.slotBeginDate || !formData.slotEndDate) {\n      setError('Slot begin and end dates are required');\n      return;\n    }\n\n    if (!formData.slotDuration || formData.slotDuration < 30) {\n      setError('Valid slot duration is required (minimum 30 minutes)');\n      return;\n    }\n\n    if (!formData.availableSlots || formData.availableSlots.length === 0) {\n      setError('At least one available time slot is required');\n      return;\n    }\n\n    // Prepare form data with default values for required fields\n    const formToSubmit = { ...formData };\n\n    // Set default values for empty bilingual fields\n    ['description', 'dentistryInfo', 'facilities', 'program'].forEach(field => {\n      if (!formToSubmit[field].en.trim() || !formToSubmit[field].ar.trim()) {\n        formToSubmit[field] = {\n          en: formToSubmit[field].en.trim() || 'Not provided',\n          ar: formToSubmit[field].ar.trim() || 'غير متوفر'\n        };\n      }\n    });\n\n    // Ensure address fields have values\n    ['street', 'city', 'country'].forEach(field => {\n      if (!formToSubmit.address[field].en.trim() || !formToSubmit.address[field].ar.trim()) {\n        formToSubmit.address[field] = {\n          en: formToSubmit.address[field].en.trim() || 'Not provided',\n          ar: formToSubmit.address[field].ar.trim() || 'غير متوفر'\n        };\n      }\n    });\n\n    // Ensure dentistry services have values\n    if (formToSubmit.dentistryServices.length === 0) {\n      formToSubmit.dentistryServices = [{ en: 'General Dentistry', ar: 'طب الأسنان العام' }];\n    } else {\n      formToSubmit.dentistryServices = formToSubmit.dentistryServices.map(service => ({\n        en: service.en.trim() || 'General Dentistry',\n        ar: service.ar.trim() || 'طب الأسنان العام'\n      }));\n    }\n\n    try {\n      const config = { headers: { Authorization: `Bearer ${token}` } };\n      let response;\n\n      console.log('Submitting university data:', formToSubmit);\n\n      if (isEditing) {\n        response = await axios.put(\n          `${process.env.REACT_APP_API_URL}/api/universities/${formToSubmit.universityId}`,\n          formToSubmit,\n          config\n        );\n        setUniversities(universities.map(u => u.universityId === formToSubmit.universityId ? response.data : u));\n        setSuccess('University updated successfully!');\n      } else {\n        response = await axios.post(`${process.env.REACT_APP_API_URL}/api/universities`, formToSubmit, config);\n        setUniversities([...universities, response.data]);\n        setSuccess('University created successfully!');\n      }\n      setShowUniversityModal(false);\n      setTimeout(() => setSuccess(''), 3000);\n    } catch (err) {\n      console.error('University submission error:', err);\n\n      // Create a more detailed error message\n      let errorMessage = 'Failed to save university. ';\n\n      if (err.response?.data?.error) {\n        // If the backend sends a detailed error message\n        errorMessage += err.response.data.error;\n      } else if (err.response?.data?.message) {\n        // If the backend sends a simple message\n        errorMessage += err.response.data.message;\n      } else if (err.message) {\n        // If there's a general error message\n        errorMessage += err.message;\n      }\n\n      setError(errorMessage);\n    }\n  };\n\n  const container = {\n    hidden: { opacity: 0 },\n    show: { opacity: 1, transition: { staggerChildren: 0.1 } },\n  };\n\n  const item = {\n    hidden: { opacity: 0, y: 20 },\n    show: { opacity: 1, y: 0 },\n  };\n\n  if (loading) {\n    return <Loader />;\n  }\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\">\n      <SuperAdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\n\n        <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\">\n          <div className=\"max-w-7xl mx-auto\">\n            {error && (\n              <motion.div\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                className=\"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\"\n              >\n                <div className=\"flex items-center\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-red-500 mr-3\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <p className=\"text-red-700 font-medium\">{error}</p>\n                </div>\n              </motion.div>\n            )}\n            {success && (\n              <motion.div\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                className=\"mb-6 p-4 bg-green-50 border-l-4 border-green-500 rounded-lg shadow-sm\"\n              >\n                <div className=\"flex items-center\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-green-500 mr-3\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <p className=\"text-green-700 font-medium\">{success}</p>\n                </div>\n              </motion.div>\n            )}\n\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.5 }}\n            >\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\">\n                <div>\n                  <h1 className=\"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\">Universities</h1>\n                  <p className=\"text-[#333333]\">Manage university information and settings</p>\n                </div>\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={handleAddUniversity}\n                  className=\"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\"\n                >\n                  <FaPlus className=\"h-5 w-5 mr-2\" />\n                  Add University\n                </motion.button>\n              </div>\n\n              <motion.div\n                variants={container}\n                initial=\"hidden\"\n                whileInView=\"show\"\n                viewport={{ once: true }}\n                className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden\"\n              >\n                <div className=\"p-6\">\n                  <div className=\"overflow-x-auto\">\n                    <table className=\"min-w-full divide-y divide-gray-200\">\n                      <thead className=\"bg-gray-50\">\n                        <tr>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">ID</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Name (EN)</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Name (AR)</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Phone</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Email</th>\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n                        </tr>\n                      </thead>\n                      <tbody className=\"bg-white divide-y divide-gray-200\">\n                        {universities.length === 0 ? (\n                          <tr>\n                            <td colSpan=\"6\" className=\"px-6 py-8 text-center\">\n                              <div className=\"flex flex-col items-center justify-center\">\n                                <FaUniversity className=\"h-12 w-12 text-gray-400 mb-4\" />\n                                <h3 className=\"text-lg font-medium text-gray-900\">No universities found</h3>\n                                <p className=\"mt-1 text-gray-500\">Add a university using the button above.</p>\n                              </div>\n                            </td>\n                          </tr>\n                        ) : (\n                          universities.map((university) => (\n                            <motion.tr\n                              key={university.universityId}\n                              variants={item}\n                              onClick={() => handleRowClick(university.universityId)}\n                              className=\"hover:bg-gray-50 cursor-pointer\"\n                            >\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{university.universityId || 'N/A'}</td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{university.name?.en || 'N/A'}</td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{university.name?.ar || 'N/A'}</td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{university.contactInfo?.phone || 'N/A'}</td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{university.contactInfo?.email || 'N/A'}</td>\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 flex space-x-2\">\n                                <button\n                                  onClick={(e) => handleEditClick(e, university)}\n                                  className=\"text-[#0077B6] hover:text-[#20B2AA]\"\n                                  title=\"Edit\"\n                                >\n                                  <FaEdit className=\"h-5 w-5\" />\n                                </button>\n                                <button\n                                  onClick={(e) => handleDeleteClick(e, university.universityId)}\n                                  className=\"text-red-600 hover:text-red-800\"\n                                  title=\"Delete\"\n                                >\n                                  <FaTrash className=\"h-5 w-5\" />\n                                </button>\n                              </td>\n                            </motion.tr>\n                          ))\n                        )}\n                      </tbody>\n                    </table>\n                  </div>\n                </div>\n              </motion.div>\n            </motion.div>\n          </div>\n        </main>\n      </div>\n\n      <ConfirmModal\n        isOpen={showConfirmModal}\n        onClose={() => setShowConfirmModal(false)}\n        onConfirm={handleConfirmDelete}\n        title=\"Confirm Delete\"\n        message=\"Are you sure you want to delete this university? This action cannot be undone.\"\n      />\n\n      <UniversityDetailsModal\n        isOpen={showDetailsModal}\n        onClose={() => setShowDetailsModal(false)}\n        university={selectedUniversity}\n      />\n\n      {showUniversityModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <motion.div\n            initial={{ scale: 0.9, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            className=\"bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\"\n          >\n            <div className=\"p-6\">\n              <div className=\"flex justify-between items-center mb-6\">\n                <h2 className=\"text-2xl font-bold text-[#0077B6]\">{isEditing ? 'Edit University' : 'Add University'}</h2>\n                <button onClick={() => setShowUniversityModal(false)} className=\"text-gray-400 hover:text-gray-500\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n              <form onSubmit={handleUniversitySubmit} className=\"space-y-6\">\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">University ID*</label>\n                    <input\n                      type=\"text\"\n                      value={formData.universityId}\n                      onChange={(e) => handleInputChange(e, 'universityId')}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                      required\n                      disabled={isEditing}\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Name (English)*</label>\n                    <input\n                      type=\"text\"\n                      value={formData.name.en}\n                      onChange={(e) => handleInputChange(e, 'name', null, 'en')}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                      required\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Name (Arabic)*</label>\n                    <input\n                      type=\"text\"\n                      value={formData.name.ar}\n                      onChange={(e) => handleInputChange(e, 'name', null, 'ar')}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                      required\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Phone*</label>\n                    <input\n                      type=\"tel\"\n                      value={formData.contactInfo.phone}\n                      onChange={(e) => handleInputChange(e, 'contactInfo', 'phone')}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                      required\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Email*</label>\n                    <input\n                      type=\"email\"\n                      value={formData.contactInfo.email}\n                      onChange={(e) => handleInputChange(e, 'contactInfo', 'email')}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                      required\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Website</label>\n                    <input\n                      type=\"url\"\n                      value={formData.contactInfo.website}\n                      onChange={(e) => handleInputChange(e, 'contactInfo', 'website')}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                    />\n                  </div>\n                </div>\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Street (English)*</label>\n                    <input\n                      type=\"text\"\n                      value={formData.address.street.en}\n                      onChange={(e) => handleInputChange(e, 'address', 'street', 'en')}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                      required\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Street (Arabic)*</label>\n                    <input\n                      type=\"text\"\n                      value={formData.address.street.ar}\n                      onChange={(e) => handleInputChange(e, 'address', 'street', 'ar')}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                      required\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">City (English)*</label>\n                    <input\n                      type=\"text\"\n                      value={formData.address.city.en}\n                      onChange={(e) => handleInputChange(e, 'address', 'city', 'en')}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                      required\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">City (Arabic)*</label>\n                    <input\n                      type=\"text\"\n                      value={formData.address.city.ar}\n                      onChange={(e) => handleInputChange(e, 'address', 'city', 'ar')}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                      required\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Country (English)*</label>\n                    <input\n                      type=\"text\"\n                      value={formData.address.country.en}\n                      onChange={(e) => handleInputChange(e, 'address', 'country', 'en')}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                      required\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Country (Arabic)*</label>\n                    <input\n                      type=\"text\"\n                      value={formData.address.country.ar}\n                      onChange={(e) => handleInputChange(e, 'address', 'country', 'ar')}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                      required\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Postal Code</label>\n                    <input\n                      type=\"text\"\n                      value={formData.address.postalCode}\n                      onChange={(e) => handleInputChange(e, 'address', 'postalCode')}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    />\n                  </div>\n                </div>\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Slot Begin Date*</label>\n                    <input\n                      type=\"date\"\n                      value={formData.slotBeginDate}\n                      onChange={(e) => handleInputChange(e, 'slotBeginDate')}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                      required\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Slot End Date*</label>\n                    <input\n                      type=\"date\"\n                      value={formData.slotEndDate}\n                      onChange={(e) => handleInputChange(e, 'slotEndDate')}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                      required\n                    />\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Slot Settings</label>\n                  <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2 p-4 border border-gray-200 rounded-lg\">\n                    <div>\n                      <label className=\"block text-xs font-medium text-gray-700 mb-1\">Slot Duration (minutes)*</label>\n                      <input\n                        type=\"number\"\n                        value={formData.slotDuration}\n                        onChange={handleSlotDurationChange}\n                        min=\"30\"\n                        step=\"30\"\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        required\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-xs font-medium text-gray-700 mb-1\">Available Time Slots*</label>\n                      <div className=\"space-y-2\">\n                        {formData.availableSlots.map((slot, index) => (\n                          <div key={index} className=\"flex items-center space-x-2\">\n                            <input\n                              type=\"time\"\n                              value={slot}\n                              onChange={(e) => handleTimeSlotChange(index, e.target.value)}\n                              className=\"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                              required\n                            />\n                            <button\n                              type=\"button\"\n                              onClick={() => removeTimeSlot(index)}\n                              className=\"text-red-500 hover:text-red-700\"\n                            >\n                              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                                <path fillRule=\"evenodd\" d=\"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                        <button\n                          type=\"button\"\n                          onClick={addTimeSlot}\n                          className=\"px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors w-full\"\n                        >\n                          + Add Time Slot\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Holidays</label>\n                  <div className=\"grid grid-cols-2 sm:grid-cols-4 gap-3 mt-2 p-4 border border-gray-200 rounded-lg\">\n                    {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (\n                      <div key={day} className=\"flex items-center\">\n                        <input\n                          type=\"checkbox\"\n                          id={`holiday-${day}`}\n                          checked={formData.holidays.includes(day)}\n                          onChange={() => handleHolidayChange(day)}\n                          className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                        />\n                        <label htmlFor={`holiday-${day}`} className=\"ml-2 block text-sm text-gray-700\">\n                          {day}\n                        </label>\n                      </div>\n                    ))}\n                  </div>\n                  <p className=\"mt-1 text-sm text-gray-500\">Select days when the university is closed.</p>\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Description (English)</label>\n                  <textarea\n                    value={formData.description.en}\n                    onChange={(e) => handleInputChange(e, 'description', null, 'en')}\n                    rows=\"3\"\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Description (Arabic)</label>\n                  <textarea\n                    value={formData.description.ar}\n                    onChange={(e) => handleInputChange(e, 'description', null, 'ar')}\n                    rows=\"3\"\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Dentistry Info (English)</label>\n                  <textarea\n                    value={formData.dentistryInfo.en}\n                    onChange={(e) => handleInputChange(e, 'dentistryInfo', null, 'en')}\n                    rows=\"3\"\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Dentistry Info (Arabic)</label>\n                  <textarea\n                    value={formData.dentistryInfo.ar}\n                    onChange={(e) => handleInputChange(e, 'dentistryInfo', null, 'ar')}\n                    rows=\"3\"\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Facilities (English)</label>\n                  <textarea\n                    value={formData.facilities.en}\n                    onChange={(e) => handleInputChange(e, 'facilities', null, 'en')}\n                    rows=\"3\"\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Facilities (Arabic)</label>\n                  <textarea\n                    value={formData.facilities.ar}\n                    onChange={(e) => handleInputChange(e, 'facilities', null, 'ar')}\n                    rows=\"3\"\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Program (English)</label>\n                  <textarea\n                    value={formData.program.en}\n                    onChange={(e) => handleInputChange(e, 'program', null, 'en')}\n                    rows=\"3\"\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Program (Arabic)</label>\n                  <textarea\n                    value={formData.program.ar}\n                    onChange={(e) => handleInputChange(e, 'program', null, 'ar')}\n                    rows=\"3\"\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <h3 className=\"text-lg font-semibold text-gray-700 mb-2\">Dentistry Services</h3>\n                  {formData.dentistryServices.map((service, index) => (\n                    <div key={index} className=\"mb-4 p-4 border border-gray-200 rounded-md\">\n                      <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">Service (English)*</label>\n                          <input\n                            type=\"text\"\n                            value={service.en}\n                            onChange={(e) => handleServiceChange(index, 'en', e.target.value)}\n                            className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                            required\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">Service (Arabic)*</label>\n                          <input\n                            type=\"text\"\n                            value={service.ar}\n                            onChange={(e) => handleServiceChange(index, 'ar', e.target.value)}\n                            className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                            required\n                          />\n                        </div>\n                      </div>\n                      {formData.dentistryServices.length > 1 && (\n                        <button\n                          type=\"button\"\n                          onClick={() => removeService(index)}\n                          className=\"mt-2 text-red-600 hover:text-red-800 text-sm font-medium\"\n                        >\n                          Remove Service\n                        </button>\n                      )}\n                    </div>\n                  ))}\n                  <button\n                    type=\"button\"\n                    onClick={addService}\n                    className=\"mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium\"\n                  >\n                    Add Another Service\n                  </button>\n                </div>\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Logo URL</label>\n                    <input\n                      type=\"url\"\n                      value={formData.logo}\n                      onChange={(e) => handleInputChange(e, 'logo')}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Image URL</label>\n                    <input\n                      type=\"url\"\n                      value={formData.image}\n                      onChange={(e) => handleInputChange(e, 'image')}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Map URL</label>\n                    <input\n                      type=\"url\"\n                      value={formData.mapUrl}\n                      onChange={(e) => handleInputChange(e, 'mapUrl')}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    />\n                  </div>\n                </div>\n                <div className=\"flex justify-end space-x-4 pt-4\">\n                  <motion.button\n                    type=\"button\"\n                    onClick={() => setShowUniversityModal(false)}\n                    className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    Cancel\n                  </motion.button>\n                  <motion.button\n                    type=\"submit\"\n                    className=\"px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-lg hover:from-blue-600 hover:to-blue-800 font-medium transition-colors shadow-md hover:shadow-lg\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    {isEditing ? 'Update University' : 'Add University'}\n                  </motion.button>\n                </div>\n              </form>\n            </div>\n          </motion.div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Universities;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,YAAY,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,QAAQ,gBAAgB;AACtE,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,sBAAsB,MAAM,sCAAsC;AACzE,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,iBAAiB,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC6B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC+B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACiC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACmC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACqC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACyC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAC;IACvC2C,YAAY,EAAE,EAAE;IAChBC,IAAI,EAAE;MAAEC,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG,CAAC;IACxBC,WAAW,EAAE;MAAEF,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG,CAAC;IAC/BE,aAAa,EAAE;MAAEH,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG,CAAC;IACjCG,UAAU,EAAE;MAAEJ,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG,CAAC;IAC9BI,OAAO,EAAE;MAAEL,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG,CAAC;IAC3BK,iBAAiB,EAAE,CAAC;MAAEN,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAG,CAAC,CAAC;IACvCM,OAAO,EAAE;MACPC,MAAM,EAAE;QAAER,EAAE,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAG,CAAC;MAC1BQ,IAAI,EAAE;QAAET,EAAE,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAG,CAAC;MACxBS,OAAO,EAAE;QAAEV,EAAE,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAG,CAAC;MAC3BU,UAAU,EAAE;IACd,CAAC;IACDC,WAAW,EAAE;MACXC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE;IACX,CAAC;IACDC,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,GAAG;IAAE;IACnBC,cAAc,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;IAAE;IAC7CC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAAE;IAChCC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAGnE,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEoE,IAAI;IAAEC;EAAM,CAAC,GAAG1D,OAAO,CAAC,CAAC;EAEjCZ,SAAS,CAAC,MAAM;IACd,MAAMuE,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI,CAACF,IAAI,IAAI,CAACC,KAAK,EAAE;QACnB7C,QAAQ,CAAC,qCAAqC,CAAC;QAC/CF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,IAAI;QACF,MAAMiD,MAAM,GAAG;UAAEC,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAUJ,KAAK;UAAG;QAAE,CAAC;QAChE,MAAMK,QAAQ,GAAG,MAAMzE,KAAK,CAAC0E,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,mBAAmB,EAAEP,MAAM,CAAC;QAC7FQ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEN,QAAQ,CAACO,IAAI,CAAC,CAAC,CAAC;QACtD7D,eAAe,CAACsD,QAAQ,CAACO,IAAI,IAAI,EAAE,CAAC;MACtC,CAAC,CAAC,OAAOC,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA,EAAAC,cAAA;QACZR,OAAO,CAACxD,KAAK,CAAC,cAAc,EAAE,EAAA4D,aAAA,GAAAD,GAAG,CAACR,QAAQ,cAAAS,aAAA,uBAAZA,aAAA,CAAcF,IAAI,KAAIC,GAAG,CAACM,OAAO,CAAC;QAChE,MAAMC,YAAY,GAAG,EAAAL,cAAA,GAAAF,GAAG,CAACR,QAAQ,cAAAU,cAAA,uBAAZA,cAAA,CAAcM,MAAM,MAAK,GAAG,GAC7C,oCAAoC,GACpC,EAAAL,cAAA,GAAAH,GAAG,CAACR,QAAQ,cAAAW,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcJ,IAAI,cAAAK,mBAAA,uBAAlBA,mBAAA,CAAoBE,OAAO,KAAI,6BAA6B;QAChEhE,QAAQ,CAACiE,YAAY,CAAC;QACtB,IAAI,EAAAF,cAAA,GAAAL,GAAG,CAACR,QAAQ,cAAAa,cAAA,uBAAZA,cAAA,CAAcG,MAAM,MAAK,GAAG,EAAE;UAChCvB,QAAQ,CAAC,QAAQ,CAAC;QACpB;MACF,CAAC,SAAS;QACR7C,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDgD,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACF,IAAI,EAAEC,KAAK,EAAEF,QAAQ,CAAC,CAAC;EAE3B,MAAMwB,cAAc,GAAG,MAAOlD,YAAY,IAAK;IAC7C,IAAI;MACF,MAAM8B,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUJ,KAAK;QAAG;MAAE,CAAC;MAChE,MAAMK,QAAQ,GAAG,MAAMzE,KAAK,CAAC0E,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,qBAAqBrC,YAAY,EAAE,EAAE8B,MAAM,CAAC;MAC7GnC,qBAAqB,CAACsC,QAAQ,CAACO,IAAI,CAAC;MACpCnD,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAOoD,GAAG,EAAE;MAAA,IAAAU,cAAA,EAAAC,mBAAA;MACZrE,QAAQ,CAAC,EAAAoE,cAAA,GAAAV,GAAG,CAACR,QAAQ,cAAAkB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcX,IAAI,cAAAY,mBAAA,uBAAlBA,mBAAA,CAAoBL,OAAO,KAAI,mCAAmC,CAAC;IAC9E;EACF,CAAC;EAED,MAAMM,eAAe,GAAGA,CAACC,CAAC,EAAEC,UAAU,KAAK;IAAA,IAAAC,gBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACzC3B,CAAC,CAAC4B,eAAe,CAAC,CAAC;IACnB5C,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEgB,UAAU,CAAC,CAAC,CAAC;;IAEhD;IACA,IAAInC,YAAY,GAAG,GAAG,CAAC,CAAC;IACxB,IAAImC,UAAU,CAACnC,YAAY,EAAE;MAC3B;MACAA,YAAY,GAAGmC,UAAU,CAACnC,YAAY;IACxC,CAAC,MAAM,IAAI+D,KAAK,CAACC,OAAO,CAAC7B,UAAU,CAAC8B,SAAS,CAAC,IAAI9B,UAAU,CAAC8B,SAAS,CAACC,MAAM,GAAG,CAAC,IAAI/B,UAAU,CAAC8B,SAAS,CAAC,CAAC,CAAC,CAACE,QAAQ,EAAE;MACrH;MACAnE,YAAY,GAAGmC,UAAU,CAAC8B,SAAS,CAAC,CAAC,CAAC,CAACE,QAAQ;IACjD;;IAEA;IACA,IAAIlE,cAAc,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;IAClD,IAAI8D,KAAK,CAACC,OAAO,CAAC7B,UAAU,CAAC8B,SAAS,CAAC,IAAI9B,UAAU,CAAC8B,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;MAC1E;MACAjE,cAAc,GAAG,CAAC,GAAG,IAAImE,GAAG,CAACjC,UAAU,CAAC8B,SAAS,CAACI,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;IAC5E;IAEA5F,WAAW,CAAC;MACVC,YAAY,EAAEuD,UAAU,CAACvD,YAAY,IAAI,EAAE;MAC3CC,IAAI,EAAE;QACJC,EAAE,EAAE,EAAAsD,gBAAA,GAAAD,UAAU,CAACtD,IAAI,cAAAuD,gBAAA,uBAAfA,gBAAA,CAAiBtD,EAAE,KAAI,EAAE;QAC7BC,EAAE,EAAE,EAAAsD,iBAAA,GAAAF,UAAU,CAACtD,IAAI,cAAAwD,iBAAA,uBAAfA,iBAAA,CAAiBtD,EAAE,KAAI;MAC7B,CAAC;MACDC,WAAW,EAAE;QACXF,EAAE,EAAE,EAAAwD,qBAAA,GAAAH,UAAU,CAACnD,WAAW,cAAAsD,qBAAA,uBAAtBA,qBAAA,CAAwBxD,EAAE,KAAI,EAAE;QACpCC,EAAE,EAAE,EAAAwD,sBAAA,GAAAJ,UAAU,CAACnD,WAAW,cAAAuD,sBAAA,uBAAtBA,sBAAA,CAAwBxD,EAAE,KAAI;MACpC,CAAC;MACDE,aAAa,EAAE;QACbH,EAAE,EAAE,EAAA0D,qBAAA,GAAAL,UAAU,CAAClD,aAAa,cAAAuD,qBAAA,uBAAxBA,qBAAA,CAA0B1D,EAAE,KAAI,EAAE;QACtCC,EAAE,EAAE,EAAA0D,sBAAA,GAAAN,UAAU,CAAClD,aAAa,cAAAwD,sBAAA,uBAAxBA,sBAAA,CAA0B1D,EAAE,KAAI;MACtC,CAAC;MACDG,UAAU,EAAE;QACVJ,EAAE,EAAE,EAAA4D,qBAAA,GAAAP,UAAU,CAACjD,UAAU,cAAAwD,qBAAA,uBAArBA,qBAAA,CAAuB5D,EAAE,KAAI,EAAE;QACnCC,EAAE,EAAE,EAAA4D,sBAAA,GAAAR,UAAU,CAACjD,UAAU,cAAAyD,sBAAA,uBAArBA,sBAAA,CAAuB5D,EAAE,KAAI;MACnC,CAAC;MACDI,OAAO,EAAE;QACPL,EAAE,EAAE,EAAA8D,mBAAA,GAAAT,UAAU,CAAChD,OAAO,cAAAyD,mBAAA,uBAAlBA,mBAAA,CAAoB9D,EAAE,KAAI,EAAE;QAChCC,EAAE,EAAE,EAAA8D,oBAAA,GAAAV,UAAU,CAAChD,OAAO,cAAA0D,oBAAA,uBAAlBA,oBAAA,CAAoB9D,EAAE,KAAI;MAChC,CAAC;MACDK,iBAAiB,EAAE2E,KAAK,CAACC,OAAO,CAAC7B,UAAU,CAAC/C,iBAAiB,CAAC,IAAI+C,UAAU,CAAC/C,iBAAiB,CAAC8E,MAAM,GAAG,CAAC,GACrG/B,UAAU,CAAC/C,iBAAiB,CAACiF,GAAG,CAACG,OAAO,KAAK;QAC3C1F,EAAE,EAAE,CAAA0F,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE1F,EAAE,KAAI,EAAE;QACrBC,EAAE,EAAE,CAAAyF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEzF,EAAE,KAAI;MACrB,CAAC,CAAC,CAAC,GACH,CAAC;QAAED,EAAE,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAG,CAAC,CAAC;MACxBM,OAAO,EAAE;QACPC,MAAM,EAAE;UACNR,EAAE,EAAE,EAAAgE,mBAAA,GAAAX,UAAU,CAAC9C,OAAO,cAAAyD,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBxD,MAAM,cAAAyD,qBAAA,uBAA1BA,qBAAA,CAA4BjE,EAAE,KAAI,EAAE;UACxCC,EAAE,EAAE,EAAAiE,oBAAA,GAAAb,UAAU,CAAC9C,OAAO,cAAA2D,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoB1D,MAAM,cAAA2D,qBAAA,uBAA1BA,qBAAA,CAA4BlE,EAAE,KAAI;QACxC,CAAC;QACDQ,IAAI,EAAE;UACJT,EAAE,EAAE,EAAAoE,oBAAA,GAAAf,UAAU,CAAC9C,OAAO,cAAA6D,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoB3D,IAAI,cAAA4D,qBAAA,uBAAxBA,qBAAA,CAA0BrE,EAAE,KAAI,EAAE;UACtCC,EAAE,EAAE,EAAAqE,oBAAA,GAAAjB,UAAU,CAAC9C,OAAO,cAAA+D,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoB7D,IAAI,cAAA8D,qBAAA,uBAAxBA,qBAAA,CAA0BtE,EAAE,KAAI;QACtC,CAAC;QACDS,OAAO,EAAE;UACPV,EAAE,EAAE,EAAAwE,oBAAA,GAAAnB,UAAU,CAAC9C,OAAO,cAAAiE,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoB9D,OAAO,cAAA+D,qBAAA,uBAA3BA,qBAAA,CAA6BzE,EAAE,KAAI,EAAE;UACzCC,EAAE,EAAE,EAAAyE,oBAAA,GAAArB,UAAU,CAAC9C,OAAO,cAAAmE,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoBhE,OAAO,cAAAiE,qBAAA,uBAA3BA,qBAAA,CAA6B1E,EAAE,KAAI;QACzC,CAAC;QACDU,UAAU,EAAE,EAAAiE,oBAAA,GAAAvB,UAAU,CAAC9C,OAAO,cAAAqE,oBAAA,uBAAlBA,oBAAA,CAAoBjE,UAAU,KAAI;MAChD,CAAC;MACDC,WAAW,EAAE;QACXC,KAAK,EAAE,EAAAgE,qBAAA,GAAAxB,UAAU,CAACzC,WAAW,cAAAiE,qBAAA,uBAAtBA,qBAAA,CAAwBhE,KAAK,KAAI,EAAE;QAC1CC,KAAK,EAAE,EAAAgE,sBAAA,GAAAzB,UAAU,CAACzC,WAAW,cAAAkE,sBAAA,uBAAtBA,sBAAA,CAAwBhE,KAAK,KAAI,EAAE;QAC1CC,OAAO,EAAE,EAAAgE,sBAAA,GAAA1B,UAAU,CAACzC,WAAW,cAAAmE,sBAAA,uBAAtBA,sBAAA,CAAwBhE,OAAO,KAAI;MAC9C,CAAC;MACDC,aAAa,EAAEqC,UAAU,CAACrC,aAAa,GACnC,IAAI2E,IAAI,CAACtC,UAAU,CAACrC,aAAa,CAAC,CAAC4E,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAC9D,EAAE;MACN5E,WAAW,EAAEoC,UAAU,CAACpC,WAAW,GAC/B,IAAI0E,IAAI,CAACtC,UAAU,CAACpC,WAAW,CAAC,CAAC2E,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAC5D,EAAE;MACN3E,YAAY,EAAEA,YAAY;MAC1BC,cAAc,EAAEA,cAAc;MAC9BC,QAAQ,EAAE6D,KAAK,CAACC,OAAO,CAAC7B,UAAU,CAACjC,QAAQ,CAAC,GAAGiC,UAAU,CAACjC,QAAQ,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC;MACzFC,IAAI,EAAEgC,UAAU,CAAChC,IAAI,IAAI,EAAE;MAC3BC,KAAK,EAAE+B,UAAU,CAAC/B,KAAK,IAAI,EAAE;MAC7BC,MAAM,EAAE8B,UAAU,CAAC9B,MAAM,IAAI;IAC/B,CAAC,CAAC;IAEF5B,YAAY,CAAC,IAAI,CAAC;IAClBN,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMyG,mBAAmB,GAAGA,CAAA,KAAM;IAChCjG,WAAW,CAAC;MACVC,YAAY,EAAE,EAAE;MAChBC,IAAI,EAAE;QAAEC,EAAE,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAG,CAAC;MACxBC,WAAW,EAAE;QAAEF,EAAE,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAG,CAAC;MAC/BE,aAAa,EAAE;QAAEH,EAAE,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAG,CAAC;MACjCG,UAAU,EAAE;QAAEJ,EAAE,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAG,CAAC;MAC9BI,OAAO,EAAE;QAAEL,EAAE,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAG,CAAC;MAC3BK,iBAAiB,EAAE,CAAC;QAAEN,EAAE,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAG,CAAC,CAAC;MACvCM,OAAO,EAAE;QACPC,MAAM,EAAE;UAAER,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QAC1BQ,IAAI,EAAE;UAAET,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QACxBS,OAAO,EAAE;UAAEV,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG,CAAC;QAC3BU,UAAU,EAAE;MACd,CAAC;MACDC,WAAW,EAAE;QACXC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE;MACX,CAAC;MACDC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,GAAG;MAAE;MACnBC,cAAc,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;MAAE;MAC7CC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;MAAE;MAChCC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE;IACV,CAAC,CAAC;IACF5B,YAAY,CAAC,KAAK,CAAC;IACnBN,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAM0G,iBAAiB,GAAGA,CAAC3C,CAAC,EAAEtD,YAAY,KAAK;IAC7CsD,CAAC,CAAC4B,eAAe,CAAC,CAAC;IACnBzF,uBAAuB,CAACO,YAAY,CAAC;IACrCb,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAM+G,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMpE,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUJ,KAAK;QAAG;MAAE,CAAC;MAChE,MAAMpE,KAAK,CAAC2I,MAAM,CAAC,GAAGhE,OAAO,CAACC,GAAG,CAACC,iBAAiB,qBAAqB7C,oBAAoB,EAAE,EAAEsC,MAAM,CAAC;MACvGnD,eAAe,CAACD,YAAY,CAAC0H,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACrG,YAAY,KAAKR,oBAAoB,CAAC,CAAC;MACpFL,mBAAmB,CAAC,KAAK,CAAC;MAC1BM,uBAAuB,CAAC,IAAI,CAAC;MAC7BR,UAAU,CAAC,kCAAkC,CAAC;MAC9CqH,UAAU,CAAC,MAAMrH,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,CAAC,OAAOwD,GAAG,EAAE;MAAA,IAAA8D,cAAA,EAAAC,mBAAA;MACZzH,QAAQ,CAAC,EAAAwH,cAAA,GAAA9D,GAAG,CAACR,QAAQ,cAAAsE,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc/D,IAAI,cAAAgE,mBAAA,uBAAlBA,mBAAA,CAAoBzD,OAAO,KAAI,6BAA6B,CAAC;MACtE5D,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAMsH,iBAAiB,GAAGA,CAACnD,CAAC,EAAEoD,KAAK,EAAEC,QAAQ,EAAEC,IAAI,KAAK;IACtD,MAAMC,KAAK,GAAGvD,CAAC,CAACwD,MAAM,CAACD,KAAK;IAC5B9G,WAAW,CAAEgH,IAAI,IAAK;MACpB;MACA,IAAIL,KAAK,KAAK,SAAS,IAAIC,QAAQ,IAAIC,IAAI,EAAE;QAC3C,OAAO;UACL,GAAGG,IAAI;UACPtG,OAAO,EAAE;YACP,GAAGsG,IAAI,CAACtG,OAAO;YACf,CAACkG,QAAQ,GAAG;cACV,GAAGI,IAAI,CAACtG,OAAO,CAACkG,QAAQ,CAAC;cACzB,CAACC,IAAI,GAAGC;YACV;UACF;QACF,CAAC;MACH;MACA;MAAA,KACK,IAAID,IAAI,EAAE;QACb,OAAO;UACL,GAAGG,IAAI;UACP,CAACL,KAAK,GAAG;YACP,GAAGK,IAAI,CAACL,KAAK,CAAC;YACd,CAACE,IAAI,GAAGC;UACV;QACF,CAAC;MACH;MACA;MAAA,KACK,IAAIF,QAAQ,EAAE;QACjB,OAAO;UACL,GAAGI,IAAI;UACP,CAACL,KAAK,GAAG;YACP,GAAGK,IAAI,CAACL,KAAK,CAAC;YACd,CAACC,QAAQ,GAAGE;UACd;QACF,CAAC;MACH;MACA;MACA,OAAO;QACL,GAAGE,IAAI;QACP,CAACL,KAAK,GAAGG;MACX,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EAED,MAAMG,mBAAmB,GAAGA,CAACC,KAAK,EAAEL,IAAI,EAAEC,KAAK,KAAK;IAClD9G,WAAW,CAAEgH,IAAI,IAAK;MACpB,MAAMG,WAAW,GAAG,CAAC,GAAGH,IAAI,CAACvG,iBAAiB,CAAC;MAC/C0G,WAAW,CAACD,KAAK,CAAC,GAAG;QAAE,GAAGC,WAAW,CAACD,KAAK,CAAC;QAAE,CAACL,IAAI,GAAGC;MAAM,CAAC;MAC7D,OAAO;QAAE,GAAGE,IAAI;QAAEvG,iBAAiB,EAAE0G;MAAY,CAAC;IACpD,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBpH,WAAW,CAAEgH,IAAI,KAAM;MACrB,GAAGA,IAAI;MACPvG,iBAAiB,EAAE,CAAC,GAAGuG,IAAI,CAACvG,iBAAiB,EAAE;QAAEN,EAAE,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAG,CAAC;IACnE,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMiH,aAAa,GAAIH,KAAK,IAAK;IAC/BlH,WAAW,CAAEgH,IAAI,KAAM;MACrB,GAAGA,IAAI;MACPvG,iBAAiB,EAAEuG,IAAI,CAACvG,iBAAiB,CAAC4F,MAAM,CAAC,CAACiB,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKL,KAAK;IACxE,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMM,wBAAwB,GAAIjE,CAAC,IAAK;IACtCvD,WAAW,CAAEgH,IAAI,KAAM;MACrB,GAAGA,IAAI;MACP3F,YAAY,EAAEoG,QAAQ,CAAClE,CAAC,CAACwD,MAAM,CAACD,KAAK;IACvC,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMY,oBAAoB,GAAGA,CAACR,KAAK,EAAEJ,KAAK,KAAK;IAC7C9G,WAAW,CAAEgH,IAAI,IAAK;MACpB,MAAMW,QAAQ,GAAG,CAAC,GAAGX,IAAI,CAAC1F,cAAc,CAAC;MACzCqG,QAAQ,CAACT,KAAK,CAAC,GAAGJ,KAAK;MACvB,OAAO;QAAE,GAAGE,IAAI;QAAE1F,cAAc,EAAEqG;MAAS,CAAC;IAC9C,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB5H,WAAW,CAAEgH,IAAI,KAAM;MACrB,GAAGA,IAAI;MACP1F,cAAc,EAAE,CAAC,GAAG0F,IAAI,CAAC1F,cAAc,EAAE,OAAO;IAClD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMuG,cAAc,GAAIX,KAAK,IAAK;IAChClH,WAAW,CAAEgH,IAAI,KAAM;MACrB,GAAGA,IAAI;MACP1F,cAAc,EAAE0F,IAAI,CAAC1F,cAAc,CAAC+E,MAAM,CAAC,CAACiB,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKL,KAAK;IAClE,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMY,mBAAmB,GAAIC,GAAG,IAAK;IACnC/H,WAAW,CAAEgH,IAAI,IAAK;MACpB,MAAMgB,eAAe,GAAG,CAAC,GAAGhB,IAAI,CAACzF,QAAQ,CAAC;MAC1C,IAAIyG,eAAe,CAACC,QAAQ,CAACF,GAAG,CAAC,EAAE;QACjC;QACA,OAAO;UACL,GAAGf,IAAI;UACPzF,QAAQ,EAAEyG,eAAe,CAAC3B,MAAM,CAAC6B,OAAO,IAAIA,OAAO,KAAKH,GAAG;QAC7D,CAAC;MACH,CAAC,MAAM;QACL;QACA,OAAO;UACL,GAAGf,IAAI;UACPzF,QAAQ,EAAE,CAAC,GAAGyG,eAAe,EAAED,GAAG;QACpC,CAAC;MACH;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,sBAAsB,GAAG,MAAO5E,CAAC,IAAK;IAC1CA,CAAC,CAAC6E,cAAc,CAAC,CAAC;IAClBpJ,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;;IAEd;IACA,IAAI,CAACa,QAAQ,CAACE,YAAY,CAACoI,IAAI,CAAC,CAAC,EAAE;MACjCrJ,QAAQ,CAAC,2BAA2B,CAAC;MACrC;IACF;IAEA,IAAI,CAACe,QAAQ,CAACG,IAAI,CAACC,EAAE,CAACkI,IAAI,CAAC,CAAC,IAAI,CAACtI,QAAQ,CAACG,IAAI,CAACE,EAAE,CAACiI,IAAI,CAAC,CAAC,EAAE;MACxDrJ,QAAQ,CAAC,wDAAwD,CAAC;MAClE;IACF;;IAEA;IACA,IAAI,CAACe,QAAQ,CAACW,OAAO,CAACC,MAAM,CAACR,EAAE,CAACkI,IAAI,CAAC,CAAC,IAAI,CAACtI,QAAQ,CAACW,OAAO,CAACC,MAAM,CAACP,EAAE,CAACiI,IAAI,CAAC,CAAC,EAAE;MAC5ErJ,QAAQ,CAAC,uDAAuD,CAAC;MACjE;IACF;IAEA,IAAI,CAACe,QAAQ,CAACW,OAAO,CAACE,IAAI,CAACT,EAAE,CAACkI,IAAI,CAAC,CAAC,IAAI,CAACtI,QAAQ,CAACW,OAAO,CAACE,IAAI,CAACR,EAAE,CAACiI,IAAI,CAAC,CAAC,EAAE;MACxErJ,QAAQ,CAAC,6CAA6C,CAAC;MACvD;IACF;IAEA,IAAI,CAACe,QAAQ,CAACW,OAAO,CAACG,OAAO,CAACV,EAAE,CAACkI,IAAI,CAAC,CAAC,IAAI,CAACtI,QAAQ,CAACW,OAAO,CAACG,OAAO,CAACT,EAAE,CAACiI,IAAI,CAAC,CAAC,EAAE;MAC9ErJ,QAAQ,CAAC,gDAAgD,CAAC;MAC1D;IACF;IAEA,IAAI,CAACe,QAAQ,CAACgB,WAAW,CAACC,KAAK,CAACqH,IAAI,CAAC,CAAC,IAAI,CAACtI,QAAQ,CAACgB,WAAW,CAACE,KAAK,CAACoH,IAAI,CAAC,CAAC,EAAE;MAC5ErJ,QAAQ,CAAC,mDAAmD,CAAC;MAC7D;IACF;IAEA,IAAI,CAACe,QAAQ,CAACoB,aAAa,IAAI,CAACpB,QAAQ,CAACqB,WAAW,EAAE;MACpDpC,QAAQ,CAAC,uCAAuC,CAAC;MACjD;IACF;IAEA,IAAI,CAACe,QAAQ,CAACsB,YAAY,IAAItB,QAAQ,CAACsB,YAAY,GAAG,EAAE,EAAE;MACxDrC,QAAQ,CAAC,sDAAsD,CAAC;MAChE;IACF;IAEA,IAAI,CAACe,QAAQ,CAACuB,cAAc,IAAIvB,QAAQ,CAACuB,cAAc,CAACiE,MAAM,KAAK,CAAC,EAAE;MACpEvG,QAAQ,CAAC,8CAA8C,CAAC;MACxD;IACF;;IAEA;IACA,MAAMsJ,YAAY,GAAG;MAAE,GAAGvI;IAAS,CAAC;;IAEpC;IACA,CAAC,aAAa,EAAE,eAAe,EAAE,YAAY,EAAE,SAAS,CAAC,CAACwI,OAAO,CAAC5B,KAAK,IAAI;MACzE,IAAI,CAAC2B,YAAY,CAAC3B,KAAK,CAAC,CAACxG,EAAE,CAACkI,IAAI,CAAC,CAAC,IAAI,CAACC,YAAY,CAAC3B,KAAK,CAAC,CAACvG,EAAE,CAACiI,IAAI,CAAC,CAAC,EAAE;QACpEC,YAAY,CAAC3B,KAAK,CAAC,GAAG;UACpBxG,EAAE,EAAEmI,YAAY,CAAC3B,KAAK,CAAC,CAACxG,EAAE,CAACkI,IAAI,CAAC,CAAC,IAAI,cAAc;UACnDjI,EAAE,EAAEkI,YAAY,CAAC3B,KAAK,CAAC,CAACvG,EAAE,CAACiI,IAAI,CAAC,CAAC,IAAI;QACvC,CAAC;MACH;IACF,CAAC,CAAC;;IAEF;IACA,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC,CAACE,OAAO,CAAC5B,KAAK,IAAI;MAC7C,IAAI,CAAC2B,YAAY,CAAC5H,OAAO,CAACiG,KAAK,CAAC,CAACxG,EAAE,CAACkI,IAAI,CAAC,CAAC,IAAI,CAACC,YAAY,CAAC5H,OAAO,CAACiG,KAAK,CAAC,CAACvG,EAAE,CAACiI,IAAI,CAAC,CAAC,EAAE;QACpFC,YAAY,CAAC5H,OAAO,CAACiG,KAAK,CAAC,GAAG;UAC5BxG,EAAE,EAAEmI,YAAY,CAAC5H,OAAO,CAACiG,KAAK,CAAC,CAACxG,EAAE,CAACkI,IAAI,CAAC,CAAC,IAAI,cAAc;UAC3DjI,EAAE,EAAEkI,YAAY,CAAC5H,OAAO,CAACiG,KAAK,CAAC,CAACvG,EAAE,CAACiI,IAAI,CAAC,CAAC,IAAI;QAC/C,CAAC;MACH;IACF,CAAC,CAAC;;IAEF;IACA,IAAIC,YAAY,CAAC7H,iBAAiB,CAAC8E,MAAM,KAAK,CAAC,EAAE;MAC/C+C,YAAY,CAAC7H,iBAAiB,GAAG,CAAC;QAAEN,EAAE,EAAE,mBAAmB;QAAEC,EAAE,EAAE;MAAmB,CAAC,CAAC;IACxF,CAAC,MAAM;MACLkI,YAAY,CAAC7H,iBAAiB,GAAG6H,YAAY,CAAC7H,iBAAiB,CAACiF,GAAG,CAACG,OAAO,KAAK;QAC9E1F,EAAE,EAAE0F,OAAO,CAAC1F,EAAE,CAACkI,IAAI,CAAC,CAAC,IAAI,mBAAmB;QAC5CjI,EAAE,EAAEyF,OAAO,CAACzF,EAAE,CAACiI,IAAI,CAAC,CAAC,IAAI;MAC3B,CAAC,CAAC,CAAC;IACL;IAEA,IAAI;MACF,MAAMtG,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUJ,KAAK;QAAG;MAAE,CAAC;MAChE,IAAIK,QAAQ;MAEZK,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE8F,YAAY,CAAC;MAExD,IAAIzI,SAAS,EAAE;QACbqC,QAAQ,GAAG,MAAMzE,KAAK,CAAC+K,GAAG,CACxB,GAAGpG,OAAO,CAACC,GAAG,CAACC,iBAAiB,qBAAqBgG,YAAY,CAACrI,YAAY,EAAE,EAChFqI,YAAY,EACZvG,MACF,CAAC;QACDnD,eAAe,CAACD,YAAY,CAAC+G,GAAG,CAACY,CAAC,IAAIA,CAAC,CAACrG,YAAY,KAAKqI,YAAY,CAACrI,YAAY,GAAGiC,QAAQ,CAACO,IAAI,GAAG6D,CAAC,CAAC,CAAC;QACxGpH,UAAU,CAAC,kCAAkC,CAAC;MAChD,CAAC,MAAM;QACLgD,QAAQ,GAAG,MAAMzE,KAAK,CAACgL,IAAI,CAAC,GAAGrG,OAAO,CAACC,GAAG,CAACC,iBAAiB,mBAAmB,EAAEgG,YAAY,EAAEvG,MAAM,CAAC;QACtGnD,eAAe,CAAC,CAAC,GAAGD,YAAY,EAAEuD,QAAQ,CAACO,IAAI,CAAC,CAAC;QACjDvD,UAAU,CAAC,kCAAkC,CAAC;MAChD;MACAM,sBAAsB,CAAC,KAAK,CAAC;MAC7B+G,UAAU,CAAC,MAAMrH,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,CAAC,OAAOwD,GAAG,EAAE;MAAA,IAAAgG,cAAA,EAAAC,mBAAA,EAAAC,cAAA,EAAAC,mBAAA;MACZtG,OAAO,CAACxD,KAAK,CAAC,8BAA8B,EAAE2D,GAAG,CAAC;;MAElD;MACA,IAAIO,YAAY,GAAG,6BAA6B;MAEhD,KAAAyF,cAAA,GAAIhG,GAAG,CAACR,QAAQ,cAAAwG,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcjG,IAAI,cAAAkG,mBAAA,eAAlBA,mBAAA,CAAoB5J,KAAK,EAAE;QAC7B;QACAkE,YAAY,IAAIP,GAAG,CAACR,QAAQ,CAACO,IAAI,CAAC1D,KAAK;MACzC,CAAC,MAAM,KAAA6J,cAAA,GAAIlG,GAAG,CAACR,QAAQ,cAAA0G,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcnG,IAAI,cAAAoG,mBAAA,eAAlBA,mBAAA,CAAoB7F,OAAO,EAAE;QACtC;QACAC,YAAY,IAAIP,GAAG,CAACR,QAAQ,CAACO,IAAI,CAACO,OAAO;MAC3C,CAAC,MAAM,IAAIN,GAAG,CAACM,OAAO,EAAE;QACtB;QACAC,YAAY,IAAIP,GAAG,CAACM,OAAO;MAC7B;MAEAhE,QAAQ,CAACiE,YAAY,CAAC;IACxB;EACF,CAAC;EAED,MAAM6F,SAAS,GAAG;IAChBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEE,UAAU,EAAE;QAAEC,eAAe,EAAE;MAAI;IAAE;EAC3D,CAAC;EAED,MAAMC,IAAI,GAAG;IACXL,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAG,CAAC;IAC7BJ,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAE;EAC3B,CAAC;EAED,IAAIxK,OAAO,EAAE;IACX,oBAAOP,OAAA,CAACN,MAAM;MAAAsL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnB;EAEA,oBACEnL,OAAA;IAAKoL,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvCrL,OAAA,CAACF,iBAAiB;MAACwL,MAAM,EAAEnL,WAAY;MAACoL,SAAS,EAAEnL;IAAe;MAAA4K,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAErEnL,OAAA;MAAKoL,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDrL,OAAA,CAACP,MAAM;QAAC+L,aAAa,EAAEA,CAAA,KAAMpL,cAAc,CAAC,CAACD,WAAW;MAAE;QAAA6K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE7DnL,OAAA;QAAMoL,SAAS,EAAC,0FAA0F;QAAAC,QAAA,eACxGrL,OAAA;UAAKoL,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAC/B5K,KAAK,iBACJT,OAAA,CAACZ,MAAM,CAACqM,GAAG;YACTC,OAAO,EAAE;cAAEhB,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCY,OAAO,EAAE;cAAEjB,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE;YAAE,CAAE;YAC9BK,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAE7ErL,OAAA;cAAKoL,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCrL,OAAA;gBAAK4L,KAAK,EAAC,4BAA4B;gBAACR,SAAS,EAAC,2BAA2B;gBAACS,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAT,QAAA,eACnHrL,OAAA;kBAAM+L,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,mNAAmN;kBAACC,QAAQ,EAAC;gBAAS;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjQ,CAAC,eACNnL,OAAA;gBAAGoL,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAE5K;cAAK;gBAAAuK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,EACAxK,OAAO,iBACNX,OAAA,CAACZ,MAAM,CAACqM,GAAG;YACTC,OAAO,EAAE;cAAEhB,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCY,OAAO,EAAE;cAAEjB,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE;YAAE,CAAE;YAC9BK,SAAS,EAAC,uEAAuE;YAAAC,QAAA,eAEjFrL,OAAA;cAAKoL,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCrL,OAAA;gBAAK4L,KAAK,EAAC,4BAA4B;gBAACR,SAAS,EAAC,6BAA6B;gBAACS,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAT,QAAA,eACrHrL,OAAA;kBAAM+L,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,uIAAuI;kBAACC,QAAQ,EAAC;gBAAS;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrL,CAAC,eACNnL,OAAA;gBAAGoL,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAE1K;cAAO;gBAAAqK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eAEDnL,OAAA,CAACZ,MAAM,CAACqM,GAAG;YACTC,OAAO,EAAE;cAAEhB,OAAO,EAAE;YAAE,CAAE;YACxBiB,OAAO,EAAE;cAAEjB,OAAO,EAAE;YAAE,CAAE;YACxBE,UAAU,EAAE;cAAE1D,QAAQ,EAAE;YAAI,CAAE;YAAAmE,QAAA,gBAE9BrL,OAAA;cAAKoL,SAAS,EAAC,kFAAkF;cAAAC,QAAA,gBAC/FrL,OAAA;gBAAAqL,QAAA,gBACErL,OAAA;kBAAIoL,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAAC;gBAAY;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpFnL,OAAA;kBAAGoL,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAA0C;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACNnL,OAAA,CAACZ,MAAM,CAAC8M,MAAM;gBACZC,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAC1BE,OAAO,EAAE3E,mBAAoB;gBAC7ByD,SAAS,EAAC,oMAAoM;gBAAAC,QAAA,gBAE9MrL,OAAA,CAACR,MAAM;kBAAC4L,SAAS,EAAC;gBAAc;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,kBAErC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eAENnL,OAAA,CAACZ,MAAM,CAACqM,GAAG;cACTc,QAAQ,EAAE/B,SAAU;cACpBkB,OAAO,EAAC,QAAQ;cAChBc,WAAW,EAAC,MAAM;cAClBC,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBtB,SAAS,EAAC,kHAAkH;cAAAC,QAAA,eAE5HrL,OAAA;gBAAKoL,SAAS,EAAC,KAAK;gBAAAC,QAAA,eAClBrL,OAAA;kBAAKoL,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAC9BrL,OAAA;oBAAOoL,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,gBACpDrL,OAAA;sBAAOoL,SAAS,EAAC,YAAY;sBAAAC,QAAA,eAC3BrL,OAAA;wBAAAqL,QAAA,gBACErL,OAAA;0BAAIoL,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAE;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACtGnL,OAAA;0BAAIoL,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAS;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC7GnL,OAAA;0BAAIoL,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAS;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC7GnL,OAAA;0BAAIoL,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAK;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACzGnL,OAAA;0BAAIoL,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAK;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACzGnL,OAAA;0BAAIoL,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAO;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACRnL,OAAA;sBAAOoL,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EACjDhL,YAAY,CAAC4G,MAAM,KAAK,CAAC,gBACxBjH,OAAA;wBAAAqL,QAAA,eACErL,OAAA;0BAAI2M,OAAO,EAAC,GAAG;0BAACvB,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,eAC/CrL,OAAA;4BAAKoL,SAAS,EAAC,2CAA2C;4BAAAC,QAAA,gBACxDrL,OAAA,CAACX,YAAY;8BAAC+L,SAAS,EAAC;4BAA8B;8BAAAJ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eACzDnL,OAAA;8BAAIoL,SAAS,EAAC,mCAAmC;8BAAAC,QAAA,EAAC;4BAAqB;8BAAAL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAC5EnL,OAAA;8BAAGoL,SAAS,EAAC,oBAAoB;8BAAAC,QAAA,EAAC;4BAAwC;8BAAAL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3E;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,GAEL9K,YAAY,CAAC+G,GAAG,CAAElC,UAAU;wBAAA,IAAA0H,iBAAA,EAAAC,iBAAA,EAAAC,sBAAA,EAAAC,sBAAA;wBAAA,oBAC1B/M,OAAA,CAACZ,MAAM,CAAC4N,EAAE;0BAERT,QAAQ,EAAEzB,IAAK;0BACfwB,OAAO,EAAEA,CAAA,KAAMzH,cAAc,CAACK,UAAU,CAACvD,YAAY,CAAE;0BACvDyJ,SAAS,EAAC,iCAAiC;0BAAAC,QAAA,gBAE3CrL,OAAA;4BAAIoL,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAAEnG,UAAU,CAACvD,YAAY,IAAI;0BAAK;4BAAAqJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACzGnL,OAAA;4BAAIoL,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAAE,EAAAuB,iBAAA,GAAA1H,UAAU,CAACtD,IAAI,cAAAgL,iBAAA,uBAAfA,iBAAA,CAAiB/K,EAAE,KAAI;0BAAK;4BAAAmJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACrGnL,OAAA;4BAAIoL,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAAE,EAAAwB,iBAAA,GAAA3H,UAAU,CAACtD,IAAI,cAAAiL,iBAAA,uBAAfA,iBAAA,CAAiB/K,EAAE,KAAI;0BAAK;4BAAAkJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACrGnL,OAAA;4BAAIoL,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAAE,EAAAyB,sBAAA,GAAA5H,UAAU,CAACzC,WAAW,cAAAqK,sBAAA,uBAAtBA,sBAAA,CAAwBpK,KAAK,KAAI;0BAAK;4BAAAsI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC/GnL,OAAA;4BAAIoL,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAAE,EAAA0B,sBAAA,GAAA7H,UAAU,CAACzC,WAAW,cAAAsK,sBAAA,uBAAtBA,sBAAA,CAAwBpK,KAAK,KAAI;0BAAK;4BAAAqI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC/GnL,OAAA;4BAAIoL,SAAS,EAAC,kEAAkE;4BAAAC,QAAA,gBAC9ErL,OAAA;8BACEsM,OAAO,EAAGrH,CAAC,IAAKD,eAAe,CAACC,CAAC,EAAEC,UAAU,CAAE;8BAC/CkG,SAAS,EAAC,qCAAqC;8BAC/C6B,KAAK,EAAC,MAAM;8BAAA5B,QAAA,eAEZrL,OAAA,CAACV,MAAM;gCAAC8L,SAAS,EAAC;8BAAS;gCAAAJ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACxB,CAAC,eACTnL,OAAA;8BACEsM,OAAO,EAAGrH,CAAC,IAAK2C,iBAAiB,CAAC3C,CAAC,EAAEC,UAAU,CAACvD,YAAY,CAAE;8BAC9DyJ,SAAS,EAAC,iCAAiC;8BAC3C6B,KAAK,EAAC,QAAQ;8BAAA5B,QAAA,eAEdrL,OAAA,CAACT,OAAO;gCAAC6L,SAAS,EAAC;8BAAS;gCAAAJ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACzB,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACP,CAAC;wBAAA,GAzBAjG,UAAU,CAACvD,YAAY;0BAAAqJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OA0BnB,CAAC;sBAAA,CACb;oBACF;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENnL,OAAA,CAACL,YAAY;MACX2L,MAAM,EAAEzK,gBAAiB;MACzBqM,OAAO,EAAEA,CAAA,KAAMpM,mBAAmB,CAAC,KAAK,CAAE;MAC1CqM,SAAS,EAAEtF,mBAAoB;MAC/BoF,KAAK,EAAC,gBAAgB;MACtBvI,OAAO,EAAC;IAAgF;MAAAsG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzF,CAAC,eAEFnL,OAAA,CAACJ,sBAAsB;MACrB0L,MAAM,EAAEvK,gBAAiB;MACzBmM,OAAO,EAAEA,CAAA,KAAMlM,mBAAmB,CAAC,KAAK,CAAE;MAC1CkE,UAAU,EAAE7D;IAAmB;MAAA2J,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC,EAEDlK,mBAAmB,iBAClBjB,OAAA;MAAKoL,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7FrL,OAAA,CAACZ,MAAM,CAACqM,GAAG;QACTC,OAAO,EAAE;UAAEU,KAAK,EAAE,GAAG;UAAE1B,OAAO,EAAE;QAAE,CAAE;QACpCiB,OAAO,EAAE;UAAES,KAAK,EAAE,CAAC;UAAE1B,OAAO,EAAE;QAAE,CAAE;QAClCU,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAEzFrL,OAAA;UAAKoL,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBrL,OAAA;YAAKoL,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDrL,OAAA;cAAIoL,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAE9J,SAAS,GAAG,iBAAiB,GAAG;YAAgB;cAAAyJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzGnL,OAAA;cAAQsM,OAAO,EAAEA,CAAA,KAAMpL,sBAAsB,CAAC,KAAK,CAAE;cAACkK,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eACjGrL,OAAA;gBAAK4L,KAAK,EAAC,4BAA4B;gBAACR,SAAS,EAAC,SAAS;gBAACU,IAAI,EAAC,MAAM;gBAACD,OAAO,EAAC,WAAW;gBAACuB,MAAM,EAAC,cAAc;gBAAA/B,QAAA,eAC/GrL,OAAA;kBAAMqN,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACvB,CAAC,EAAC;gBAAsB;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNnL,OAAA;YAAMwN,QAAQ,EAAE3D,sBAAuB;YAACuB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAC3DrL,OAAA;cAAKoL,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDrL,OAAA;gBAAAqL,QAAA,gBACErL,OAAA;kBAAOoL,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAc;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtFnL,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXjF,KAAK,EAAE/G,QAAQ,CAACE,YAAa;kBAC7B+L,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,cAAc,CAAE;kBACtDmG,SAAS,EAAC,6GAA6G;kBACvHuC,QAAQ;kBACRC,QAAQ,EAAErM;gBAAU;kBAAAyJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnL,OAAA;gBAAAqL,QAAA,gBACErL,OAAA;kBAAOoL,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAe;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvFnL,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXjF,KAAK,EAAE/G,QAAQ,CAACG,IAAI,CAACC,EAAG;kBACxB6L,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAE;kBAC1DmG,SAAS,EAAC,6GAA6G;kBACvHuC,QAAQ;gBAAA;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnL,OAAA;gBAAAqL,QAAA,gBACErL,OAAA;kBAAOoL,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAc;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtFnL,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXjF,KAAK,EAAE/G,QAAQ,CAACG,IAAI,CAACE,EAAG;kBACxB4L,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAE;kBAC1DmG,SAAS,EAAC,6GAA6G;kBACvHuC,QAAQ;gBAAA;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnL,OAAA;gBAAAqL,QAAA,gBACErL,OAAA;kBAAOoL,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAM;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9EnL,OAAA;kBACEyN,IAAI,EAAC,KAAK;kBACVjF,KAAK,EAAE/G,QAAQ,CAACgB,WAAW,CAACC,KAAM;kBAClCgL,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,aAAa,EAAE,OAAO,CAAE;kBAC9DmG,SAAS,EAAC,6GAA6G;kBACvHuC,QAAQ;gBAAA;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnL,OAAA;gBAAAqL,QAAA,gBACErL,OAAA;kBAAOoL,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAM;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9EnL,OAAA;kBACEyN,IAAI,EAAC,OAAO;kBACZjF,KAAK,EAAE/G,QAAQ,CAACgB,WAAW,CAACE,KAAM;kBAClC+K,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,aAAa,EAAE,OAAO,CAAE;kBAC9DmG,SAAS,EAAC,6GAA6G;kBACvHuC,QAAQ;gBAAA;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnL,OAAA;gBAAAqL,QAAA,gBACErL,OAAA;kBAAOoL,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAO;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/EnL,OAAA;kBACEyN,IAAI,EAAC,KAAK;kBACVjF,KAAK,EAAE/G,QAAQ,CAACgB,WAAW,CAACG,OAAQ;kBACpC8K,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,aAAa,EAAE,SAAS,CAAE;kBAChEmG,SAAS,EAAC;gBAA6G;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnL,OAAA;cAAKoL,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDrL,OAAA;gBAAAqL,QAAA,gBACErL,OAAA;kBAAOoL,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzFnL,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXjF,KAAK,EAAE/G,QAAQ,CAACW,OAAO,CAACC,MAAM,CAACR,EAAG;kBAClC6L,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAE;kBACjEmG,SAAS,EAAC,2GAA2G;kBACrHuC,QAAQ;gBAAA;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnL,OAAA;gBAAAqL,QAAA,gBACErL,OAAA;kBAAOoL,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxFnL,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXjF,KAAK,EAAE/G,QAAQ,CAACW,OAAO,CAACC,MAAM,CAACP,EAAG;kBAClC4L,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAE;kBACjEmG,SAAS,EAAC,2GAA2G;kBACrHuC,QAAQ;gBAAA;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnL,OAAA;gBAAAqL,QAAA,gBACErL,OAAA;kBAAOoL,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAe;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvFnL,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXjF,KAAK,EAAE/G,QAAQ,CAACW,OAAO,CAACE,IAAI,CAACT,EAAG;kBAChC6L,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,CAAE;kBAC/DmG,SAAS,EAAC,2GAA2G;kBACrHuC,QAAQ;gBAAA;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnL,OAAA;gBAAAqL,QAAA,gBACErL,OAAA;kBAAOoL,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAc;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtFnL,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXjF,KAAK,EAAE/G,QAAQ,CAACW,OAAO,CAACE,IAAI,CAACR,EAAG;kBAChC4L,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,CAAE;kBAC/DmG,SAAS,EAAC,2GAA2G;kBACrHuC,QAAQ;gBAAA;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnL,OAAA;gBAAAqL,QAAA,gBACErL,OAAA;kBAAOoL,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1FnL,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXjF,KAAK,EAAE/G,QAAQ,CAACW,OAAO,CAACG,OAAO,CAACV,EAAG;kBACnC6L,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAE;kBAClEmG,SAAS,EAAC,2GAA2G;kBACrHuC,QAAQ;gBAAA;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnL,OAAA;gBAAAqL,QAAA,gBACErL,OAAA;kBAAOoL,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzFnL,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXjF,KAAK,EAAE/G,QAAQ,CAACW,OAAO,CAACG,OAAO,CAACT,EAAG;kBACnC4L,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAE;kBAClEmG,SAAS,EAAC,2GAA2G;kBACrHuC,QAAQ;gBAAA;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnL,OAAA;gBAAAqL,QAAA,gBACErL,OAAA;kBAAOoL,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAW;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnFnL,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXjF,KAAK,EAAE/G,QAAQ,CAACW,OAAO,CAACI,UAAW;kBACnCkL,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,SAAS,EAAE,YAAY,CAAE;kBAC/DmG,SAAS,EAAC;gBAA2G;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnL,OAAA;cAAKoL,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDrL,OAAA;gBAAAqL,QAAA,gBACErL,OAAA;kBAAOoL,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxFnL,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXjF,KAAK,EAAE/G,QAAQ,CAACoB,aAAc;kBAC9B6K,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,eAAe,CAAE;kBACvDmG,SAAS,EAAC,2GAA2G;kBACrHuC,QAAQ;gBAAA;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnL,OAAA;gBAAAqL,QAAA,gBACErL,OAAA;kBAAOoL,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAc;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtFnL,OAAA;kBACEyN,IAAI,EAAC,MAAM;kBACXjF,KAAK,EAAE/G,QAAQ,CAACqB,WAAY;kBAC5B4K,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,aAAa,CAAE;kBACrDmG,SAAS,EAAC,2GAA2G;kBACrHuC,QAAQ;gBAAA;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnL,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAOoL,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAa;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrFnL,OAAA;gBAAKoL,SAAS,EAAC,kFAAkF;gBAAAC,QAAA,gBAC/FrL,OAAA;kBAAAqL,QAAA,gBACErL,OAAA;oBAAOoL,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAwB;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAChGnL,OAAA;oBACEyN,IAAI,EAAC,QAAQ;oBACbjF,KAAK,EAAE/G,QAAQ,CAACsB,YAAa;oBAC7B2K,QAAQ,EAAExE,wBAAyB;oBACnC2E,GAAG,EAAC,IAAI;oBACRC,IAAI,EAAC,IAAI;oBACT1C,SAAS,EAAC,2GAA2G;oBACrHuC,QAAQ;kBAAA;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNnL,OAAA;kBAAAqL,QAAA,gBACErL,OAAA;oBAAOoL,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAqB;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7FnL,OAAA;oBAAKoL,SAAS,EAAC,WAAW;oBAAAC,QAAA,GACvB5J,QAAQ,CAACuB,cAAc,CAACoE,GAAG,CAAC,CAACC,IAAI,EAAEuB,KAAK,kBACvC5I,OAAA;sBAAiBoL,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,gBACtDrL,OAAA;wBACEyN,IAAI,EAAC,MAAM;wBACXjF,KAAK,EAAEnB,IAAK;wBACZqG,QAAQ,EAAGzI,CAAC,IAAKmE,oBAAoB,CAACR,KAAK,EAAE3D,CAAC,CAACwD,MAAM,CAACD,KAAK,CAAE;wBAC7D4C,SAAS,EAAC,2GAA2G;wBACrHuC,QAAQ;sBAAA;wBAAA3C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CAAC,eACFnL,OAAA;wBACEyN,IAAI,EAAC,QAAQ;wBACbnB,OAAO,EAAEA,CAAA,KAAM/C,cAAc,CAACX,KAAK,CAAE;wBACrCwC,SAAS,EAAC,iCAAiC;wBAAAC,QAAA,eAE3CrL,OAAA;0BAAK4L,KAAK,EAAC,4BAA4B;0BAACR,SAAS,EAAC,SAAS;0BAACS,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,cAAc;0BAAAT,QAAA,eACjGrL,OAAA;4BAAM+L,QAAQ,EAAC,SAAS;4BAACC,CAAC,EAAC,6MAA6M;4BAACC,QAAQ,EAAC;0BAAS;4BAAAjB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3P;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GAhBDvC,KAAK;sBAAAoC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAiBV,CACN,CAAC,eACFnL,OAAA;sBACEyN,IAAI,EAAC,QAAQ;sBACbnB,OAAO,EAAEhD,WAAY;sBACrB8B,SAAS,EAAC,0FAA0F;sBAAAC,QAAA,EACrG;oBAED;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnL,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAOoL,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAQ;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChFnL,OAAA;gBAAKoL,SAAS,EAAC,kFAAkF;gBAAAC,QAAA,EAC9F,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC,CAACjE,GAAG,CAAEqC,GAAG,iBACtFzJ,OAAA;kBAAeoL,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC1CrL,OAAA;oBACEyN,IAAI,EAAC,UAAU;oBACfM,EAAE,EAAE,WAAWtE,GAAG,EAAG;oBACrBuE,OAAO,EAAEvM,QAAQ,CAACwB,QAAQ,CAAC0G,QAAQ,CAACF,GAAG,CAAE;oBACzCiE,QAAQ,EAAEA,CAAA,KAAMlE,mBAAmB,CAACC,GAAG,CAAE;oBACzC2B,SAAS,EAAC;kBAAmE;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CAAC,eACFnL,OAAA;oBAAOiO,OAAO,EAAE,WAAWxE,GAAG,EAAG;oBAAC2B,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC3E5B;kBAAG;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA,GAVA1B,GAAG;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWR,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnL,OAAA;gBAAGoL,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAA0C;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,eACNnL,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAOoL,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAqB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7FnL,OAAA;gBACEwI,KAAK,EAAE/G,QAAQ,CAACM,WAAW,CAACF,EAAG;gBAC/B6L,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,IAAI,CAAE;gBACjEiJ,IAAI,EAAC,GAAG;gBACR9C,SAAS,EAAC;cAA2G;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNnL,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAOoL,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAoB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5FnL,OAAA;gBACEwI,KAAK,EAAE/G,QAAQ,CAACM,WAAW,CAACD,EAAG;gBAC/B4L,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,IAAI,CAAE;gBACjEiJ,IAAI,EAAC,GAAG;gBACR9C,SAAS,EAAC;cAA2G;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNnL,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAOoL,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAwB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChGnL,OAAA;gBACEwI,KAAK,EAAE/G,QAAQ,CAACO,aAAa,CAACH,EAAG;gBACjC6L,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,CAAE;gBACnEiJ,IAAI,EAAC,GAAG;gBACR9C,SAAS,EAAC;cAA2G;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNnL,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAOoL,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAuB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/FnL,OAAA;gBACEwI,KAAK,EAAE/G,QAAQ,CAACO,aAAa,CAACF,EAAG;gBACjC4L,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,CAAE;gBACnEiJ,IAAI,EAAC,GAAG;gBACR9C,SAAS,EAAC;cAA2G;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNnL,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAOoL,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAoB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5FnL,OAAA;gBACEwI,KAAK,EAAE/G,QAAQ,CAACQ,UAAU,CAACJ,EAAG;gBAC9B6L,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,CAAE;gBAChEiJ,IAAI,EAAC,GAAG;gBACR9C,SAAS,EAAC;cAA2G;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNnL,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAOoL,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAmB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3FnL,OAAA;gBACEwI,KAAK,EAAE/G,QAAQ,CAACQ,UAAU,CAACH,EAAG;gBAC9B4L,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,CAAE;gBAChEiJ,IAAI,EAAC,GAAG;gBACR9C,SAAS,EAAC;cAA2G;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNnL,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAOoL,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAiB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzFnL,OAAA;gBACEwI,KAAK,EAAE/G,QAAQ,CAACS,OAAO,CAACL,EAAG;gBAC3B6L,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAE;gBAC7DiJ,IAAI,EAAC,GAAG;gBACR9C,SAAS,EAAC;cAA2G;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNnL,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAOoL,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAgB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxFnL,OAAA;gBACEwI,KAAK,EAAE/G,QAAQ,CAACS,OAAO,CAACJ,EAAG;gBAC3B4L,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAE;gBAC7DiJ,IAAI,EAAC,GAAG;gBACR9C,SAAS,EAAC;cAA2G;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNnL,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAIoL,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAkB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAC/E1J,QAAQ,CAACU,iBAAiB,CAACiF,GAAG,CAAC,CAACG,OAAO,EAAEqB,KAAK,kBAC7C5I,OAAA;gBAAiBoL,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,gBACrErL,OAAA;kBAAKoL,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDrL,OAAA;oBAAAqL,QAAA,gBACErL,OAAA;sBAAOoL,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC1FnL,OAAA;sBACEyN,IAAI,EAAC,MAAM;sBACXjF,KAAK,EAAEjB,OAAO,CAAC1F,EAAG;sBAClB6L,QAAQ,EAAGzI,CAAC,IAAK0D,mBAAmB,CAACC,KAAK,EAAE,IAAI,EAAE3D,CAAC,CAACwD,MAAM,CAACD,KAAK,CAAE;sBAClE4C,SAAS,EAAC,2GAA2G;sBACrHuC,QAAQ;oBAAA;sBAAA3C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNnL,OAAA;oBAAAqL,QAAA,gBACErL,OAAA;sBAAOoL,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACzFnL,OAAA;sBACEyN,IAAI,EAAC,MAAM;sBACXjF,KAAK,EAAEjB,OAAO,CAACzF,EAAG;sBAClB4L,QAAQ,EAAGzI,CAAC,IAAK0D,mBAAmB,CAACC,KAAK,EAAE,IAAI,EAAE3D,CAAC,CAACwD,MAAM,CAACD,KAAK,CAAE;sBAClE4C,SAAS,EAAC,2GAA2G;sBACrHuC,QAAQ;oBAAA;sBAAA3C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EACL1J,QAAQ,CAACU,iBAAiB,CAAC8E,MAAM,GAAG,CAAC,iBACpCjH,OAAA;kBACEyN,IAAI,EAAC,QAAQ;kBACbnB,OAAO,EAAEA,CAAA,KAAMvD,aAAa,CAACH,KAAK,CAAE;kBACpCwC,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,EACrE;gBAED;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA,GA/BOvC,KAAK;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgCV,CACN,CAAC,eACFnL,OAAA;gBACEyN,IAAI,EAAC,QAAQ;gBACbnB,OAAO,EAAExD,UAAW;gBACpBsC,SAAS,EAAC,4DAA4D;gBAAAC,QAAA,EACvE;cAED;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNnL,OAAA;cAAKoL,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDrL,OAAA;gBAAAqL,QAAA,gBACErL,OAAA;kBAAOoL,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChFnL,OAAA;kBACEyN,IAAI,EAAC,KAAK;kBACVjF,KAAK,EAAE/G,QAAQ,CAACyB,IAAK;kBACrBwK,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,MAAM,CAAE;kBAC9CmG,SAAS,EAAC;gBAA2G;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnL,OAAA;gBAAAqL,QAAA,gBACErL,OAAA;kBAAOoL,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAS;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjFnL,OAAA;kBACEyN,IAAI,EAAC,KAAK;kBACVjF,KAAK,EAAE/G,QAAQ,CAAC0B,KAAM;kBACtBuK,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,OAAO,CAAE;kBAC/CmG,SAAS,EAAC;gBAA2G;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnL,OAAA;gBAAAqL,QAAA,gBACErL,OAAA;kBAAOoL,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAO;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/EnL,OAAA;kBACEyN,IAAI,EAAC,KAAK;kBACVjF,KAAK,EAAE/G,QAAQ,CAAC2B,MAAO;kBACvBsK,QAAQ,EAAGzI,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,EAAE,QAAQ,CAAE;kBAChDmG,SAAS,EAAC;gBAA2G;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnL,OAAA;cAAKoL,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9CrL,OAAA,CAACZ,MAAM,CAAC8M,MAAM;gBACZuB,IAAI,EAAC,QAAQ;gBACbnB,OAAO,EAAEA,CAAA,KAAMpL,sBAAsB,CAAC,KAAK,CAAE;gBAC7CkK,SAAS,EAAC,0GAA0G;gBACpHe,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAAAf,QAAA,EAC3B;cAED;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC,eAChBnL,OAAA,CAACZ,MAAM,CAAC8M,MAAM;gBACZuB,IAAI,EAAC,QAAQ;gBACbrC,SAAS,EAAC,0KAA0K;gBACpLe,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAAAf,QAAA,EAEzB9J,SAAS,GAAG,mBAAmB,GAAG;cAAgB;gBAAAyJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjL,EAAA,CAhhCID,YAAY;EAAA,QAwCCf,WAAW,EACJW,OAAO;AAAA;AAAAsO,EAAA,GAzC3BlO,YAAY;AAkhClB,eAAeA,YAAY;AAAC,IAAAkO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}