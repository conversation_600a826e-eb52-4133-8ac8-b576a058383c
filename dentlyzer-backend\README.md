# ODenta Backend API

Backend API for the ODenta dental management system built with Node.js, Express, and MongoDB.

## 🚀 Quick Start

### Prerequisites
- Node.js (v14 or higher)
- MongoDB (local or cloud)
- npm or yarn

### Local Development

1. **Clone and install dependencies:**
```bash
cd dentlyzer-backend
npm install
```

2. **Set up environment variables:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Start the development server:**
```bash
npm run dev
```

The server will start on `http://localhost:5000`

## 🌍 Environment Variables

### Required Variables
```env
# Database Configuration
MONGO_URI=mongodb://localhost:27017/dentlyzer

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
JWT_REFRESH_SECRET=your_refresh_secret_here

# Server Configuration
PORT=5000
NODE_ENV=development

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:3000
```

### Optional Variables
```env
# JWT Expiration
JWT_ACCESS_EXPIRATION=15m
JWT_REFRESH_EXPIRATION=7d

# File Upload Configuration
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=50mb

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# Cloudinary (for cloud storage)
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
```

## 🚀 Deployment

### Railway Deployment

1. **Create a Railway account** at [railway.app](https://railway.app)

2. **Connect your GitHub repository**

3. **Set environment variables in Railway:**
```env
MONGO_URI=mongodb+srv://username:<EMAIL>/dentlyzer
JWT_SECRET=your_production_jwt_secret
JWT_REFRESH_SECRET=your_production_refresh_secret
NODE_ENV=production
FRONTEND_URL=https://your-vercel-app.vercel.app
PORT=5000
```

4. **Deploy:** Railway will automatically deploy when you push to your main branch

### Other Deployment Options

#### Heroku
```bash
# Install Heroku CLI and login
heroku create your-app-name
heroku config:set MONGO_URI=your_mongodb_uri
heroku config:set JWT_SECRET=your_jwt_secret
# ... set other environment variables
git push heroku main
```

#### DigitalOcean App Platform
1. Connect your GitHub repository
2. Set environment variables in the control panel
3. Deploy

## 📁 Project Structure

```
dentlyzer-backend/
├── config/
│   ├── config.js          # Environment configuration
│   └── db.js              # Database connection
├── controllers/           # Route controllers
├── middleware/           # Custom middleware
├── models/              # MongoDB models
├── routes/              # API routes
├── uploads/             # File uploads directory
├── utils/               # Utility functions
├── .env                 # Environment variables
├── .env.example         # Environment template
├── server.js            # Main server file
└── package.json         # Dependencies and scripts
```

## 🔧 Available Scripts

- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm run seed` - Seed database with initial data
- `npm run seed:universities` - Seed universities
- `npm run seed:supervisors` - Seed supervisors

## 🔐 Security Features

- JWT authentication with refresh tokens
- Password hashing with bcrypt
- CORS configuration
- Input validation with Joi
- File upload restrictions
- Activity logging

## 📊 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile

### Users
- `GET /api/students` - Get students
- `GET /api/supervisors` - Get supervisors
- `GET /api/admin` - Admin endpoints

### Patients
- `GET /api/patients` - Get patients
- `POST /api/patients` - Create patient
- `PUT /api/patients/:id` - Update patient

### Appointments
- `GET /api/appointments` - Get appointments
- `POST /api/appointments` - Create appointment

### Reviews
- `GET /api/reviews` - Get reviews
- `POST /api/reviews` - Create review

### Universities
- `GET /api/universities` - Get universities
- `POST /api/universities` - Create university

## 🐛 Troubleshooting

### Common Issues

1. **MongoDB Connection Error**
   - Check your MONGO_URI
   - Ensure MongoDB is running
   - Check network connectivity

2. **JWT Token Issues**
   - Verify JWT_SECRET is set
   - Check token expiration
   - Ensure proper token format

3. **File Upload Issues**
   - Check UPLOAD_PATH permissions
   - Verify MAX_FILE_SIZE setting
   - Ensure uploads directory exists

4. **CORS Issues**
   - Verify FRONTEND_URL is correct
   - Check CORS configuration in server.js

## 📝 License

This project is licensed under the ISC License.
