{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\student\\\\MedicalTab.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport { motion } from 'framer-motion';\nimport { FaFileMedical, FaProcedures, FaUser } from 'react-icons/fa';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MedicalTab = ({\n  onSave\n}) => {\n  _s();\n  var _patientData$medicalI, _patientData$medicalI2, _patientData$medicalI3, _patientData$medicalI4, _patientData$medicalI5;\n  const {\n    nationalId\n  } = useParams();\n  const {\n    token\n  } = useAuth();\n  const [patientData, setPatientData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [isEditing, setIsEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    fullName: '',\n    nationalId: '',\n    phoneNumber: '',\n    gender: '',\n    age: '',\n    address: '',\n    occupation: '',\n    medicalInfo: {\n      chronicDiseases: [],\n      recentSurgicalProcedures: '',\n      currentMedications: '',\n      chiefComplaint: ''\n    }\n  });\n  useEffect(() => {\n    const fetchPatientData = async () => {\n      try {\n        var _patient$medicalInfo, _patient$medicalInfo2, _patient$medicalInfo3, _patient$medicalInfo4;\n        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/patients/public/${nationalId}`);\n        const patient = response.data;\n        setPatientData(patient);\n        setFormData({\n          fullName: patient.fullName || '',\n          nationalId: patient.nationalId || '',\n          phoneNumber: patient.phoneNumber || '',\n          gender: patient.gender || '',\n          age: patient.age || '',\n          address: patient.address || '',\n          occupation: patient.occupation || '',\n          medicalInfo: {\n            chronicDiseases: ((_patient$medicalInfo = patient.medicalInfo) === null || _patient$medicalInfo === void 0 ? void 0 : _patient$medicalInfo.chronicDiseases) || [],\n            recentSurgicalProcedures: ((_patient$medicalInfo2 = patient.medicalInfo) === null || _patient$medicalInfo2 === void 0 ? void 0 : _patient$medicalInfo2.recentSurgicalProcedures) || '',\n            currentMedications: ((_patient$medicalInfo3 = patient.medicalInfo) === null || _patient$medicalInfo3 === void 0 ? void 0 : _patient$medicalInfo3.currentMedications) || '',\n            chiefComplaint: ((_patient$medicalInfo4 = patient.medicalInfo) === null || _patient$medicalInfo4 === void 0 ? void 0 : _patient$medicalInfo4.chiefComplaint) || ''\n          }\n        });\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response4$data;\n        console.error('Fetch error:', (_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status, (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data);\n        const message = ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401 ? 'Unauthorized: Please log in again.' : ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.message) || 'Failed to load patient data';\n        setError(message);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchPatientData();\n  }, [nationalId, token]);\n  const handleEditToggle = () => {\n    setIsEditing(!isEditing);\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    if (name.startsWith('medical-')) {\n      const medicalField = name.replace('medical-', '');\n      setFormData(prev => ({\n        ...prev,\n        medicalInfo: {\n          ...prev.medicalInfo,\n          [medicalField]: value\n        }\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n  };\n  const handleChronicDiseaseChange = e => {\n    const {\n      value,\n      checked\n    } = e.target;\n    setFormData(prev => {\n      const updatedDiseases = checked ? [...prev.medicalInfo.chronicDiseases, value] : prev.medicalInfo.chronicDiseases.filter(disease => disease !== value);\n      return {\n        ...prev,\n        medicalInfo: {\n          ...prev.medicalInfo,\n          chronicDiseases: updatedDiseases\n        }\n      };\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      // Update the patient data with the new info\n      const response = await axios.put(`${process.env.REACT_APP_API_URL}/api/patients/${nationalId}`, {\n        fullName: formData.fullName,\n        phoneNumber: formData.phoneNumber,\n        gender: formData.gender,\n        age: formData.age,\n        address: formData.address,\n        occupation: formData.occupation,\n        medicalInfo: formData.medicalInfo,\n        drId: patientData.drId // Include drId as required by patientSchema\n      }, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (response.data && response.data.patient) {\n        setPatientData(response.data.patient);\n        setIsEditing(false);\n\n        // Call the onSave callback if provided\n        if (onSave) {\n          onSave(formData.medicalInfo);\n        }\n      } else {\n        throw new Error('Invalid patient data received from server');\n      }\n    } catch (err) {\n      var _err$response5, _err$response6, _err$response7, _err$response7$data;\n      console.error('Update error:', (_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : _err$response5.status, (_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : _err$response6.data);\n      setError(((_err$response7 = err.response) === null || _err$response7 === void 0 ? void 0 : (_err$response7$data = _err$response7.data) === null || _err$response7$data === void 0 ? void 0 : _err$response7$data.message) || 'Failed to update patient data');\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 bg-white rounded-lg shadow-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-pulse\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-6 bg-gray-200 rounded w-1/4 mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-4 bg-gray-200 rounded w-full mb-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-4 bg-gray-200 rounded w-full mb-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-4 bg-gray-200 rounded w-3/4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !patientData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 bg-white rounded-lg shadow-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-red-500\",\n        children: error || 'Patient data not found'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this);\n  }\n  const chronicDiseaseOptions = ['Diabetes', 'Hypertension', 'Heart Disease', 'Asthma', 'Thyroid Disorder', 'Kidney Disease', 'Liver Disease', 'Arthritis', 'Cancer', 'Other'];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full\",\n    children: isEditing ? /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0\n      },\n      animate: {\n        opacity: 1\n      },\n      className: \"bg-white rounded-lg p-6 shadow-sm\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-[#0077B6]\",\n          children: \"Edit Medical Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleEditToggle,\n          className: \"text-gray-500 hover:text-gray-700\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-6 w-6\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-[#0077B6] mb-4\",\n            children: \"Personal Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700\",\n                children: \"Full Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"fullName\",\n                value: formData.fullName,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700\",\n                children: \"National ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"nationalId\",\n                value: formData.nationalId,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                disabled: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700\",\n                children: \"Phone Number\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"phoneNumber\",\n                value: formData.phoneNumber,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700\",\n                children: \"Gender\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"gender\",\n                value: formData.gender,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Gender\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"male\",\n                  children: \"Male\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"female\",\n                  children: \"Female\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"other\",\n                  children: \"Other\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700\",\n                children: \"Age\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"age\",\n                value: formData.age,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700\",\n                children: \"Address\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"address\",\n                value: formData.address,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700\",\n                children: \"Occupation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"occupation\",\n                value: formData.occupation,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-[#0077B6] mb-4\",\n            children: \"Medical Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-md font-medium text-gray-700 mb-2\",\n              children: \"Chronic Diseases\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-2\",\n              children: chronicDiseaseOptions.map(disease => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  id: `disease-${disease}`,\n                  value: disease,\n                  checked: formData.medicalInfo.chronicDiseases.includes(disease),\n                  onChange: handleChronicDiseaseChange,\n                  className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: `disease-${disease}`,\n                  className: \"ml-2 text-sm text-gray-700\",\n                  children: disease\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 23\n                }, this)]\n              }, disease, true, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-md font-medium text-gray-700 mb-2\",\n              children: \"Recent Surgical Procedures\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              name: \"medical-recentSurgicalProcedures\",\n              value: formData.medicalInfo.recentSurgicalProcedures,\n              onChange: handleInputChange,\n              rows: \"3\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n              placeholder: \"List any recent surgical procedures\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-md font-medium text-gray-700 mb-2\",\n              children: \"Current Medications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              name: \"medical-currentMedications\",\n              value: formData.medicalInfo.currentMedications,\n              onChange: handleInputChange,\n              rows: \"3\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n              placeholder: \"List any current medications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-md font-medium text-gray-700 mb-2\",\n              children: \"Chief Complaint\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              name: \"medical-chiefComplaint\",\n              value: formData.medicalInfo.chiefComplaint,\n              onChange: handleInputChange,\n              rows: \"3\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n              placeholder: \"Describe the patient's main complaint or reason for visit\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end space-x-4 pt-4\",\n          children: [/*#__PURE__*/_jsxDEV(motion.button, {\n            type: \"button\",\n            onClick: handleEditToggle,\n            className: \"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\",\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n            type: \"submit\",\n            className: \"px-4 py-2 bg-[#0077B6] text-white rounded-lg hover:bg-blue-700 font-medium transition-colors\",\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: \"Save Changes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0\n      },\n      animate: {\n        opacity: 1\n      },\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-[#0077B6] flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(FaUser, {\n              className: \"h-5 w-5 mr-2 text-[#0077B6]\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this), \"Personal Information\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleEditToggle,\n            className: \"px-3 py-1 text-sm bg-[#0077B6] text-white rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"Edit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Full Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-900\",\n              children: patientData.fullName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"National ID\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-900\",\n              children: patientData.nationalId\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-900\",\n              children: patientData.phoneNumber\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Gender\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-900\",\n              children: patientData.gender\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Age\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-900\",\n              children: patientData.age\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-900\",\n              children: patientData.address || 'Not provided'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Occupation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-900\",\n              children: patientData.occupation || 'Not provided'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-[#0077B6] flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(FaFileMedical, {\n              className: \"h-5 w-5 mr-2 text-[#0077B6]\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 17\n            }, this), \"Medical History\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Chronic Diseases\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-900\",\n              children: ((_patientData$medicalI = patientData.medicalInfo) === null || _patientData$medicalI === void 0 ? void 0 : (_patientData$medicalI2 = _patientData$medicalI.chronicDiseases) === null || _patientData$medicalI2 === void 0 ? void 0 : _patientData$medicalI2.length) > 0 ? patientData.medicalInfo.chronicDiseases.join(', ') : 'None'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Recent Surgical Procedures\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-900\",\n              children: ((_patientData$medicalI3 = patientData.medicalInfo) === null || _patientData$medicalI3 === void 0 ? void 0 : _patientData$medicalI3.recentSurgicalProcedures) || 'None'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Current Medications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-900\",\n              children: ((_patientData$medicalI4 = patientData.medicalInfo) === null || _patientData$medicalI4 === void 0 ? void 0 : _patientData$medicalI4.currentMedications) || 'None'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-[#0077B6] mb-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(FaProcedures, {\n            className: \"h-5 w-5 mr-2 text-[#0077B6]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this), \"Chief Complaint\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-900\",\n          children: ((_patientData$medicalI5 = patientData.medicalInfo) === null || _patientData$medicalI5 === void 0 ? void 0 : _patientData$medicalI5.chiefComplaint) || 'None'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 172,\n    columnNumber: 5\n  }, this);\n};\n_s(MedicalTab, \"dEWJ8/67Vpt01C2/7/j2UQSzfqg=\", false, function () {\n  return [useParams, useAuth];\n});\n_c = MedicalTab;\nexport default MedicalTab;\nvar _c;\n$RefreshReg$(_c, \"MedicalTab\");", "map": {"version": 3, "names": ["useState", "useEffect", "useParams", "axios", "motion", "FaFileMedical", "FaProcedures", "FaUser", "useAuth", "jsxDEV", "_jsxDEV", "MedicalTab", "onSave", "_s", "_patientData$medicalI", "_patientData$medicalI2", "_patientData$medicalI3", "_patientData$medicalI4", "_patientData$medicalI5", "nationalId", "token", "patientData", "setPatientData", "loading", "setLoading", "error", "setError", "isEditing", "setIsEditing", "formData", "setFormData", "fullName", "phoneNumber", "gender", "age", "address", "occupation", "medicalInfo", "chronicDiseases", "recentSurgicalProcedures", "currentMedications", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fetchPatientData", "_patient$medicalInfo", "_patient$medicalInfo2", "_patient$medicalInfo3", "_patient$medicalInfo4", "response", "get", "process", "env", "REACT_APP_API_URL", "patient", "data", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response4$data", "console", "status", "message", "handleEditToggle", "handleInputChange", "e", "name", "value", "target", "startsWith", "medicalField", "replace", "prev", "handleChronicDiseaseChange", "checked", "updatedDiseases", "filter", "disease", "handleSubmit", "preventDefault", "put", "drId", "headers", "Authorization", "Error", "_err$response5", "_err$response6", "_err$response7", "_err$response7$data", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "chronicDiseaseOptions", "div", "initial", "opacity", "animate", "onClick", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onSubmit", "type", "onChange", "disabled", "map", "id", "includes", "htmlFor", "rows", "placeholder", "required", "button", "whileHover", "scale", "whileTap", "length", "join", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/student/MedicalTab.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport { motion } from 'framer-motion';\nimport { FaFileMedical, FaProcedures, FaUser } from 'react-icons/fa';\nimport { useAuth } from '../context/AuthContext';\n\nconst MedicalTab = ({ onSave }) => {\n  const { nationalId } = useParams();\n  const { token } = useAuth();\n  const [patientData, setPatientData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [isEditing, setIsEditing] = useState(false);\n\n  const [formData, setFormData] = useState({\n    fullName: '',\n    nationalId: '',\n    phoneNumber: '',\n    gender: '',\n    age: '',\n    address: '',\n    occupation: '',\n    medicalInfo: {\n      chronicDiseases: [],\n      recentSurgicalProcedures: '',\n      currentMedications: '',\n      chiefComplaint: '',\n    },\n  });\n\n  useEffect(() => {\n    const fetchPatientData = async () => {\n      try {\n        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/patients/public/${nationalId}`);\n        const patient = response.data;\n        setPatientData(patient);\n        setFormData({\n          fullName: patient.fullName || '',\n          nationalId: patient.nationalId || '',\n          phoneNumber: patient.phoneNumber || '',\n          gender: patient.gender || '',\n          age: patient.age || '',\n          address: patient.address || '',\n          occupation: patient.occupation || '',\n          medicalInfo: {\n            chronicDiseases: patient.medicalInfo?.chronicDiseases || [],\n            recentSurgicalProcedures: patient.medicalInfo?.recentSurgicalProcedures || '',\n            currentMedications: patient.medicalInfo?.currentMedications || '',\n            chiefComplaint: patient.medicalInfo?.chiefComplaint || '',\n          },\n        });\n      } catch (err) {\n        console.error('Fetch error:', err.response?.status, err.response?.data);\n        const message = err.response?.status === 401\n          ? 'Unauthorized: Please log in again.'\n          : err.response?.data?.message || 'Failed to load patient data';\n        setError(message);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchPatientData();\n  }, [nationalId, token]);\n\n  const handleEditToggle = () => {\n    setIsEditing(!isEditing);\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    if (name.startsWith('medical-')) {\n      const medicalField = name.replace('medical-', '');\n      setFormData(prev => ({\n        ...prev,\n        medicalInfo: { ...prev.medicalInfo, [medicalField]: value },\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value,\n      }));\n    }\n  };\n\n  const handleChronicDiseaseChange = (e) => {\n    const { value, checked } = e.target;\n    setFormData(prev => {\n      const updatedDiseases = checked\n        ? [...prev.medicalInfo.chronicDiseases, value]\n        : prev.medicalInfo.chronicDiseases.filter(disease => disease !== value);\n\n      return {\n        ...prev,\n        medicalInfo: { ...prev.medicalInfo, chronicDiseases: updatedDiseases },\n      };\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      // Update the patient data with the new info\n      const response = await axios.put(\n        `${process.env.REACT_APP_API_URL}/api/patients/${nationalId}`,\n        {\n          fullName: formData.fullName,\n          phoneNumber: formData.phoneNumber,\n          gender: formData.gender,\n          age: formData.age,\n          address: formData.address,\n          occupation: formData.occupation,\n          medicalInfo: formData.medicalInfo,\n          drId: patientData.drId // Include drId as required by patientSchema\n        },\n        { headers: { Authorization: `Bearer ${token}` } }\n      );\n\n      if (response.data && response.data.patient) {\n        setPatientData(response.data.patient);\n        setIsEditing(false);\n\n        // Call the onSave callback if provided\n        if (onSave) {\n          onSave(formData.medicalInfo);\n        }\n      } else {\n        throw new Error('Invalid patient data received from server');\n      }\n    } catch (err) {\n      console.error('Update error:', err.response?.status, err.response?.data);\n      setError(err.response?.data?.message || 'Failed to update patient data');\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"p-4 bg-white rounded-lg shadow-sm\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-6 bg-gray-200 rounded w-1/4 mb-4\"></div>\n          <div className=\"h-4 bg-gray-200 rounded w-full mb-2\"></div>\n          <div className=\"h-4 bg-gray-200 rounded w-full mb-2\"></div>\n          <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !patientData) {\n    return (\n      <div className=\"p-4 bg-white rounded-lg shadow-sm\">\n        <div className=\"text-red-500\">{error || 'Patient data not found'}</div>\n      </div>\n    );\n  }\n\n  const chronicDiseaseOptions = [\n    'Diabetes',\n    'Hypertension',\n    'Heart Disease',\n    'Asthma',\n    'Thyroid Disorder',\n    'Kidney Disease',\n    'Liver Disease',\n    'Arthritis',\n    'Cancer',\n    'Other'\n  ];\n\n  return (\n    <div className=\"w-full\">\n      {isEditing ? (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"bg-white rounded-lg p-6 shadow-sm\"\n        >\n          <div className=\"flex justify-between items-center mb-6\">\n            <h2 className=\"text-xl font-semibold text-[#0077B6]\">Edit Medical Information</h2>\n            <button\n              onClick={handleEditToggle}\n              className=\"text-gray-500 hover:text-gray-700\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                className=\"h-6 w-6\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M6 18L18 6M6 6l12 12\"\n                />\n              </svg>\n            </button>\n          </div>\n\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            <div>\n              <h3 className=\"text-lg font-semibold text-[#0077B6] mb-4\">Personal Information</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Full Name</label>\n                  <input\n                    type=\"text\"\n                    name=\"fullName\"\n                    value={formData.fullName}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">National ID</label>\n                  <input\n                    type=\"text\"\n                    name=\"nationalId\"\n                    value={formData.nationalId}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    disabled\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Phone Number</label>\n                  <input\n                    type=\"text\"\n                    name=\"phoneNumber\"\n                    value={formData.phoneNumber}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Gender</label>\n                  <select\n                    name=\"gender\"\n                    value={formData.gender}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  >\n                    <option value=\"\">Select Gender</option>\n                    <option value=\"male\">Male</option>\n                    <option value=\"female\">Female</option>\n                    <option value=\"other\">Other</option>\n                  </select>\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Age</label>\n                  <input\n                    type=\"number\"\n                    name=\"age\"\n                    value={formData.age}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Address</label>\n                  <input\n                    type=\"text\"\n                    name=\"address\"\n                    value={formData.address}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Occupation</label>\n                  <input\n                    type=\"text\"\n                    name=\"occupation\"\n                    value={formData.occupation}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            <div>\n              <h3 className=\"text-lg font-semibold text-[#0077B6] mb-4\">Medical Information</h3>\n              <div>\n                <h4 className=\"text-md font-medium text-gray-700 mb-2\">Chronic Diseases</h4>\n                <div className=\"grid grid-cols-2 gap-2\">\n                  {chronicDiseaseOptions.map(disease => (\n                    <div key={disease} className=\"flex items-center\">\n                      <input\n                        type=\"checkbox\"\n                        id={`disease-${disease}`}\n                        value={disease}\n                        checked={formData.medicalInfo.chronicDiseases.includes(disease)}\n                        onChange={handleChronicDiseaseChange}\n                        className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                      />\n                      <label htmlFor={`disease-${disease}`} className=\"ml-2 text-sm text-gray-700\">\n                        {disease}\n                      </label>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              <div className=\"mt-4\">\n                <h4 className=\"text-md font-medium text-gray-700 mb-2\">Recent Surgical Procedures</h4>\n                <textarea\n                  name=\"medical-recentSurgicalProcedures\"\n                  value={formData.medicalInfo.recentSurgicalProcedures}\n                  onChange={handleInputChange}\n                  rows=\"3\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"List any recent surgical procedures\"\n                />\n              </div>\n\n              <div className=\"mt-4\">\n                <h4 className=\"text-md font-medium text-gray-700 mb-2\">Current Medications</h4>\n                <textarea\n                  name=\"medical-currentMedications\"\n                  value={formData.medicalInfo.currentMedications}\n                  onChange={handleInputChange}\n                  rows=\"3\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"List any current medications\"\n                />\n              </div>\n\n              <div className=\"mt-4\">\n                <h4 className=\"text-md font-medium text-gray-700 mb-2\">Chief Complaint</h4>\n                <textarea\n                  name=\"medical-chiefComplaint\"\n                  value={formData.medicalInfo.chiefComplaint}\n                  onChange={handleInputChange}\n                  rows=\"3\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Describe the patient's main complaint or reason for visit\"\n                  required\n                />\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-4 pt-4\">\n              <motion.button\n                type=\"button\"\n                onClick={handleEditToggle}\n                className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                Cancel\n              </motion.button>\n              <motion.button\n                type=\"submit\"\n                className=\"px-4 py-2 bg-[#0077B6] text-white rounded-lg hover:bg-blue-700 font-medium transition-colors\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                Save Changes\n              </motion.button>\n            </div>\n          </form>\n        </motion.div>\n      ) : (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"space-y-6\"\n        >\n          <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]\">\n            <div className=\"flex justify-between items-center mb-4\">\n              <h2 className=\"text-lg font-semibold text-[#0077B6] flex items-center\">\n                <FaUser className=\"h-5 w-5 mr-2 text-[#0077B6]\" />\n                Personal Information\n              </h2>\n              <button\n                onClick={handleEditToggle}\n                className=\"px-3 py-1 text-sm bg-[#0077B6] text-white rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                Edit\n              </button>\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <h3 className=\"text-sm font-medium text-gray-500\">Full Name</h3>\n                <p className=\"mt-1 text-sm text-gray-900\">{patientData.fullName}</p>\n              </div>\n              <div>\n                <h3 className=\"text-sm font-medium text-gray-500\">National ID</h3>\n                <p className=\"mt-1 text-sm text-gray-900\">{patientData.nationalId}</p>\n              </div>\n              <div>\n                <h3 className=\"text-sm font-medium text-gray-500\">Phone Number</h3>\n                <p className=\"mt-1 text-sm text-gray-900\">{patientData.phoneNumber}</p>\n              </div>\n              <div>\n                <h3 className=\"text-sm font-medium text-gray-500\">Gender</h3>\n                <p className=\"mt-1 text-sm text-gray-900\">{patientData.gender}</p>\n              </div>\n              <div>\n                <h3 className=\"text-sm font-medium text-gray-500\">Age</h3>\n                <p className=\"mt-1 text-sm text-gray-900\">{patientData.age}</p>\n              </div>\n              <div>\n                <h3 className=\"text-sm font-medium text-gray-500\">Address</h3>\n                <p className=\"mt-1 text-sm text-gray-900\">{patientData.address || 'Not provided'}</p>\n              </div>\n              <div>\n                <h3 className=\"text-sm font-medium text-gray-500\">Occupation</h3>\n                <p className=\"mt-1 text-sm text-gray-900\">{patientData.occupation || 'Not provided'}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]\">\n            <div className=\"flex justify-between items-center mb-4\">\n              <h2 className=\"text-lg font-semibold text-[#0077B6] flex items-center\">\n                <FaFileMedical className=\"h-5 w-5 mr-2 text-[#0077B6]\" />\n                Medical History\n              </h2>\n            </div>\n            <div className=\"space-y-4\">\n              <div>\n                <h3 className=\"text-sm font-medium text-gray-500\">Chronic Diseases</h3>\n                <p className=\"mt-1 text-sm text-gray-900\">\n                  {patientData.medicalInfo?.chronicDiseases?.length > 0\n                    ? patientData.medicalInfo.chronicDiseases.join(', ')\n                    : 'None'}\n                </p>\n              </div>\n              <div>\n                <h3 className=\"text-sm font-medium text-gray-500\">Recent Surgical Procedures</h3>\n                <p className=\"mt-1 text-sm text-gray-900\">\n                  {patientData.medicalInfo?.recentSurgicalProcedures || 'None'}\n                </p>\n              </div>\n              <div>\n                <h3 className=\"text-sm font-medium text-gray-500\">Current Medications</h3>\n                <p className=\"mt-1 text-sm text-gray-900\">\n                  {patientData.medicalInfo?.currentMedications || 'None'}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]\">\n            <h2 className=\"text-lg font-semibold text-[#0077B6] mb-4 flex items-center\">\n              <FaProcedures className=\"h-5 w-5 mr-2 text-[#0077B6]\" />\n              Chief Complaint\n            </h2>\n            <p className=\"text-sm text-gray-900\">\n              {patientData.medicalInfo?.chiefComplaint || 'None'}\n            </p>\n          </div>\n        </motion.div>\n      )}\n    </div>\n  );\n};\n\nexport default MedicalTab;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,aAAa,EAAEC,YAAY,EAAEC,MAAM,QAAQ,gBAAgB;AACpE,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,UAAU,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACjC,MAAM;IAAEC;EAAW,CAAC,GAAGjB,SAAS,CAAC,CAAC;EAClC,MAAM;IAAEkB;EAAM,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAC3B,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC;IACvC+B,QAAQ,EAAE,EAAE;IACZZ,UAAU,EAAE,EAAE;IACda,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,EAAE;IACVC,GAAG,EAAE,EAAE;IACPC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE;MACXC,eAAe,EAAE,EAAE;MACnBC,wBAAwB,EAAE,EAAE;MAC5BC,kBAAkB,EAAE,EAAE;MACtBC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFxC,SAAS,CAAC,MAAM;IACd,MAAMyC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QAAA,IAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;QACF,MAAMC,QAAQ,GAAG,MAAM5C,KAAK,CAAC6C,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,wBAAwBhC,UAAU,EAAE,CAAC;QACtG,MAAMiC,OAAO,GAAGL,QAAQ,CAACM,IAAI;QAC7B/B,cAAc,CAAC8B,OAAO,CAAC;QACvBtB,WAAW,CAAC;UACVC,QAAQ,EAAEqB,OAAO,CAACrB,QAAQ,IAAI,EAAE;UAChCZ,UAAU,EAAEiC,OAAO,CAACjC,UAAU,IAAI,EAAE;UACpCa,WAAW,EAAEoB,OAAO,CAACpB,WAAW,IAAI,EAAE;UACtCC,MAAM,EAAEmB,OAAO,CAACnB,MAAM,IAAI,EAAE;UAC5BC,GAAG,EAAEkB,OAAO,CAAClB,GAAG,IAAI,EAAE;UACtBC,OAAO,EAAEiB,OAAO,CAACjB,OAAO,IAAI,EAAE;UAC9BC,UAAU,EAAEgB,OAAO,CAAChB,UAAU,IAAI,EAAE;UACpCC,WAAW,EAAE;YACXC,eAAe,EAAE,EAAAK,oBAAA,GAAAS,OAAO,CAACf,WAAW,cAAAM,oBAAA,uBAAnBA,oBAAA,CAAqBL,eAAe,KAAI,EAAE;YAC3DC,wBAAwB,EAAE,EAAAK,qBAAA,GAAAQ,OAAO,CAACf,WAAW,cAAAO,qBAAA,uBAAnBA,qBAAA,CAAqBL,wBAAwB,KAAI,EAAE;YAC7EC,kBAAkB,EAAE,EAAAK,qBAAA,GAAAO,OAAO,CAACf,WAAW,cAAAQ,qBAAA,uBAAnBA,qBAAA,CAAqBL,kBAAkB,KAAI,EAAE;YACjEC,cAAc,EAAE,EAAAK,qBAAA,GAAAM,OAAO,CAACf,WAAW,cAAAS,qBAAA,uBAAnBA,qBAAA,CAAqBL,cAAc,KAAI;UACzD;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOa,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZC,OAAO,CAACnC,KAAK,CAAC,cAAc,GAAA8B,aAAA,GAAED,GAAG,CAACP,QAAQ,cAAAQ,aAAA,uBAAZA,aAAA,CAAcM,MAAM,GAAAL,cAAA,GAAEF,GAAG,CAACP,QAAQ,cAAAS,cAAA,uBAAZA,cAAA,CAAcH,IAAI,CAAC;QACvE,MAAMS,OAAO,GAAG,EAAAL,cAAA,GAAAH,GAAG,CAACP,QAAQ,cAAAU,cAAA,uBAAZA,cAAA,CAAcI,MAAM,MAAK,GAAG,GACxC,oCAAoC,GACpC,EAAAH,cAAA,GAAAJ,GAAG,CAACP,QAAQ,cAAAW,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcL,IAAI,cAAAM,mBAAA,uBAAlBA,mBAAA,CAAoBG,OAAO,KAAI,6BAA6B;QAChEpC,QAAQ,CAACoC,OAAO,CAAC;MACnB,CAAC,SAAS;QACRtC,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDkB,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACvB,UAAU,EAAEC,KAAK,CAAC,CAAC;EAEvB,MAAM2C,gBAAgB,GAAGA,CAAA,KAAM;IAC7BnC,YAAY,CAAC,CAACD,SAAS,CAAC;EAC1B,CAAC;EAED,MAAMqC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC,IAAIF,IAAI,CAACG,UAAU,CAAC,UAAU,CAAC,EAAE;MAC/B,MAAMC,YAAY,GAAGJ,IAAI,CAACK,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;MACjDzC,WAAW,CAAC0C,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPnC,WAAW,EAAE;UAAE,GAAGmC,IAAI,CAACnC,WAAW;UAAE,CAACiC,YAAY,GAAGH;QAAM;MAC5D,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLrC,WAAW,CAAC0C,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACN,IAAI,GAAGC;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMM,0BAA0B,GAAIR,CAAC,IAAK;IACxC,MAAM;MAAEE,KAAK;MAAEO;IAAQ,CAAC,GAAGT,CAAC,CAACG,MAAM;IACnCtC,WAAW,CAAC0C,IAAI,IAAI;MAClB,MAAMG,eAAe,GAAGD,OAAO,GAC3B,CAAC,GAAGF,IAAI,CAACnC,WAAW,CAACC,eAAe,EAAE6B,KAAK,CAAC,GAC5CK,IAAI,CAACnC,WAAW,CAACC,eAAe,CAACsC,MAAM,CAACC,OAAO,IAAIA,OAAO,KAAKV,KAAK,CAAC;MAEzE,OAAO;QACL,GAAGK,IAAI;QACPnC,WAAW,EAAE;UAAE,GAAGmC,IAAI,CAACnC,WAAW;UAAEC,eAAe,EAAEqC;QAAgB;MACvE,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOb,CAAC,IAAK;IAChCA,CAAC,CAACc,cAAc,CAAC,CAAC;IAClB,IAAI;MACF;MACA,MAAMhC,QAAQ,GAAG,MAAM5C,KAAK,CAAC6E,GAAG,CAC9B,GAAG/B,OAAO,CAACC,GAAG,CAACC,iBAAiB,iBAAiBhC,UAAU,EAAE,EAC7D;QACEY,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;QAC3BC,WAAW,EAAEH,QAAQ,CAACG,WAAW;QACjCC,MAAM,EAAEJ,QAAQ,CAACI,MAAM;QACvBC,GAAG,EAAEL,QAAQ,CAACK,GAAG;QACjBC,OAAO,EAAEN,QAAQ,CAACM,OAAO;QACzBC,UAAU,EAAEP,QAAQ,CAACO,UAAU;QAC/BC,WAAW,EAAER,QAAQ,CAACQ,WAAW;QACjC4C,IAAI,EAAE5D,WAAW,CAAC4D,IAAI,CAAC;MACzB,CAAC,EACD;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAU/D,KAAK;QAAG;MAAE,CAClD,CAAC;MAED,IAAI2B,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACD,OAAO,EAAE;QAC1C9B,cAAc,CAACyB,QAAQ,CAACM,IAAI,CAACD,OAAO,CAAC;QACrCxB,YAAY,CAAC,KAAK,CAAC;;QAEnB;QACA,IAAIhB,MAAM,EAAE;UACVA,MAAM,CAACiB,QAAQ,CAACQ,WAAW,CAAC;QAC9B;MACF,CAAC,MAAM;QACL,MAAM,IAAI+C,KAAK,CAAC,2CAA2C,CAAC;MAC9D;IACF,CAAC,CAAC,OAAO9B,GAAG,EAAE;MAAA,IAAA+B,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;MACZ5B,OAAO,CAACnC,KAAK,CAAC,eAAe,GAAA4D,cAAA,GAAE/B,GAAG,CAACP,QAAQ,cAAAsC,cAAA,uBAAZA,cAAA,CAAcxB,MAAM,GAAAyB,cAAA,GAAEhC,GAAG,CAACP,QAAQ,cAAAuC,cAAA,uBAAZA,cAAA,CAAcjC,IAAI,CAAC;MACxE3B,QAAQ,CAAC,EAAA6D,cAAA,GAAAjC,GAAG,CAACP,QAAQ,cAAAwC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAclC,IAAI,cAAAmC,mBAAA,uBAAlBA,mBAAA,CAAoB1B,OAAO,KAAI,+BAA+B,CAAC;IAC1E;EACF,CAAC;EAED,IAAIvC,OAAO,EAAE;IACX,oBACEb,OAAA;MAAK+E,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAChDhF,OAAA;QAAK+E,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BhF,OAAA;UAAK+E,SAAS,EAAC;QAAoC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1DpF,OAAA;UAAK+E,SAAS,EAAC;QAAqC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3DpF,OAAA;UAAK+E,SAAS,EAAC;QAAqC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3DpF,OAAA;UAAK+E,SAAS,EAAC;QAA+B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIrE,KAAK,IAAI,CAACJ,WAAW,EAAE;IACzB,oBACEX,OAAA;MAAK+E,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAChDhF,OAAA;QAAK+E,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAEjE,KAAK,IAAI;MAAwB;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE,CAAC;EAEV;EAEA,MAAMC,qBAAqB,GAAG,CAC5B,UAAU,EACV,cAAc,EACd,eAAe,EACf,QAAQ,EACR,kBAAkB,EAClB,gBAAgB,EAChB,eAAe,EACf,WAAW,EACX,QAAQ,EACR,OAAO,CACR;EAED,oBACErF,OAAA;IAAK+E,SAAS,EAAC,QAAQ;IAAAC,QAAA,EACpB/D,SAAS,gBACRjB,OAAA,CAACN,MAAM,CAAC4F,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAE;QAAED,OAAO,EAAE;MAAE,CAAE;MACxBT,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAE7ChF,OAAA;QAAK+E,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDhF,OAAA;UAAI+E,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClFpF,OAAA;UACE0F,OAAO,EAAErC,gBAAiB;UAC1B0B,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7ChF,OAAA;YACE2F,KAAK,EAAC,4BAA4B;YAClCZ,SAAS,EAAC,SAAS;YACnBa,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnBC,MAAM,EAAC,cAAc;YAAAd,QAAA,eAErBhF,OAAA;cACE+F,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,WAAW,EAAE,CAAE;cACfC,CAAC,EAAC;YAAsB;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENpF,OAAA;QAAMmG,QAAQ,EAAE/B,YAAa;QAACW,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACjDhF,OAAA;UAAAgF,QAAA,gBACEhF,OAAA;YAAI+E,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnFpF,OAAA;YAAK+E,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDhF,OAAA;cAAAgF,QAAA,gBACEhF,OAAA;gBAAO+E,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5EpF,OAAA;gBACEoG,IAAI,EAAC,MAAM;gBACX5C,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAEtC,QAAQ,CAACE,QAAS;gBACzBgF,QAAQ,EAAE/C,iBAAkB;gBAC5ByB,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpF,OAAA;cAAAgF,QAAA,gBACEhF,OAAA;gBAAO+E,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9EpF,OAAA;gBACEoG,IAAI,EAAC,MAAM;gBACX5C,IAAI,EAAC,YAAY;gBACjBC,KAAK,EAAEtC,QAAQ,CAACV,UAAW;gBAC3B4F,QAAQ,EAAE/C,iBAAkB;gBAC5ByB,SAAS,EAAC,2GAA2G;gBACrHuB,QAAQ;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpF,OAAA;cAAAgF,QAAA,gBACEhF,OAAA;gBAAO+E,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/EpF,OAAA;gBACEoG,IAAI,EAAC,MAAM;gBACX5C,IAAI,EAAC,aAAa;gBAClBC,KAAK,EAAEtC,QAAQ,CAACG,WAAY;gBAC5B+E,QAAQ,EAAE/C,iBAAkB;gBAC5ByB,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpF,OAAA;cAAAgF,QAAA,gBACEhF,OAAA;gBAAO+E,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzEpF,OAAA;gBACEwD,IAAI,EAAC,QAAQ;gBACbC,KAAK,EAAEtC,QAAQ,CAACI,MAAO;gBACvB8E,QAAQ,EAAE/C,iBAAkB;gBAC5ByB,SAAS,EAAC,2GAA2G;gBAAAC,QAAA,gBAErHhF,OAAA;kBAAQyD,KAAK,EAAC,EAAE;kBAAAuB,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCpF,OAAA;kBAAQyD,KAAK,EAAC,MAAM;kBAAAuB,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCpF,OAAA;kBAAQyD,KAAK,EAAC,QAAQ;kBAAAuB,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCpF,OAAA;kBAAQyD,KAAK,EAAC,OAAO;kBAAAuB,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNpF,OAAA;cAAAgF,QAAA,gBACEhF,OAAA;gBAAO+E,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtEpF,OAAA;gBACEoG,IAAI,EAAC,QAAQ;gBACb5C,IAAI,EAAC,KAAK;gBACVC,KAAK,EAAEtC,QAAQ,CAACK,GAAI;gBACpB6E,QAAQ,EAAE/C,iBAAkB;gBAC5ByB,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpF,OAAA;cAAAgF,QAAA,gBACEhF,OAAA;gBAAO+E,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1EpF,OAAA;gBACEoG,IAAI,EAAC,MAAM;gBACX5C,IAAI,EAAC,SAAS;gBACdC,KAAK,EAAEtC,QAAQ,CAACM,OAAQ;gBACxB4E,QAAQ,EAAE/C,iBAAkB;gBAC5ByB,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpF,OAAA;cAAAgF,QAAA,gBACEhF,OAAA;gBAAO+E,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7EpF,OAAA;gBACEoG,IAAI,EAAC,MAAM;gBACX5C,IAAI,EAAC,YAAY;gBACjBC,KAAK,EAAEtC,QAAQ,CAACO,UAAW;gBAC3B2E,QAAQ,EAAE/C,iBAAkB;gBAC5ByB,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpF,OAAA;UAAAgF,QAAA,gBACEhF,OAAA;YAAI+E,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClFpF,OAAA;YAAAgF,QAAA,gBACEhF,OAAA;cAAI+E,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5EpF,OAAA;cAAK+E,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EACpCK,qBAAqB,CAACkB,GAAG,CAACpC,OAAO,iBAChCnE,OAAA;gBAAmB+E,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC9ChF,OAAA;kBACEoG,IAAI,EAAC,UAAU;kBACfI,EAAE,EAAE,WAAWrC,OAAO,EAAG;kBACzBV,KAAK,EAAEU,OAAQ;kBACfH,OAAO,EAAE7C,QAAQ,CAACQ,WAAW,CAACC,eAAe,CAAC6E,QAAQ,CAACtC,OAAO,CAAE;kBAChEkC,QAAQ,EAAEtC,0BAA2B;kBACrCgB,SAAS,EAAC;gBAAmE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC,eACFpF,OAAA;kBAAO0G,OAAO,EAAE,WAAWvC,OAAO,EAAG;kBAACY,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EACzEb;gBAAO;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAXAjB,OAAO;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYZ,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpF,OAAA;YAAK+E,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBhF,OAAA;cAAI+E,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtFpF,OAAA;cACEwD,IAAI,EAAC,kCAAkC;cACvCC,KAAK,EAAEtC,QAAQ,CAACQ,WAAW,CAACE,wBAAyB;cACrDwE,QAAQ,EAAE/C,iBAAkB;cAC5BqD,IAAI,EAAC,GAAG;cACR5B,SAAS,EAAC,2GAA2G;cACrH6B,WAAW,EAAC;YAAqC;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENpF,OAAA;YAAK+E,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBhF,OAAA;cAAI+E,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/EpF,OAAA;cACEwD,IAAI,EAAC,4BAA4B;cACjCC,KAAK,EAAEtC,QAAQ,CAACQ,WAAW,CAACG,kBAAmB;cAC/CuE,QAAQ,EAAE/C,iBAAkB;cAC5BqD,IAAI,EAAC,GAAG;cACR5B,SAAS,EAAC,2GAA2G;cACrH6B,WAAW,EAAC;YAA8B;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENpF,OAAA;YAAK+E,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBhF,OAAA;cAAI+E,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3EpF,OAAA;cACEwD,IAAI,EAAC,wBAAwB;cAC7BC,KAAK,EAAEtC,QAAQ,CAACQ,WAAW,CAACI,cAAe;cAC3CsE,QAAQ,EAAE/C,iBAAkB;cAC5BqD,IAAI,EAAC,GAAG;cACR5B,SAAS,EAAC,2GAA2G;cACrH6B,WAAW,EAAC,2DAA2D;cACvEC,QAAQ;YAAA;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpF,OAAA;UAAK+E,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9ChF,OAAA,CAACN,MAAM,CAACoH,MAAM;YACZV,IAAI,EAAC,QAAQ;YACbV,OAAO,EAAErC,gBAAiB;YAC1B0B,SAAS,EAAC,0GAA0G;YACpHgC,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAAAhC,QAAA,EAC3B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,eAChBpF,OAAA,CAACN,MAAM,CAACoH,MAAM;YACZV,IAAI,EAAC,QAAQ;YACbrB,SAAS,EAAC,8FAA8F;YACxGgC,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAAAhC,QAAA,EAC3B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,gBAEbpF,OAAA,CAACN,MAAM,CAAC4F,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBC,OAAO,EAAE;QAAED,OAAO,EAAE;MAAE,CAAE;MACxBT,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAErBhF,OAAA;QAAK+E,SAAS,EAAC,8EAA8E;QAAAC,QAAA,gBAC3FhF,OAAA;UAAK+E,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDhF,OAAA;YAAI+E,SAAS,EAAC,wDAAwD;YAAAC,QAAA,gBACpEhF,OAAA,CAACH,MAAM;cAACkF,SAAS,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wBAEpD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLpF,OAAA;YACE0F,OAAO,EAAErC,gBAAiB;YAC1B0B,SAAS,EAAC,0FAA0F;YAAAC,QAAA,EACrG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNpF,OAAA;UAAK+E,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDhF,OAAA;YAAAgF,QAAA,gBACEhF,OAAA;cAAI+E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChEpF,OAAA;cAAG+E,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAErE,WAAW,CAACU;YAAQ;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eACNpF,OAAA;YAAAgF,QAAA,gBACEhF,OAAA;cAAI+E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClEpF,OAAA;cAAG+E,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAErE,WAAW,CAACF;YAAU;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eACNpF,OAAA;YAAAgF,QAAA,gBACEhF,OAAA;cAAI+E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnEpF,OAAA;cAAG+E,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAErE,WAAW,CAACW;YAAW;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eACNpF,OAAA;YAAAgF,QAAA,gBACEhF,OAAA;cAAI+E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7DpF,OAAA;cAAG+E,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAErE,WAAW,CAACY;YAAM;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eACNpF,OAAA;YAAAgF,QAAA,gBACEhF,OAAA;cAAI+E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1DpF,OAAA;cAAG+E,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAErE,WAAW,CAACa;YAAG;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eACNpF,OAAA;YAAAgF,QAAA,gBACEhF,OAAA;cAAI+E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DpF,OAAA;cAAG+E,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAErE,WAAW,CAACc,OAAO,IAAI;YAAc;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC,eACNpF,OAAA;YAAAgF,QAAA,gBACEhF,OAAA;cAAI+E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjEpF,OAAA;cAAG+E,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAErE,WAAW,CAACe,UAAU,IAAI;YAAc;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpF,OAAA;QAAK+E,SAAS,EAAC,8EAA8E;QAAAC,QAAA,gBAC3FhF,OAAA;UAAK+E,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrDhF,OAAA;YAAI+E,SAAS,EAAC,wDAAwD;YAAAC,QAAA,gBACpEhF,OAAA,CAACL,aAAa;cAACoF,SAAS,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mBAE3D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNpF,OAAA;UAAK+E,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhF,OAAA;YAAAgF,QAAA,gBACEhF,OAAA;cAAI+E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvEpF,OAAA;cAAG+E,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EACtC,EAAA5E,qBAAA,GAAAO,WAAW,CAACgB,WAAW,cAAAvB,qBAAA,wBAAAC,sBAAA,GAAvBD,qBAAA,CAAyBwB,eAAe,cAAAvB,sBAAA,uBAAxCA,sBAAA,CAA0C6G,MAAM,IAAG,CAAC,GACjDvG,WAAW,CAACgB,WAAW,CAACC,eAAe,CAACuF,IAAI,CAAC,IAAI,CAAC,GAClD;YAAM;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNpF,OAAA;YAAAgF,QAAA,gBACEhF,OAAA;cAAI+E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjFpF,OAAA;cAAG+E,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EACtC,EAAA1E,sBAAA,GAAAK,WAAW,CAACgB,WAAW,cAAArB,sBAAA,uBAAvBA,sBAAA,CAAyBuB,wBAAwB,KAAI;YAAM;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNpF,OAAA;YAAAgF,QAAA,gBACEhF,OAAA;cAAI+E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1EpF,OAAA;cAAG+E,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EACtC,EAAAzE,sBAAA,GAAAI,WAAW,CAACgB,WAAW,cAAApB,sBAAA,uBAAvBA,sBAAA,CAAyBuB,kBAAkB,KAAI;YAAM;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpF,OAAA;QAAK+E,SAAS,EAAC,8EAA8E;QAAAC,QAAA,gBAC3FhF,OAAA;UAAI+E,SAAS,EAAC,6DAA6D;UAAAC,QAAA,gBACzEhF,OAAA,CAACJ,YAAY;YAACmF,SAAS,EAAC;UAA6B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAE1D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLpF,OAAA;UAAG+E,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EACjC,EAAAxE,sBAAA,GAAAG,WAAW,CAACgB,WAAW,cAAAnB,sBAAA,uBAAvBA,sBAAA,CAAyBuB,cAAc,KAAI;QAAM;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EACb;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjF,EAAA,CArcIF,UAAU;EAAA,QACST,SAAS,EACdM,OAAO;AAAA;AAAAsH,EAAA,GAFrBnH,UAAU;AAuchB,eAAeA,UAAU;AAAC,IAAAmH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}