const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const connectDB = require('./config/db');
const config = require('./config/config');
const errorHandler = require('./utils/errorHandler');
const authRoutes = require('./routes/authRoutes');
const patientRoutes = require('./routes/patientRoutes');
const studentRoutes = require('./routes/studentRoutes');
const supervisorRoutes = require('./routes/supervisorRoutes');
const adminRoutes = require('./routes/adminRoutes');
const superadminRoutes = require('./routes/superadminRoutes');
const appointmentRoutes = require('./routes/appointmentRoutes');
const reviewRoutes = require('./routes/reviewRoutes');
const teethChartRoutes = require('./routes/teethChartRoutes');
const analyticsRoutes = require('./routes/analyticsRoutes');
const universityRoutes = require('./routes/universityRoutes');
const dentistRoutes = require('./routes/dentistRoutes');
const newsRoutes = require('./routes/newsRoutes');
const authController = require('./controllers/authController');
const accountRoutes = require('./routes/accountRoutes');
const procedureRequestRoutes = require('./routes/procedureRequestRoutes');
const labRequestRoutes = require('./routes/labRequestRoutes');
const activityLogger = require('./middleware/activityLogger');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: config.FRONTEND_URL,
    methods: ["GET", "POST"]
  }
});

// Connect to MongoDB
connectDB();

// Enhanced CORS configuration
const cors = require('cors');
app.use(cors({
  origin: config.FRONTEND_URL,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Middleware
app.use(express.json({ limit: config.MAX_FILE_SIZE })); // Configurable limit for JSON payloads
app.use(express.urlencoded({ extended: true, limit: config.MAX_FILE_SIZE })); // Configurable limit for URL-encoded payloads

// Serve static files from uploads directory with configurable path
const path = require('path');
app.use('/uploads', express.static(path.join(__dirname, config.UPLOAD_PATH.replace('./', ''))));

// Make Socket.io instance available to routes/controllers
app.set('io', io);

// Activity logging middleware (after auth middleware)
app.use(activityLogger);

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/patients', patientRoutes);
app.use('/api/students', studentRoutes);
app.use('/api/supervisors', supervisorRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/superadmin', superadminRoutes);
app.use('/api/appointments', appointmentRoutes);
app.use('/api/reviews', reviewRoutes);
app.use('/api/dental', teethChartRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/universities', universityRoutes);
app.use('/api/dentists', dentistRoutes);
app.use('/api/accounts', accountRoutes);
app.use('/api/news', newsRoutes);
app.use('/api/procedure-requests', procedureRequestRoutes);
app.use('/api/lab-requests', labRequestRoutes);




// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({ status: 'OK', timestamp: new Date() });
});

// Socket.io setup with enhanced error handling
io.on('connection', (socket) => {
  console.log('New client connected:', socket.id);

  socket.on('join', (userId) => {
    if (userId) {
      socket.join(userId);
      console.log(`User ${userId} joined their room`);
    }
  });

  socket.on('dentalChartUpdate', (data) => {
    // Broadcast updates to all clients viewing this patient's chart
    if (data.patientId) {
      io.to(data.patientId).emit('dentalChartUpdated', data);
    }
  });

  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });

  socket.on('error', (err) => {
    console.error('Socket error:', err);
  });
});

// Error handling middleware
app.use((req, res, next) => {
  const error = new Error('Not Found');
  error.status = 404;
  next(error);
});

app.use(errorHandler);

// Server setup with graceful shutdown
const PORT = config.PORT || 5000;
const serverInstance = server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

process.on('SIGTERM', () => {
  console.log('SIGTERM received. Shutting down gracefully...');
  serverInstance.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received. Shutting down gracefully...');
  serverInstance.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

module.exports = server;