# 🚀 ODenta Deployment Guide

Complete deployment guide for the ODenta dental management system with Railway (backend) and Vercel (frontend).

## 📋 Prerequisites

- GitHub account
- Railway account ([railway.app](https://railway.app))
- Vercel account ([vercel.com](https://vercel.com))
- MongoDB Atlas account (for production database)

## 🗄️ Database Setup (MongoDB Atlas)

1. **Create MongoDB Atlas Account**
   - Go to [mongodb.com/atlas](https://mongodb.com/atlas)
   - Create a free account

2. **Create a Cluster**
   - Choose "Build a Database" → "Shared" (free tier)
   - Select your preferred cloud provider and region
   - Create cluster

3. **Configure Database Access**
   - Go to "Database Access" → "Add New Database User"
   - Create username and password
   - Set privileges to "Read and write to any database"

4. **Configure Network Access**
   - Go to "Network Access" → "Add IP Address"
   - Add `0.0.0.0/0` (allow access from anywhere) for Railway deployment

5. **Get Connection String**
   - Go to "Clusters" → "Connect" → "Connect your application"
   - Copy the connection string
   - Replace `<password>` with your database user password

## 🚂 Backend Deployment (Railway)

### Step 1: Prepare Repository
```bash
# Ensure your backend code is pushed to GitHub
git add .
git commit -m "Prepare for Railway deployment"
git push origin main
```

### Step 2: Deploy to Railway

1. **Create Railway Project**
   - Go to [railway.app](https://railway.app)
   - Click "Start a New Project"
   - Select "Deploy from GitHub repo"
   - Choose your repository
   - Select the `dentlyzer-backend` folder

2. **Set Environment Variables**
   In Railway dashboard, go to Variables tab and add:

   ```env
   # Database Configuration
   MONGO_URI=mongodb+srv://username:<EMAIL>/dentlyzer
   
   # JWT Configuration (generate new secrets for production)
   JWT_SECRET=your_production_jwt_secret_here
   JWT_REFRESH_SECRET=your_production_refresh_secret_here
   JWT_ACCESS_EXPIRATION=15m
   JWT_REFRESH_EXPIRATION=7d
   
   # Server Configuration
   PORT=5000
   NODE_ENV=production
   
   # Frontend URL (will be updated after Vercel deployment)
   FRONTEND_URL=https://your-vercel-app.vercel.app
   
   # File Upload Configuration
   UPLOAD_PATH=./uploads
   MAX_FILE_SIZE=50mb
   ```

3. **Generate JWT Secrets**
   ```bash
   # Run this command to generate secure JWT secrets
   node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
   ```

4. **Deploy**
   - Railway will automatically deploy your backend
   - Note the deployment URL (e.g., `https://your-app.railway.app`)

### Step 3: Test Backend
- Visit `https://your-app.railway.app/api/health`
- Should return: `{"status":"OK","timestamp":"..."}`

## 🌐 Frontend Deployment (Vercel)

### Step 1: Prepare Frontend
```bash
# Update frontend environment variables
cd dentlyzer-frontend
```

### Step 2: Deploy to Vercel

1. **Create Vercel Project**
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository
   - Select the `dentlyzer-frontend` folder

2. **Configure Build Settings**
   - Framework Preset: Create React App
   - Build Command: `npm run build`
   - Output Directory: `build`
   - Install Command: `npm install`

3. **Set Environment Variables**
   In Vercel dashboard, go to Settings → Environment Variables:

   ```env
   REACT_APP_API_URL=https://your-railway-app.railway.app
   ```

4. **Deploy**
   - Click "Deploy"
   - Vercel will build and deploy your frontend
   - Note the deployment URL (e.g., `https://your-app.vercel.app`)

### Step 3: Update Backend CORS
1. **Update Railway Environment Variables**
   - Go back to Railway dashboard
   - Update `FRONTEND_URL` to your Vercel URL:
   ```env
   FRONTEND_URL=https://your-app.vercel.app
   ```

2. **Redeploy Backend**
   - Railway will automatically redeploy with new environment variables

## ✅ Post-Deployment Checklist

### 1. Test Authentication
- [ ] Visit your frontend URL
- [ ] Try logging in with test credentials
- [ ] Verify JWT tokens are working

### 2. Test File Uploads
- [ ] Upload X-ray images
- [ ] Upload gallery images
- [ ] Verify files are stored correctly

### 3. Test Database Operations
- [ ] Create new patients
- [ ] Schedule appointments
- [ ] Submit reviews
- [ ] Verify data persistence

### 4. Test Real-time Features
- [ ] Socket.io connections
- [ ] Real-time updates
- [ ] Notifications

## 🔧 Environment Variables Summary

### Backend (Railway)
```env
MONGO_URI=mongodb+srv://username:<EMAIL>/dentlyzer
JWT_SECRET=your_production_jwt_secret
JWT_REFRESH_SECRET=your_production_refresh_secret
NODE_ENV=production
FRONTEND_URL=https://your-vercel-app.vercel.app
PORT=5000
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=50mb
```

### Frontend (Vercel)
```env
REACT_APP_API_URL=https://your-railway-app.railway.app
```

## 🐛 Troubleshooting

### Common Issues

1. **CORS Errors**
   - Verify `FRONTEND_URL` in Railway matches your Vercel URL exactly
   - Check for trailing slashes

2. **Database Connection Issues**
   - Verify MongoDB Atlas connection string
   - Check network access settings (0.0.0.0/0)
   - Ensure database user has correct permissions

3. **File Upload Issues**
   - Railway has ephemeral storage - files may be lost on redeploy
   - Consider using Cloudinary for production file storage

4. **Environment Variable Issues**
   - Ensure all required variables are set
   - Check for typos in variable names
   - Redeploy after changing variables

## 🔄 Continuous Deployment

Both Railway and Vercel support automatic deployments:

- **Railway**: Automatically deploys when you push to your main branch
- **Vercel**: Automatically deploys when you push to your main branch

To deploy updates:
```bash
git add .
git commit -m "Your update message"
git push origin main
```

## 🔐 Security Considerations

1. **Use Strong JWT Secrets**
   - Generate new secrets for production
   - Keep secrets secure and never commit them

2. **Database Security**
   - Use strong database passwords
   - Limit network access when possible
   - Enable MongoDB Atlas security features

3. **Environment Variables**
   - Never commit `.env` files
   - Use different secrets for development and production
   - Regularly rotate secrets

## 📞 Support

If you encounter issues during deployment:
1. Check the deployment logs in Railway/Vercel dashboards
2. Verify all environment variables are set correctly
3. Test API endpoints individually
4. Check database connectivity

## 🎉 Success!

Once deployed successfully, your ODenta application will be available at:
- **Frontend**: `https://your-app.vercel.app`
- **Backend API**: `https://your-app.railway.app`

Your dental management system is now live and ready for use!
