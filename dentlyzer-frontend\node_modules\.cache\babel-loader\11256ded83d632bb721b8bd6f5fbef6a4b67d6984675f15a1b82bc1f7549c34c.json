{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\student\\\\Appointments.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport Navbar from './Navbar';\nimport Sidebar from './Sidebar';\nimport PatientNav from './PatientNav';\nimport axios from 'axios';\nimport { useParams } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { motion } from 'framer-motion';\nimport { FaPlus, FaSearch, FaCalendar, FaClock, FaTimes } from 'react-icons/fa';\nimport Loader from '../components/Loader';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Appointments = () => {\n  _s();\n  const {\n    user,\n    token\n  } = useAuth();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [showAppointmentDetails, setShowAppointmentDetails] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [appointments, setAppointments] = useState([]);\n  const [availableSlots, setAvailableSlots] = useState([]);\n  const [slotsLoading, setSlotsLoading] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const {\n    nationalId\n  } = useParams();\n  const [newAppointment, setNewAppointment] = useState({\n    date: '',\n    time: '',\n    type: 'checkup',\n    notes: '',\n    status: 'pending',\n    patient: nationalId,\n    doctor: (user === null || user === void 0 ? void 0 : user.studentId) || '',\n    doctorModel: 'Student',\n    chiefComplaint: ''\n  });\n\n  // Log user and token for debugging\n  useEffect(() => {\n    console.log('User:', user, 'Token:', token, 'NationalId:', nationalId);\n  }, [user, token, nationalId]);\n  useEffect(() => {\n    const fetchAppointments = async () => {\n      try {\n        console.log('Fetching appointments for nationalId:', nationalId);\n        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/appointments/patient/${nationalId}`, {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        });\n        console.log('Appointments fetched:', response.data);\n        setAppointments(response.data || []);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response2$data;\n        console.error('Error fetching appointments:', ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n        setError(((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.message) || 'Failed to load appointments');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (token && nationalId) fetchAppointments();\n  }, [nationalId, token]);\n  useEffect(() => {\n    const fetchAvailableSlots = async () => {\n      if (!newAppointment.date || !(user !== null && user !== void 0 && user.studentId)) {\n        console.log('Skipping fetch: missing date or studentId', {\n          date: newAppointment.date,\n          studentId: user === null || user === void 0 ? void 0 : user.studentId\n        });\n        return;\n      }\n      setSlotsLoading(true);\n      try {\n        const formattedDate = new Date(newAppointment.date).toISOString().split('T')[0];\n        console.log('Fetching slots with params:', {\n          doctorId: user.studentId,\n          doctorModel: 'Student',\n          date: formattedDate\n        });\n        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/appointments/available-slots`, {\n          params: {\n            doctorId: user.studentId,\n            doctorModel: 'Student',\n            date: formattedDate\n          },\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        });\n        console.log('Slots fetched:', response.data);\n        setAvailableSlots(response.data || []);\n      } catch (err) {\n        var _err$response3, _err$response4, _err$response4$data;\n        console.error('Error fetching slots:', ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.data) || err.message);\n        setError(((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.message) || 'Failed to load available slots');\n        setAvailableSlots([]);\n      } finally {\n        setSlotsLoading(false);\n      }\n    };\n    fetchAvailableSlots();\n  }, [newAppointment.date, user === null || user === void 0 ? void 0 : user.studentId, token]);\n  const validateForm = () => {\n    if (!newAppointment.date) return 'Date is required';\n    if (!newAppointment.time) return 'Time is required';\n    if (!newAppointment.type) return 'Appointment type is required';\n    if (!newAppointment.chiefComplaint) return 'Chief complaint is required';\n    if (!newAppointment.doctor) return 'Doctor ID is required';\n    if (!newAppointment.patient) return 'Patient ID is required';\n    return '';\n  };\n  const handleAddAppointment = async e => {\n    e.preventDefault();\n    if (!(user !== null && user !== void 0 && user.studentId)) {\n      setError('You must be logged in as a student to add an appointment.');\n      return;\n    }\n    const validationError = validateForm();\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n    try {\n      console.log('Creating appointment:', newAppointment);\n      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/appointments`, newAppointment, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      console.log('Appointment created:', response.data);\n      setAppointments([response.data, ...appointments]);\n      setNewAppointment({\n        date: '',\n        time: '',\n        type: 'checkup',\n        notes: '',\n        status: 'pending',\n        patient: nationalId,\n        doctor: user.studentId,\n        doctorModel: 'Student',\n        chiefComplaint: ''\n      });\n      setShowAddForm(false);\n      setError('');\n    } catch (err) {\n      var _err$response5, _err$response6, _err$response6$data;\n      console.error('Error creating appointment:', ((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : _err$response5.data) || err.message);\n      setError(((_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.message) || 'Failed to add appointment');\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    console.log('Input changed:', {\n      name,\n      value\n    });\n    setNewAppointment({\n      ...newAppointment,\n      [name]: value\n    });\n  };\n  const filteredAppointments = appointments.filter(appt => {\n    var _appt$notes;\n    return appt.type.toLowerCase().includes(searchTerm.toLowerCase()) || ((_appt$notes = appt.notes) === null || _appt$notes === void 0 ? void 0 : _appt$notes.toLowerCase().includes(searchTerm.toLowerCase()));\n  });\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      weekday: 'short',\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n  const formatTime = timeString => {\n    const [hours, minutes] = timeString.split(':');\n    const hour = parseInt(hours);\n    return `${hour > 12 ? hour - 12 : hour}:${minutes} ${hour >= 12 ? 'PM' : 'AM'}`;\n  };\n  const deleteAppointment = async id => {\n    try {\n      console.log('Deleting appointment:', id);\n      await axios.delete(`${process.env.REACT_APP_API_URL}/api/appointments/${id}`, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      setAppointments(appointments.filter(appt => appt._id !== id));\n      setShowAppointmentDetails(null);\n    } catch (err) {\n      var _err$response7, _err$response8, _err$response8$data;\n      console.error('Error deleting appointment:', ((_err$response7 = err.response) === null || _err$response7 === void 0 ? void 0 : _err$response7.data) || err.message);\n      setError(((_err$response8 = err.response) === null || _err$response8 === void 0 ? void 0 : (_err$response8$data = _err$response8.data) === null || _err$response8$data === void 0 ? void 0 : _err$response8$data.message) || 'Failed to delete appointment');\n    }\n  };\n\n  // Animation variants\n  const container = {\n    hidden: {\n      opacity: 0\n    },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const item = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    show: {\n      opacity: 1,\n      y: 0\n    }\n  };\n  if (!user || !user.studentId) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n        isOpen: sidebarOpen,\n        setIsOpen: setSidebarOpen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex flex-col overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(Navbar, {\n          toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0.9,\n                opacity: 0\n              },\n              animate: {\n                scale: 1,\n                opacity: 1\n              },\n              className: \"text-center max-w-md bg-white p-8 rounded-xl shadow-sm border border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0077B6] mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  className: \"h-12 w-12 mx-auto\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-bold text-gray-900 mb-2\",\n                children: \"Access Denied\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-6\",\n                children: \"Please log in as a student to view this page.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PatientNav, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: [error && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            className: \"mb-6 p-4 bg-red-100 text-[#333333] rounded-lg\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\",\n                  children: \"Appointments\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600\",\n                  children: \"Manage patient appointments\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: () => setShowAddForm(true),\n                className: \"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-2.5 rounded-full font-medium hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaPlus, {\n                  className: \"h-5 w-5 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 19\n                }, this), \"New Appointment\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                scale: 1.02\n              },\n              className: \"relative mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(FaSearch, {\n                  className: \"h-5 w-5 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search appointments...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                className: \"block w-full pl-10 pr-3 py-2.5 border border-gray-200 rounded-full bg-white shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#0077B6] focus:bg-white sm:text-sm transition-all duration-200\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: container,\n              initial: \"hidden\",\n              animate: \"show\",\n              className: \"bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden\",\n              children: filteredAppointments.length === 0 ? /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0\n                },\n                animate: {\n                  opacity: 1\n                },\n                className: \"p-8 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mx-auto h-16 w-16 text-gray-400 mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 321,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-bold text-gray-900 mb-2\",\n                  children: \"No appointments found\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-500\",\n                  children: searchTerm ? 'Try a different search term' : 'Get started by adding a new appointment'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => setShowAddForm(true),\n                  className: \"mt-6 inline-flex items-center px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium shadow-md hover:shadow-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(FaPlus, {\n                    className: \"h-5 w-5 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 23\n                  }, this), \"Add Appointment\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"divide-y divide-gray-200\",\n                children: filteredAppointments.map(appt => /*#__PURE__*/_jsxDEV(motion.li, {\n                  variants: item,\n                  className: \"hover:bg-gray-50 transition-colors cursor-pointer\",\n                  onClick: () => setShowAppointmentDetails(appt),\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"px-6 py-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `h-3 w-3 rounded-full ${appt.status === 'completed' ? 'bg-[#28A745]' : appt.status === 'cancelled' ? 'bg-red-500' : 'bg-[#0077B6]'}`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 355,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: \"text-lg font-medium text-gray-800\",\n                          children: appt.type\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 364,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 354,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: formatDate(appt.date)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 366,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 353,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-2 flex justify-between items-center\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-medium\",\n                          children: \"Time:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 370,\n                          columnNumber: 31\n                        }, this), \" \", formatTime(appt.time)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 369,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 368,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 25\n                  }, this)\n                }, appt._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), showAddForm && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0.9,\n                opacity: 0\n              },\n              animate: {\n                scale: 1,\n                opacity: 1\n              },\n              className: \"bg-white rounded-xl w-full max-w-md max-h-[90vh] overflow-y-auto shadow-2xl border border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 border-b flex justify-between items-center bg-[#0077B6]/10\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-semibold text-[#0077B6]\",\n                  children: \"New Appointment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setShowAddForm(false),\n                  className: \"text-gray-500 hover:text-gray-700\",\n                  children: /*#__PURE__*/_jsxDEV(FaTimes, {\n                    className: \"h-6 w-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n                onSubmit: handleAddAppointment,\n                className: \"p-6 space-y-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1.5\",\n                    children: \"Date*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative\",\n                    children: [/*#__PURE__*/_jsxDEV(FaCalendar, {\n                      className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 400,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"date\",\n                      name: \"date\",\n                      value: newAppointment.date,\n                      onChange: handleInputChange,\n                      className: \"pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\",\n                      required: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 401,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1.5\",\n                    children: \"Time*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative\",\n                    children: [/*#__PURE__*/_jsxDEV(FaClock, {\n                      className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 414,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                      name: \"time\",\n                      value: newAppointment.time,\n                      onChange: handleInputChange,\n                      className: \"pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\",\n                      required: true,\n                      disabled: slotsLoading,\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"\",\n                        children: \"Select a time\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 423,\n                        columnNumber: 27\n                      }, this), slotsLoading ? /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"\",\n                        disabled: true,\n                        children: \"Loading slots...\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 425,\n                        columnNumber: 29\n                      }, this) : availableSlots.length === 0 ? /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"\",\n                        disabled: true,\n                        children: \"No available slots\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 429,\n                        columnNumber: 29\n                      }, this) : availableSlots.map(slot => /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: slot.split(' - ')[0],\n                        children: slot\n                      }, slot, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 434,\n                        columnNumber: 31\n                      }, this))]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 415,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1.5\",\n                    children: \"Type*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    name: \"type\",\n                    value: newAppointment.type,\n                    onChange: handleInputChange,\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\",\n                    required: true,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"checkup\",\n                      children: \"Checkup\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"cleaning\",\n                      children: \"Cleaning\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 452,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"filling\",\n                      children: \"Filling\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 453,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"extraction\",\n                      children: \"Extraction\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"other\",\n                      children: \"Other\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 455,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1.5\",\n                    children: \"Chief Complaint*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    name: \"chiefComplaint\",\n                    value: newAppointment.chiefComplaint,\n                    onChange: handleInputChange,\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\",\n                    rows: \"3\",\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1.5\",\n                    children: \"Notes (Optional)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    name: \"notes\",\n                    value: newAppointment.notes,\n                    onChange: handleInputChange,\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\",\n                    rows: \"3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1.5\",\n                    children: \"Doctor (Student ID)*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 480,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"doctor\",\n                    value: newAppointment.doctor,\n                    onChange: handleInputChange,\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\",\n                    disabled: true,\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1.5\",\n                    children: \"Patient ID*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 492,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"patient\",\n                    value: newAppointment.patient,\n                    onChange: handleInputChange,\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\",\n                    disabled: true,\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-end space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                    type: \"button\",\n                    onClick: () => setShowAddForm(false),\n                    className: \"px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 font-medium transition-colors\",\n                    whileHover: {\n                      scale: 1.05\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    children: \"Cancel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                    type: \"submit\",\n                    className: \"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium transition-colors shadow-md hover:shadow-lg\",\n                    whileHover: {\n                      scale: 1.05\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    disabled: slotsLoading,\n                    children: \"Save\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 513,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 15\n          }, this), showAppointmentDetails && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0.9,\n                opacity: 0\n              },\n              animate: {\n                scale: 1,\n                opacity: 1\n              },\n              className: \"bg-white rounded-xl w-full max-w-md max-h-[90vh] overflow-y-auto shadow-2xl border border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 border-b flex justify-between items-center bg-[#0077B6]/10\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-semibold text-[#0077B6]\",\n                  children: \"Appointment Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setShowAppointmentDetails(null),\n                  className: \"text-gray-500 hover:text-gray-700\",\n                  children: /*#__PURE__*/_jsxDEV(FaTimes, {\n                    className: \"h-6 w-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 540,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 536,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 space-y-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-medium text-gray-800\",\n                    children: showAppointmentDetails.type\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 545,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-3 py-1 rounded-full text-sm font-medium ${showAppointmentDetails.status === 'completed' ? 'bg-[#28A745]/20 text-[#28A745]' : showAppointmentDetails.status === 'cancelled' ? 'bg-red-100 text-red-800' : 'bg-[#0077B6]/20 text-[#0077B6]'}`,\n                    children: showAppointmentDetails.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 546,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-2 gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 560,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-900\",\n                      children: formatDate(showAppointmentDetails.date)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 561,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 559,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Time\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 564,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-900\",\n                      children: formatTime(showAppointmentDetails.time)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 565,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 563,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 21\n                }, this), showAppointmentDetails.chiefComplaint && /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Chief Complaint\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 570,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900\",\n                    children: showAppointmentDetails.chiefComplaint\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 569,\n                  columnNumber: 23\n                }, this), showAppointmentDetails.notes && /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Notes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 576,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900\",\n                    children: showAppointmentDetails.notes\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 577,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 575,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-end gap-3\",\n                  children: /*#__PURE__*/_jsxDEV(motion.button, {\n                    whileHover: {\n                      scale: 1.05\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    onClick: () => deleteAppointment(showAppointmentDetails._id),\n                    className: \"px-6 py-2 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-full hover:from-red-600 hover:to-red-700 font-medium transition-colors shadow-md\",\n                    children: \"Delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 249,\n    columnNumber: 5\n  }, this);\n};\n_s(Appointments, \"Vjs+9n4Djo4NZY7SoCPK6aKKK2Y=\", false, function () {\n  return [useAuth, useParams];\n});\n_c = Appointments;\nexport default Appointments;\nvar _c;\n$RefreshReg$(_c, \"Appointments\");", "map": {"version": 3, "names": ["useState", "useEffect", "<PERSON><PERSON><PERSON>", "Sidebar", "PatientNav", "axios", "useParams", "useAuth", "motion", "FaPlus", "FaSearch", "FaCalendar", "FaClock", "FaTimes", "Loader", "jsxDEV", "_jsxDEV", "Appointments", "_s", "user", "token", "sidebarOpen", "setSidebarOpen", "showAddForm", "setShowAddForm", "showAppointmentDetails", "setShowAppointmentDetails", "searchTerm", "setSearchTerm", "appointments", "setAppointments", "availableSlots", "setAvailableSlots", "slotsLoading", "setSlotsLoading", "loading", "setLoading", "error", "setError", "nationalId", "newAppointment", "setNewAppointment", "date", "time", "type", "notes", "status", "patient", "doctor", "studentId", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "console", "log", "fetchAppointments", "response", "get", "process", "env", "REACT_APP_API_URL", "headers", "Authorization", "data", "err", "_err$response", "_err$response2", "_err$response2$data", "message", "fetchAvailableSlots", "formattedDate", "Date", "toISOString", "split", "doctorId", "params", "_err$response3", "_err$response4", "_err$response4$data", "validateForm", "handleAddAppointment", "e", "preventDefault", "validationError", "post", "_err$response5", "_err$response6", "_err$response6$data", "handleInputChange", "name", "value", "target", "filteredAppointments", "filter", "appt", "_appt$notes", "toLowerCase", "includes", "formatDate", "dateString", "toLocaleDateString", "weekday", "year", "month", "day", "formatTime", "timeString", "hours", "minutes", "hour", "parseInt", "deleteAppointment", "id", "delete", "_id", "_err$response7", "_err$response8", "_err$response8$data", "container", "hidden", "opacity", "show", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "item", "y", "className", "children", "isOpen", "setIsOpen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toggleSidebar", "div", "initial", "scale", "animate", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "duration", "button", "whileHover", "whileTap", "onClick", "placeholder", "onChange", "variants", "length", "map", "li", "onSubmit", "required", "disabled", "slot", "rows", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/student/Appointments.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport Navbar from './Navbar';\r\nimport Sidebar from './Sidebar';\r\nimport PatientNav from './PatientNav';\r\nimport axios from 'axios';\r\nimport { useParams } from 'react-router-dom';\r\nimport { useAuth } from '../context/AuthContext';\r\nimport { motion } from 'framer-motion';\r\nimport { FaPlus, FaSearch, FaCalendar, FaClock, FaTimes } from 'react-icons/fa';\r\nimport Loader from '../components/Loader';\r\n\r\nconst Appointments = () => {\r\n  const { user, token } = useAuth();\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const [showAddForm, setShowAddForm] = useState(false);\r\n  const [showAppointmentDetails, setShowAppointmentDetails] = useState(null);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [appointments, setAppointments] = useState([]);\r\n  const [availableSlots, setAvailableSlots] = useState([]);\r\n  const [slotsLoading, setSlotsLoading] = useState(false);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const { nationalId } = useParams();\r\n\r\n  const [newAppointment, setNewAppointment] = useState({\r\n    date: '',\r\n    time: '',\r\n    type: 'checkup',\r\n    notes: '',\r\n    status: 'pending',\r\n    patient: nationalId,\r\n    doctor: user?.studentId || '',\r\n    doctorModel: 'Student',\r\n    chiefComplaint: '',\r\n  });\r\n\r\n  // Log user and token for debugging\r\n  useEffect(() => {\r\n    console.log('User:', user, 'Token:', token, 'NationalId:', nationalId);\r\n  }, [user, token, nationalId]);\r\n\r\n  useEffect(() => {\r\n    const fetchAppointments = async () => {\r\n      try {\r\n        console.log('Fetching appointments for nationalId:', nationalId);\r\n        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/appointments/patient/${nationalId}`, {\r\n          headers: { Authorization: `Bearer ${token}` },\r\n        });\r\n        console.log('Appointments fetched:', response.data);\r\n        setAppointments(response.data || []);\r\n      } catch (err) {\r\n        console.error('Error fetching appointments:', err.response?.data || err.message);\r\n        setError(err.response?.data?.message || 'Failed to load appointments');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    if (token && nationalId) fetchAppointments();\r\n  }, [nationalId, token]);\r\n\r\n  useEffect(() => {\r\n    const fetchAvailableSlots = async () => {\r\n      if (!newAppointment.date || !user?.studentId) {\r\n        console.log('Skipping fetch: missing date or studentId', {\r\n          date: newAppointment.date,\r\n          studentId: user?.studentId,\r\n        });\r\n        return;\r\n      }\r\n\r\n      setSlotsLoading(true);\r\n      try {\r\n        const formattedDate = new Date(newAppointment.date).toISOString().split('T')[0];\r\n        console.log('Fetching slots with params:', {\r\n          doctorId: user.studentId,\r\n          doctorModel: 'Student',\r\n          date: formattedDate,\r\n        });\r\n\r\n        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/appointments/available-slots`, {\r\n          params: {\r\n            doctorId: user.studentId,\r\n            doctorModel: 'Student',\r\n            date: formattedDate,\r\n          },\r\n          headers: { Authorization: `Bearer ${token}` },\r\n        });\r\n\r\n        console.log('Slots fetched:', response.data);\r\n        setAvailableSlots(response.data || []);\r\n      } catch (err) {\r\n        console.error('Error fetching slots:', err.response?.data || err.message);\r\n        setError(err.response?.data?.message || 'Failed to load available slots');\r\n        setAvailableSlots([]);\r\n      } finally {\r\n        setSlotsLoading(false);\r\n      }\r\n    };\r\n    fetchAvailableSlots();\r\n  }, [newAppointment.date, user?.studentId, token]);\r\n\r\n  const validateForm = () => {\r\n    if (!newAppointment.date) return 'Date is required';\r\n    if (!newAppointment.time) return 'Time is required';\r\n    if (!newAppointment.type) return 'Appointment type is required';\r\n    if (!newAppointment.chiefComplaint) return 'Chief complaint is required';\r\n    if (!newAppointment.doctor) return 'Doctor ID is required';\r\n    if (!newAppointment.patient) return 'Patient ID is required';\r\n    return '';\r\n  };\r\n\r\n  const handleAddAppointment = async (e) => {\r\n    e.preventDefault();\r\n    if (!user?.studentId) {\r\n      setError('You must be logged in as a student to add an appointment.');\r\n      return;\r\n    }\r\n    const validationError = validateForm();\r\n    if (validationError) {\r\n      setError(validationError);\r\n      return;\r\n    }\r\n    try {\r\n      console.log('Creating appointment:', newAppointment);\r\n      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/appointments`, newAppointment, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      console.log('Appointment created:', response.data);\r\n      setAppointments([response.data, ...appointments]);\r\n      setNewAppointment({\r\n        date: '',\r\n        time: '',\r\n        type: 'checkup',\r\n        notes: '',\r\n        status: 'pending',\r\n        patient: nationalId,\r\n        doctor: user.studentId,\r\n        doctorModel: 'Student',\r\n        chiefComplaint: '',\r\n      });\r\n      setShowAddForm(false);\r\n      setError('');\r\n    } catch (err) {\r\n      console.error('Error creating appointment:', err.response?.data || err.message);\r\n      setError(err.response?.data?.message || 'Failed to add appointment');\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value } = e.target;\r\n    console.log('Input changed:', { name, value });\r\n    setNewAppointment({ ...newAppointment, [name]: value });\r\n  };\r\n\r\n  const filteredAppointments = appointments.filter((appt) =>\r\n    appt.type.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n    appt.notes?.toLowerCase().includes(searchTerm.toLowerCase())\r\n  );\r\n\r\n  const formatDate = (dateString) => {\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      weekday: 'short',\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n    });\r\n  };\r\n\r\n  const formatTime = (timeString) => {\r\n    const [hours, minutes] = timeString.split(':');\r\n    const hour = parseInt(hours);\r\n    return `${hour > 12 ? hour - 12 : hour}:${minutes} ${hour >= 12 ? 'PM' : 'AM'}`;\r\n  };\r\n\r\n  const deleteAppointment = async (id) => {\r\n    try {\r\n      console.log('Deleting appointment:', id);\r\n      await axios.delete(`${process.env.REACT_APP_API_URL}/api/appointments/${id}`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      setAppointments(appointments.filter((appt) => appt._id !== id));\r\n      setShowAppointmentDetails(null);\r\n    } catch (err) {\r\n      console.error('Error deleting appointment:', err.response?.data || err.message);\r\n      setError(err.response?.data?.message || 'Failed to delete appointment');\r\n    }\r\n  };\r\n\r\n  // Animation variants\r\n  const container = {\r\n    hidden: { opacity: 0 },\r\n    show: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1,\r\n      },\r\n    },\r\n  };\r\n\r\n  const item = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    show: { opacity: 1, y: 0 },\r\n  };\r\n\r\n  if (!user || !user.studentId) {\r\n    return (\r\n      <div className=\"flex h-screen bg-gray-50\">\r\n        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n        <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\r\n          <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white\">\r\n            <div className=\"max-w-7xl mx-auto\">\r\n              <motion.div\r\n                initial={{ scale: 0.9, opacity: 0 }}\r\n                animate={{ scale: 1, opacity: 1 }}\r\n                className=\"text-center max-w-md bg-white p-8 rounded-xl shadow-sm border border-gray-100\"\r\n              >\r\n                <div className=\"text-[#0077B6] mb-4\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    className=\"h-12 w-12 mx-auto\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    stroke=\"currentColor\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      strokeWidth={2}\r\n                      d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\r\n                    />\r\n                  </svg>\r\n                </div>\r\n                <h3 className=\"text-lg font-bold text-gray-900 mb-2\">Access Denied</h3>\r\n                <p className=\"text-gray-600 mb-6\">Please log in as a student to view this page.</p>\r\n              </motion.div>\r\n            </div>\r\n          </main>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (loading) {\r\n    return <Loader />;\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex h-screen bg-gray-50\">\r\n      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\r\n        <PatientNav />\r\n        <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white\">\r\n          <div className=\"max-w-7xl mx-auto\">\r\n            {error && (\r\n              <motion.div\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                className=\"mb-6 p-4 bg-red-100 text-[#333333] rounded-lg\"\r\n              >\r\n                {error}\r\n              </motion.div>\r\n            )}\r\n            <motion.div\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              transition={{ duration: 0.5 }}\r\n            >\r\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\">\r\n                <div>\r\n                  <h1 className=\"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\">\r\n                    Appointments\r\n                  </h1>\r\n                  <p className=\"text-gray-600\">Manage patient appointments</p>\r\n                </div>\r\n                <motion.button\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                  onClick={() => setShowAddForm(true)}\r\n                  className=\"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-2.5 rounded-full font-medium hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\"\r\n                >\r\n                  <FaPlus className=\"h-5 w-5 mr-2\" />\r\n                  New Appointment\r\n                </motion.button>\r\n              </div>\r\n              <motion.div\r\n                whileHover={{ scale: 1.02 }}\r\n                className=\"relative mb-6\"\r\n              >\r\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                  <FaSearch className=\"h-5 w-5 text-gray-400\" />\r\n                </div>\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Search appointments...\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  className=\"block w-full pl-10 pr-3 py-2.5 border border-gray-200 rounded-full bg-white shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#0077B6] focus:bg-white sm:text-sm transition-all duration-200\"\r\n                />\r\n              </motion.div>\r\n              <motion.div\r\n                variants={container}\r\n                initial=\"hidden\"\r\n                animate=\"show\"\r\n                className=\"bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden\"\r\n              >\r\n                {filteredAppointments.length === 0 ? (\r\n                  <motion.div\r\n                    initial={{ opacity: 0 }}\r\n                    animate={{ opacity: 1 }}\r\n                    className=\"p-8 text-center\"\r\n                  >\r\n                    <div className=\"mx-auto h-16 w-16 text-gray-400 mb-4\">\r\n                      <svg\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        fill=\"none\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        stroke=\"currentColor\"\r\n                      >\r\n                        <path\r\n                          strokeLinecap=\"round\"\r\n                          strokeLinejoin=\"round\"\r\n                          strokeWidth={2}\r\n                          d=\"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <h3 className=\"text-lg font-bold text-gray-900 mb-2\">No appointments found</h3>\r\n                    <p className=\"text-gray-500\">\r\n                      {searchTerm ? 'Try a different search term' : 'Get started by adding a new appointment'}\r\n                    </p>\r\n                    <motion.button\r\n                      whileHover={{ scale: 1.05 }}\r\n                      whileTap={{ scale: 0.95 }}\r\n                      onClick={() => setShowAddForm(true)}\r\n                      className=\"mt-6 inline-flex items-center px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium shadow-md hover:shadow-lg\"\r\n                    >\r\n                      <FaPlus className=\"h-5 w-5 mr-2\" />\r\n                      Add Appointment\r\n                    </motion.button>\r\n                  </motion.div>\r\n                ) : (\r\n                  <ul className=\"divide-y divide-gray-200\">\r\n                    {filteredAppointments.map((appt) => (\r\n                      <motion.li\r\n                        key={appt._id}\r\n                        variants={item}\r\n                        className=\"hover:bg-gray-50 transition-colors cursor-pointer\"\r\n                        onClick={() => setShowAppointmentDetails(appt)}\r\n                      >\r\n                        <div className=\"px-6 py-4\">\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex items-center gap-3\">\r\n                              <div\r\n                                className={`h-3 w-3 rounded-full ${\r\n                                  appt.status === 'completed'\r\n                                    ? 'bg-[#28A745]'\r\n                                    : appt.status === 'cancelled'\r\n                                    ? 'bg-red-500'\r\n                                    : 'bg-[#0077B6]'\r\n                                }`}\r\n                              ></div>\r\n                              <h3 className=\"text-lg font-medium text-gray-800\">{appt.type}</h3>\r\n                            </div>\r\n                            <div className=\"text-sm text-gray-500\">{formatDate(appt.date)}</div>\r\n                          </div>\r\n                          <div className=\"mt-2 flex justify-between items-center\">\r\n                            <p className=\"text-sm text-gray-600\">\r\n                              <span className=\"font-medium\">Time:</span> {formatTime(appt.time)}\r\n                            </p>\r\n                          </div>\r\n                        </div>\r\n                      </motion.li>\r\n                    ))}\r\n                  </ul>\r\n                )}\r\n              </motion.div>\r\n            </motion.div>\r\n            {showAddForm && (\r\n              <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n                <motion.div\r\n                  initial={{ scale: 0.9, opacity: 0 }}\r\n                  animate={{ scale: 1, opacity: 1 }}\r\n                  className=\"bg-white rounded-xl w-full max-w-md max-h-[90vh] overflow-y-auto shadow-2xl border border-gray-100\"\r\n                >\r\n                  <div className=\"p-6 border-b flex justify-between items-center bg-[#0077B6]/10\">\r\n                    <h2 className=\"text-xl font-semibold text-[#0077B6]\">New Appointment</h2>\r\n                    <button\r\n                      onClick={() => setShowAddForm(false)}\r\n                      className=\"text-gray-500 hover:text-gray-700\"\r\n                    >\r\n                      <FaTimes className=\"h-6 w-6\" />\r\n                    </button>\r\n                  </div>\r\n                  <form onSubmit={handleAddAppointment} className=\"p-6 space-y-6\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">Date*</label>\r\n                      <div className=\"relative\">\r\n                        <FaCalendar className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\r\n                        <input\r\n                          type=\"date\"\r\n                          name=\"date\"\r\n                          value={newAppointment.date}\r\n                          onChange={handleInputChange}\r\n                          className=\"pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\"\r\n                          required\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">Time*</label>\r\n                      <div className=\"relative\">\r\n                        <FaClock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\r\n                        <select\r\n                          name=\"time\"\r\n                          value={newAppointment.time}\r\n                          onChange={handleInputChange}\r\n                          className=\"pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\"\r\n                          required\r\n                          disabled={slotsLoading}\r\n                        >\r\n                          <option value=\"\">Select a time</option>\r\n                          {slotsLoading ? (\r\n                            <option value=\"\" disabled>\r\n                              Loading slots...\r\n                            </option>\r\n                          ) : availableSlots.length === 0 ? (\r\n                            <option value=\"\" disabled>\r\n                              No available slots\r\n                            </option>\r\n                          ) : (\r\n                            availableSlots.map((slot) => (\r\n                              <option key={slot} value={slot.split(' - ')[0]}>\r\n                                {slot}\r\n                              </option>\r\n                            ))\r\n                          )}\r\n                        </select>\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">Type*</label>\r\n                      <select\r\n                        name=\"type\"\r\n                        value={newAppointment.type}\r\n                        onChange={handleInputChange}\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\"\r\n                        required\r\n                      >\r\n                        <option value=\"checkup\">Checkup</option>\r\n                        <option value=\"cleaning\">Cleaning</option>\r\n                        <option value=\"filling\">Filling</option>\r\n                        <option value=\"extraction\">Extraction</option>\r\n                        <option value=\"other\">Other</option>\r\n                      </select>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">Chief Complaint*</label>\r\n                      <textarea\r\n                        name=\"chiefComplaint\"\r\n                        value={newAppointment.chiefComplaint}\r\n                        onChange={handleInputChange}\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\"\r\n                        rows=\"3\"\r\n                        required\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">Notes (Optional)</label>\r\n                      <textarea\r\n                        name=\"notes\"\r\n                        value={newAppointment.notes}\r\n                        onChange={handleInputChange}\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\"\r\n                        rows=\"3\"\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">Doctor (Student ID)*</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        name=\"doctor\"\r\n                        value={newAppointment.doctor}\r\n                        onChange={handleInputChange}\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\"\r\n                        disabled\r\n                        required\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">Patient ID*</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        name=\"patient\"\r\n                        value={newAppointment.patient}\r\n                        onChange={handleInputChange}\r\n                        className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]\"\r\n                        disabled\r\n                        required\r\n                      />\r\n                    </div>\r\n                    <div className=\"flex justify-end space-x-4\">\r\n                      <motion.button\r\n                        type=\"button\"\r\n                        onClick={() => setShowAddForm(false)}\r\n                        className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 font-medium transition-colors\"\r\n                        whileHover={{ scale: 1.05 }}\r\n                        whileTap={{ scale: 0.95 }}\r\n                      >\r\n                        Cancel\r\n                      </motion.button>\r\n                      <motion.button\r\n                        type=\"submit\"\r\n                        className=\"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium transition-colors shadow-md hover:shadow-lg\"\r\n                        whileHover={{ scale: 1.05 }}\r\n                        whileTap={{ scale: 0.95 }}\r\n                        disabled={slotsLoading}\r\n                      >\r\n                        Save\r\n                      </motion.button>\r\n                    </div>\r\n                  </form>\r\n                </motion.div>\r\n              </div>\r\n            )}\r\n            {showAppointmentDetails && (\r\n              <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n                <motion.div\r\n                  initial={{ scale: 0.9, opacity: 0 }}\r\n                  animate={{ scale: 1, opacity: 1 }}\r\n                  className=\"bg-white rounded-xl w-full max-w-md max-h-[90vh] overflow-y-auto shadow-2xl border border-gray-100\"\r\n                >\r\n                  <div className=\"p-6 border-b flex justify-between items-center bg-[#0077B6]/10\">\r\n                    <h2 className=\"text-xl font-semibold text-[#0077B6]\">Appointment Details</h2>\r\n                    <button\r\n                      onClick={() => setShowAppointmentDetails(null)}\r\n                      className=\"text-gray-500 hover:text-gray-700\"\r\n                    >\r\n                      <FaTimes className=\"h-6 w-6\" />\r\n                    </button>\r\n                  </div>\r\n                  <div className=\"p-6 space-y-6\">\r\n                    <div className=\"flex justify-between items-center\">\r\n                      <h3 className=\"text-lg font-medium text-gray-800\">{showAppointmentDetails.type}</h3>\r\n                      <span\r\n                        className={`px-3 py-1 rounded-full text-sm font-medium ${\r\n                          showAppointmentDetails.status === 'completed'\r\n                            ? 'bg-[#28A745]/20 text-[#28A745]'\r\n                            : showAppointmentDetails.status === 'cancelled'\r\n                            ? 'bg-red-100 text-red-800'\r\n                            : 'bg-[#0077B6]/20 text-[#0077B6]'\r\n                        }`}\r\n                      >\r\n                        {showAppointmentDetails.status}\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"grid grid-cols-2 gap-4\">\r\n                      <div>\r\n                        <p className=\"text-sm font-medium text-gray-500\">Date</p>\r\n                        <p className=\"text-sm text-gray-900\">{formatDate(showAppointmentDetails.date)}</p>\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"text-sm font-medium text-gray-500\">Time</p>\r\n                        <p className=\"text-sm text-gray-900\">{formatTime(showAppointmentDetails.time)}</p>\r\n                      </div>\r\n                    </div>\r\n                    {showAppointmentDetails.chiefComplaint && (\r\n                      <div>\r\n                        <p className=\"text-sm font-medium text-gray-500\">Chief Complaint</p>\r\n                        <p className=\"text-sm text-gray-900\">{showAppointmentDetails.chiefComplaint}</p>\r\n                      </div>\r\n                    )}\r\n                    {showAppointmentDetails.notes && (\r\n                      <div>\r\n                        <p className=\"text-sm font-medium text-gray-500\">Notes</p>\r\n                        <p className=\"text-sm text-gray-900\">{showAppointmentDetails.notes}</p>\r\n                      </div>\r\n                    )}\r\n                    <div className=\"flex justify-end gap-3\">\r\n                      <motion.button\r\n                        whileHover={{ scale: 1.05 }}\r\n                        whileTap={{ scale: 0.95 }}\r\n                        onClick={() => deleteAppointment(showAppointmentDetails._id)}\r\n                        className=\"px-6 py-2 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-full hover:from-red-600 hover:to-red-700 font-medium transition-colors shadow-md\"\r\n                      >\r\n                        Delete\r\n                      </motion.button>\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Appointments;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AAC/E,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC,IAAI;IAAEC;EAAM,CAAC,GAAGb,OAAO,CAAC,CAAC;EACjC,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACyB,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC1E,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM;IAAEuC;EAAW,CAAC,GAAGjC,SAAS,CAAC,CAAC;EAElC,MAAM,CAACkC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAC;IACnD0C,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAER,UAAU;IACnBS,MAAM,EAAE,CAAA7B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,SAAS,KAAI,EAAE;IAC7BC,WAAW,EAAE,SAAS;IACtBC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAEF;EACAlD,SAAS,CAAC,MAAM;IACdmD,OAAO,CAACC,GAAG,CAAC,OAAO,EAAElC,IAAI,EAAE,QAAQ,EAAEC,KAAK,EAAE,aAAa,EAAEmB,UAAU,CAAC;EACxE,CAAC,EAAE,CAACpB,IAAI,EAAEC,KAAK,EAAEmB,UAAU,CAAC,CAAC;EAE7BtC,SAAS,CAAC,MAAM;IACd,MAAMqD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI;QACFF,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEd,UAAU,CAAC;QAChE,MAAMgB,QAAQ,GAAG,MAAMlD,KAAK,CAACmD,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,6BAA6BpB,UAAU,EAAE,EAAE;UAC1GqB,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAUzC,KAAK;UAAG;QAC9C,CAAC,CAAC;QACFgC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEE,QAAQ,CAACO,IAAI,CAAC;QACnDhC,eAAe,CAACyB,QAAQ,CAACO,IAAI,IAAI,EAAE,CAAC;MACtC,CAAC,CAAC,OAAOC,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZd,OAAO,CAACf,KAAK,CAAC,8BAA8B,EAAE,EAAA2B,aAAA,GAAAD,GAAG,CAACR,QAAQ,cAAAS,aAAA,uBAAZA,aAAA,CAAcF,IAAI,KAAIC,GAAG,CAACI,OAAO,CAAC;QAChF7B,QAAQ,CAAC,EAAA2B,cAAA,GAAAF,GAAG,CAACR,QAAQ,cAAAU,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcH,IAAI,cAAAI,mBAAA,uBAAlBA,mBAAA,CAAoBC,OAAO,KAAI,6BAA6B,CAAC;MACxE,CAAC,SAAS;QACR/B,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACD,IAAIhB,KAAK,IAAImB,UAAU,EAAEe,iBAAiB,CAAC,CAAC;EAC9C,CAAC,EAAE,CAACf,UAAU,EAAEnB,KAAK,CAAC,CAAC;EAEvBnB,SAAS,CAAC,MAAM;IACd,MAAMmE,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACtC,IAAI,CAAC5B,cAAc,CAACE,IAAI,IAAI,EAACvB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8B,SAAS,GAAE;QAC5CG,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE;UACvDX,IAAI,EAAEF,cAAc,CAACE,IAAI;UACzBO,SAAS,EAAE9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B;QACnB,CAAC,CAAC;QACF;MACF;MAEAf,eAAe,CAAC,IAAI,CAAC;MACrB,IAAI;QACF,MAAMmC,aAAa,GAAG,IAAIC,IAAI,CAAC9B,cAAc,CAACE,IAAI,CAAC,CAAC6B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/EpB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE;UACzCoB,QAAQ,EAAEtD,IAAI,CAAC8B,SAAS;UACxBC,WAAW,EAAE,SAAS;UACtBR,IAAI,EAAE2B;QACR,CAAC,CAAC;QAEF,MAAMd,QAAQ,GAAG,MAAMlD,KAAK,CAACmD,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,mCAAmC,EAAE;UACpGe,MAAM,EAAE;YACND,QAAQ,EAAEtD,IAAI,CAAC8B,SAAS;YACxBC,WAAW,EAAE,SAAS;YACtBR,IAAI,EAAE2B;UACR,CAAC;UACDT,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAUzC,KAAK;UAAG;QAC9C,CAAC,CAAC;QAEFgC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEE,QAAQ,CAACO,IAAI,CAAC;QAC5C9B,iBAAiB,CAACuB,QAAQ,CAACO,IAAI,IAAI,EAAE,CAAC;MACxC,CAAC,CAAC,OAAOC,GAAG,EAAE;QAAA,IAAAY,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZzB,OAAO,CAACf,KAAK,CAAC,uBAAuB,EAAE,EAAAsC,cAAA,GAAAZ,GAAG,CAACR,QAAQ,cAAAoB,cAAA,uBAAZA,cAAA,CAAcb,IAAI,KAAIC,GAAG,CAACI,OAAO,CAAC;QACzE7B,QAAQ,CAAC,EAAAsC,cAAA,GAAAb,GAAG,CAACR,QAAQ,cAAAqB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcd,IAAI,cAAAe,mBAAA,uBAAlBA,mBAAA,CAAoBV,OAAO,KAAI,gCAAgC,CAAC;QACzEnC,iBAAiB,CAAC,EAAE,CAAC;MACvB,CAAC,SAAS;QACRE,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC;IACDkC,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAAC5B,cAAc,CAACE,IAAI,EAAEvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,SAAS,EAAE7B,KAAK,CAAC,CAAC;EAEjD,MAAM0D,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACtC,cAAc,CAACE,IAAI,EAAE,OAAO,kBAAkB;IACnD,IAAI,CAACF,cAAc,CAACG,IAAI,EAAE,OAAO,kBAAkB;IACnD,IAAI,CAACH,cAAc,CAACI,IAAI,EAAE,OAAO,8BAA8B;IAC/D,IAAI,CAACJ,cAAc,CAACW,cAAc,EAAE,OAAO,6BAA6B;IACxE,IAAI,CAACX,cAAc,CAACQ,MAAM,EAAE,OAAO,uBAAuB;IAC1D,IAAI,CAACR,cAAc,CAACO,OAAO,EAAE,OAAO,wBAAwB;IAC5D,OAAO,EAAE;EACX,CAAC;EAED,MAAMgC,oBAAoB,GAAG,MAAOC,CAAC,IAAK;IACxCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,EAAC9D,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8B,SAAS,GAAE;MACpBX,QAAQ,CAAC,2DAA2D,CAAC;MACrE;IACF;IACA,MAAM4C,eAAe,GAAGJ,YAAY,CAAC,CAAC;IACtC,IAAII,eAAe,EAAE;MACnB5C,QAAQ,CAAC4C,eAAe,CAAC;MACzB;IACF;IACA,IAAI;MACF9B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEb,cAAc,CAAC;MACpD,MAAMe,QAAQ,GAAG,MAAMlD,KAAK,CAAC8E,IAAI,CAAC,GAAG1B,OAAO,CAACC,GAAG,CAACC,iBAAiB,mBAAmB,EAAEnB,cAAc,EAAE;QACrGoB,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUzC,KAAK;QAAG;MAC9C,CAAC,CAAC;MACFgC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEE,QAAQ,CAACO,IAAI,CAAC;MAClDhC,eAAe,CAAC,CAACyB,QAAQ,CAACO,IAAI,EAAE,GAAGjC,YAAY,CAAC,CAAC;MACjDY,iBAAiB,CAAC;QAChBC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,SAAS;QACjBC,OAAO,EAAER,UAAU;QACnBS,MAAM,EAAE7B,IAAI,CAAC8B,SAAS;QACtBC,WAAW,EAAE,SAAS;QACtBC,cAAc,EAAE;MAClB,CAAC,CAAC;MACF3B,cAAc,CAAC,KAAK,CAAC;MACrBc,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOyB,GAAG,EAAE;MAAA,IAAAqB,cAAA,EAAAC,cAAA,EAAAC,mBAAA;MACZlC,OAAO,CAACf,KAAK,CAAC,6BAA6B,EAAE,EAAA+C,cAAA,GAAArB,GAAG,CAACR,QAAQ,cAAA6B,cAAA,uBAAZA,cAAA,CAActB,IAAI,KAAIC,GAAG,CAACI,OAAO,CAAC;MAC/E7B,QAAQ,CAAC,EAAA+C,cAAA,GAAAtB,GAAG,CAACR,QAAQ,cAAA8B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcvB,IAAI,cAAAwB,mBAAA,uBAAlBA,mBAAA,CAAoBnB,OAAO,KAAI,2BAA2B,CAAC;IACtE;EACF,CAAC;EAED,MAAMoB,iBAAiB,GAAIP,CAAC,IAAK;IAC/B,MAAM;MAAEQ,IAAI;MAAEC;IAAM,CAAC,GAAGT,CAAC,CAACU,MAAM;IAChCtC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;MAAEmC,IAAI;MAAEC;IAAM,CAAC,CAAC;IAC9ChD,iBAAiB,CAAC;MAAE,GAAGD,cAAc;MAAE,CAACgD,IAAI,GAAGC;IAAM,CAAC,CAAC;EACzD,CAAC;EAED,MAAME,oBAAoB,GAAG9D,YAAY,CAAC+D,MAAM,CAAEC,IAAI;IAAA,IAAAC,WAAA;IAAA,OACpDD,IAAI,CAACjD,IAAI,CAACmD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrE,UAAU,CAACoE,WAAW,CAAC,CAAC,CAAC,MAAAD,WAAA,GAC1DD,IAAI,CAAChD,KAAK,cAAAiD,WAAA,uBAAVA,WAAA,CAAYC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrE,UAAU,CAACoE,WAAW,CAAC,CAAC,CAAC;EAAA,CAC9D,CAAC;EAED,MAAME,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAI5B,IAAI,CAAC4B,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtDC,OAAO,EAAE,OAAO;MAChBC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGF,UAAU,CAACjC,KAAK,CAAC,GAAG,CAAC;IAC9C,MAAMoC,IAAI,GAAGC,QAAQ,CAACH,KAAK,CAAC;IAC5B,OAAO,GAAGE,IAAI,GAAG,EAAE,GAAGA,IAAI,GAAG,EAAE,GAAGA,IAAI,IAAID,OAAO,IAAIC,IAAI,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;EACjF,CAAC;EAED,MAAME,iBAAiB,GAAG,MAAOC,EAAE,IAAK;IACtC,IAAI;MACF3D,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE0D,EAAE,CAAC;MACxC,MAAM1G,KAAK,CAAC2G,MAAM,CAAC,GAAGvD,OAAO,CAACC,GAAG,CAACC,iBAAiB,qBAAqBoD,EAAE,EAAE,EAAE;QAC5EnD,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUzC,KAAK;QAAG;MAC9C,CAAC,CAAC;MACFU,eAAe,CAACD,YAAY,CAAC+D,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACoB,GAAG,KAAKF,EAAE,CAAC,CAAC;MAC/DrF,yBAAyB,CAAC,IAAI,CAAC;IACjC,CAAC,CAAC,OAAOqC,GAAG,EAAE;MAAA,IAAAmD,cAAA,EAAAC,cAAA,EAAAC,mBAAA;MACZhE,OAAO,CAACf,KAAK,CAAC,6BAA6B,EAAE,EAAA6E,cAAA,GAAAnD,GAAG,CAACR,QAAQ,cAAA2D,cAAA,uBAAZA,cAAA,CAAcpD,IAAI,KAAIC,GAAG,CAACI,OAAO,CAAC;MAC/E7B,QAAQ,CAAC,EAAA6E,cAAA,GAAApD,GAAG,CAACR,QAAQ,cAAA4D,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcrD,IAAI,cAAAsD,mBAAA,uBAAlBA,mBAAA,CAAoBjD,OAAO,KAAI,8BAA8B,CAAC;IACzE;EACF,CAAC;;EAED;EACA,MAAMkD,SAAS,GAAG;IAChBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,IAAI,EAAE;MACJD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QACVC,eAAe,EAAE;MACnB;IACF;EACF,CAAC;EAED,MAAMC,IAAI,GAAG;IACXL,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAG,CAAC;IAC7BJ,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAE;EAC3B,CAAC;EAED,IAAI,CAACzG,IAAI,IAAI,CAACA,IAAI,CAAC8B,SAAS,EAAE;IAC5B,oBACEjC,OAAA;MAAK6G,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvC9G,OAAA,CAACb,OAAO;QAAC4H,MAAM,EAAE1G,WAAY;QAAC2G,SAAS,EAAE1G;MAAe;QAAA2G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DpH,OAAA;QAAK6G,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnD9G,OAAA,CAACd,MAAM;UAACmI,aAAa,EAAEA,CAAA,KAAM/G,cAAc,CAAC,CAACD,WAAW;QAAE;UAAA4G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DpH,OAAA;UAAM6G,SAAS,EAAC,+EAA+E;UAAAC,QAAA,eAC7F9G,OAAA;YAAK6G,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChC9G,OAAA,CAACR,MAAM,CAAC8H,GAAG;cACTC,OAAO,EAAE;gBAAEC,KAAK,EAAE,GAAG;gBAAEjB,OAAO,EAAE;cAAE,CAAE;cACpCkB,OAAO,EAAE;gBAAED,KAAK,EAAE,CAAC;gBAAEjB,OAAO,EAAE;cAAE,CAAE;cAClCM,SAAS,EAAC,+EAA+E;cAAAC,QAAA,gBAEzF9G,OAAA;gBAAK6G,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,eAClC9G,OAAA;kBACE0H,KAAK,EAAC,4BAA4B;kBAClCb,SAAS,EAAC,mBAAmB;kBAC7Bc,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnBC,MAAM,EAAC,cAAc;kBAAAf,QAAA,eAErB9G,OAAA;oBACE8H,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC,OAAO;oBACtBC,WAAW,EAAE,CAAE;oBACfC,CAAC,EAAC;kBAAsI;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpH,OAAA;gBAAI6G,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvEpH,OAAA;gBAAG6G,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAA6C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIjG,OAAO,EAAE;IACX,oBAAOnB,OAAA,CAACF,MAAM;MAAAmH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnB;EAEA,oBACEpH,OAAA;IAAK6G,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvC9G,OAAA,CAACb,OAAO;MAAC4H,MAAM,EAAE1G,WAAY;MAAC2G,SAAS,EAAE1G;IAAe;MAAA2G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC3DpH,OAAA;MAAK6G,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnD9G,OAAA,CAACd,MAAM;QAACmI,aAAa,EAAEA,CAAA,KAAM/G,cAAc,CAAC,CAACD,WAAW;MAAE;QAAA4G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7DpH,OAAA,CAACZ,UAAU;QAAA6H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACdpH,OAAA;QAAM6G,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAC7F9G,OAAA;UAAK6G,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAC/BzF,KAAK,iBACJrB,OAAA,CAACR,MAAM,CAAC8H,GAAG;YACTC,OAAO,EAAE;cAAEhB,OAAO,EAAE;YAAE,CAAE;YACxBkB,OAAO,EAAE;cAAElB,OAAO,EAAE;YAAE,CAAE;YACxBM,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAExDzF;UAAK;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eACDpH,OAAA,CAACR,MAAM,CAAC8H,GAAG;YACTC,OAAO,EAAE;cAAEhB,OAAO,EAAE;YAAE,CAAE;YACxBkB,OAAO,EAAE;cAAElB,OAAO,EAAE;YAAE,CAAE;YACxBE,UAAU,EAAE;cAAEyB,QAAQ,EAAE;YAAI,CAAE;YAAApB,QAAA,gBAE9B9G,OAAA;cAAK6G,SAAS,EAAC,kFAAkF;cAAAC,QAAA,gBAC/F9G,OAAA;gBAAA8G,QAAA,gBACE9G,OAAA;kBAAI6G,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAAC;gBAEnE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLpH,OAAA;kBAAG6G,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAA2B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACNpH,OAAA,CAACR,MAAM,CAAC2I,MAAM;gBACZC,UAAU,EAAE;kBAAEZ,KAAK,EAAE;gBAAK,CAAE;gBAC5Ba,QAAQ,EAAE;kBAAEb,KAAK,EAAE;gBAAK,CAAE;gBAC1Bc,OAAO,EAAEA,CAAA,KAAM9H,cAAc,CAAC,IAAI,CAAE;gBACpCqG,SAAS,EAAC,oPAAoP;gBAAAC,QAAA,gBAE9P9G,OAAA,CAACP,MAAM;kBAACoH,SAAS,EAAC;gBAAc;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,mBAErC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACNpH,OAAA,CAACR,MAAM,CAAC8H,GAAG;cACTc,UAAU,EAAE;gBAAEZ,KAAK,EAAE;cAAK,CAAE;cAC5BX,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAEzB9G,OAAA;gBAAK6G,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,eACnF9G,OAAA,CAACN,QAAQ;kBAACmH,SAAS,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACNpH,OAAA;gBACE4B,IAAI,EAAC,MAAM;gBACX2G,WAAW,EAAC,wBAAwB;gBACpC9D,KAAK,EAAE9D,UAAW;gBAClB6H,QAAQ,EAAGxE,CAAC,IAAKpD,aAAa,CAACoD,CAAC,CAACU,MAAM,CAACD,KAAK,CAAE;gBAC/CoC,SAAS,EAAC;cAAuN;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eACbpH,OAAA,CAACR,MAAM,CAAC8H,GAAG;cACTmB,QAAQ,EAAEpC,SAAU;cACpBkB,OAAO,EAAC,QAAQ;cAChBE,OAAO,EAAC,MAAM;cACdZ,SAAS,EAAC,sEAAsE;cAAAC,QAAA,EAE/EnC,oBAAoB,CAAC+D,MAAM,KAAK,CAAC,gBAChC1I,OAAA,CAACR,MAAM,CAAC8H,GAAG;gBACTC,OAAO,EAAE;kBAAEhB,OAAO,EAAE;gBAAE,CAAE;gBACxBkB,OAAO,EAAE;kBAAElB,OAAO,EAAE;gBAAE,CAAE;gBACxBM,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAE3B9G,OAAA;kBAAK6G,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,eACnD9G,OAAA;oBACE0H,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBC,MAAM,EAAC,cAAc;oBAAAf,QAAA,eAErB9G,OAAA;sBACE8H,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfC,CAAC,EAAC;oBAAoF;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNpH,OAAA;kBAAI6G,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/EpH,OAAA;kBAAG6G,SAAS,EAAC,eAAe;kBAAAC,QAAA,EACzBnG,UAAU,GAAG,6BAA6B,GAAG;gBAAyC;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CAAC,eACJpH,OAAA,CAACR,MAAM,CAAC2I,MAAM;kBACZC,UAAU,EAAE;oBAAEZ,KAAK,EAAE;kBAAK,CAAE;kBAC5Ba,QAAQ,EAAE;oBAAEb,KAAK,EAAE;kBAAK,CAAE;kBAC1Bc,OAAO,EAAEA,CAAA,KAAM9H,cAAc,CAAC,IAAI,CAAE;kBACpCqG,SAAS,EAAC,kMAAkM;kBAAAC,QAAA,gBAE5M9G,OAAA,CAACP,MAAM;oBAACoH,SAAS,EAAC;kBAAc;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,mBAErC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,gBAEbpH,OAAA;gBAAI6G,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EACrCnC,oBAAoB,CAACgE,GAAG,CAAE9D,IAAI,iBAC7B7E,OAAA,CAACR,MAAM,CAACoJ,EAAE;kBAERH,QAAQ,EAAE9B,IAAK;kBACfE,SAAS,EAAC,mDAAmD;kBAC7DyB,OAAO,EAAEA,CAAA,KAAM5H,yBAAyB,CAACmE,IAAI,CAAE;kBAAAiC,QAAA,eAE/C9G,OAAA;oBAAK6G,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxB9G,OAAA;sBAAK6G,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,gBAChD9G,OAAA;wBAAK6G,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtC9G,OAAA;0BACE6G,SAAS,EAAE,wBACThC,IAAI,CAAC/C,MAAM,KAAK,WAAW,GACvB,cAAc,GACd+C,IAAI,CAAC/C,MAAM,KAAK,WAAW,GAC3B,YAAY,GACZ,cAAc;wBACjB;0BAAAmF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACPpH,OAAA;0BAAI6G,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAEjC,IAAI,CAACjD;wBAAI;0BAAAqF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/D,CAAC,eACNpH,OAAA;wBAAK6G,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAE7B,UAAU,CAACJ,IAAI,CAACnD,IAAI;sBAAC;wBAAAuF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjE,CAAC,eACNpH,OAAA;sBAAK6G,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,eACrD9G,OAAA;wBAAG6G,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,gBAClC9G,OAAA;0BAAM6G,SAAS,EAAC,aAAa;0BAAAC,QAAA,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,KAAC,EAAC5B,UAAU,CAACX,IAAI,CAAClD,IAAI,CAAC;sBAAA;wBAAAsF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GA1BDvC,IAAI,CAACoB,GAAG;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA2BJ,CACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YACL;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACZ7G,WAAW,iBACVP,OAAA;YAAK6G,SAAS,EAAC,gFAAgF;YAAAC,QAAA,eAC7F9G,OAAA,CAACR,MAAM,CAAC8H,GAAG;cACTC,OAAO,EAAE;gBAAEC,KAAK,EAAE,GAAG;gBAAEjB,OAAO,EAAE;cAAE,CAAE;cACpCkB,OAAO,EAAE;gBAAED,KAAK,EAAE,CAAC;gBAAEjB,OAAO,EAAE;cAAE,CAAE;cAClCM,SAAS,EAAC,oGAAoG;cAAAC,QAAA,gBAE9G9G,OAAA;gBAAK6G,SAAS,EAAC,gEAAgE;gBAAAC,QAAA,gBAC7E9G,OAAA;kBAAI6G,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzEpH,OAAA;kBACEsI,OAAO,EAAEA,CAAA,KAAM9H,cAAc,CAAC,KAAK,CAAE;kBACrCqG,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,eAE7C9G,OAAA,CAACH,OAAO;oBAACgH,SAAS,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNpH,OAAA;gBAAM6I,QAAQ,EAAE9E,oBAAqB;gBAAC8C,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC7D9G,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAO6G,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/EpH,OAAA;oBAAK6G,SAAS,EAAC,UAAU;oBAAAC,QAAA,gBACvB9G,OAAA,CAACL,UAAU;sBAACkH,SAAS,EAAC;oBAAkE;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC3FpH,OAAA;sBACE4B,IAAI,EAAC,MAAM;sBACX4C,IAAI,EAAC,MAAM;sBACXC,KAAK,EAAEjD,cAAc,CAACE,IAAK;sBAC3B8G,QAAQ,EAAEjE,iBAAkB;sBAC5BsC,SAAS,EAAC,mHAAmH;sBAC7HiC,QAAQ;oBAAA;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNpH,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAO6G,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/EpH,OAAA;oBAAK6G,SAAS,EAAC,UAAU;oBAAAC,QAAA,gBACvB9G,OAAA,CAACJ,OAAO;sBAACiH,SAAS,EAAC;oBAAkE;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACxFpH,OAAA;sBACEwE,IAAI,EAAC,MAAM;sBACXC,KAAK,EAAEjD,cAAc,CAACG,IAAK;sBAC3B6G,QAAQ,EAAEjE,iBAAkB;sBAC5BsC,SAAS,EAAC,mHAAmH;sBAC7HiC,QAAQ;sBACRC,QAAQ,EAAE9H,YAAa;sBAAA6F,QAAA,gBAEvB9G,OAAA;wBAAQyE,KAAK,EAAC,EAAE;wBAAAqC,QAAA,EAAC;sBAAa;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,EACtCnG,YAAY,gBACXjB,OAAA;wBAAQyE,KAAK,EAAC,EAAE;wBAACsE,QAAQ;wBAAAjC,QAAA,EAAC;sBAE1B;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,GACPrG,cAAc,CAAC2H,MAAM,KAAK,CAAC,gBAC7B1I,OAAA;wBAAQyE,KAAK,EAAC,EAAE;wBAACsE,QAAQ;wBAAAjC,QAAA,EAAC;sBAE1B;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,GAETrG,cAAc,CAAC4H,GAAG,CAAEK,IAAI,iBACtBhJ,OAAA;wBAAmByE,KAAK,EAAEuE,IAAI,CAACxF,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAE;wBAAAsD,QAAA,EAC5CkC;sBAAI,GADMA,IAAI;wBAAA/B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAET,CACT,CACF;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNpH,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAO6G,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/EpH,OAAA;oBACEwE,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAEjD,cAAc,CAACI,IAAK;oBAC3B4G,QAAQ,EAAEjE,iBAAkB;oBAC5BsC,SAAS,EAAC,6GAA6G;oBACvHiC,QAAQ;oBAAAhC,QAAA,gBAER9G,OAAA;sBAAQyE,KAAK,EAAC,SAAS;sBAAAqC,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxCpH,OAAA;sBAAQyE,KAAK,EAAC,UAAU;sBAAAqC,QAAA,EAAC;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC1CpH,OAAA;sBAAQyE,KAAK,EAAC,SAAS;sBAAAqC,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxCpH,OAAA;sBAAQyE,KAAK,EAAC,YAAY;sBAAAqC,QAAA,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9CpH,OAAA;sBAAQyE,KAAK,EAAC,OAAO;sBAAAqC,QAAA,EAAC;oBAAK;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNpH,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAO6G,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC1FpH,OAAA;oBACEwE,IAAI,EAAC,gBAAgB;oBACrBC,KAAK,EAAEjD,cAAc,CAACW,cAAe;oBACrCqG,QAAQ,EAAEjE,iBAAkB;oBAC5BsC,SAAS,EAAC,6GAA6G;oBACvHoC,IAAI,EAAC,GAAG;oBACRH,QAAQ;kBAAA;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNpH,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAO6G,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC1FpH,OAAA;oBACEwE,IAAI,EAAC,OAAO;oBACZC,KAAK,EAAEjD,cAAc,CAACK,KAAM;oBAC5B2G,QAAQ,EAAEjE,iBAAkB;oBAC5BsC,SAAS,EAAC,6GAA6G;oBACvHoC,IAAI,EAAC;kBAAG;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNpH,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAO6G,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9FpH,OAAA;oBACE4B,IAAI,EAAC,MAAM;oBACX4C,IAAI,EAAC,QAAQ;oBACbC,KAAK,EAAEjD,cAAc,CAACQ,MAAO;oBAC7BwG,QAAQ,EAAEjE,iBAAkB;oBAC5BsC,SAAS,EAAC,6GAA6G;oBACvHkC,QAAQ;oBACRD,QAAQ;kBAAA;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNpH,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAO6G,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,EAAC;kBAAW;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACrFpH,OAAA;oBACE4B,IAAI,EAAC,MAAM;oBACX4C,IAAI,EAAC,SAAS;oBACdC,KAAK,EAAEjD,cAAc,CAACO,OAAQ;oBAC9ByG,QAAQ,EAAEjE,iBAAkB;oBAC5BsC,SAAS,EAAC,6GAA6G;oBACvHkC,QAAQ;oBACRD,QAAQ;kBAAA;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNpH,OAAA;kBAAK6G,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzC9G,OAAA,CAACR,MAAM,CAAC2I,MAAM;oBACZvG,IAAI,EAAC,QAAQ;oBACb0G,OAAO,EAAEA,CAAA,KAAM9H,cAAc,CAAC,KAAK,CAAE;oBACrCqG,SAAS,EAAC,4GAA4G;oBACtHuB,UAAU,EAAE;sBAAEZ,KAAK,EAAE;oBAAK,CAAE;oBAC5Ba,QAAQ,EAAE;sBAAEb,KAAK,EAAE;oBAAK,CAAE;oBAAAV,QAAA,EAC3B;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe,CAAC,eAChBpH,OAAA,CAACR,MAAM,CAAC2I,MAAM;oBACZvG,IAAI,EAAC,QAAQ;oBACbiF,SAAS,EAAC,sLAAsL;oBAChMuB,UAAU,EAAE;sBAAEZ,KAAK,EAAE;oBAAK,CAAE;oBAC5Ba,QAAQ,EAAE;sBAAEb,KAAK,EAAE;oBAAK,CAAE;oBAC1BuB,QAAQ,EAAE9H,YAAa;oBAAA6F,QAAA,EACxB;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN,EACA3G,sBAAsB,iBACrBT,OAAA;YAAK6G,SAAS,EAAC,gFAAgF;YAAAC,QAAA,eAC7F9G,OAAA,CAACR,MAAM,CAAC8H,GAAG;cACTC,OAAO,EAAE;gBAAEC,KAAK,EAAE,GAAG;gBAAEjB,OAAO,EAAE;cAAE,CAAE;cACpCkB,OAAO,EAAE;gBAAED,KAAK,EAAE,CAAC;gBAAEjB,OAAO,EAAE;cAAE,CAAE;cAClCM,SAAS,EAAC,oGAAoG;cAAAC,QAAA,gBAE9G9G,OAAA;gBAAK6G,SAAS,EAAC,gEAAgE;gBAAAC,QAAA,gBAC7E9G,OAAA;kBAAI6G,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7EpH,OAAA;kBACEsI,OAAO,EAAEA,CAAA,KAAM5H,yBAAyB,CAAC,IAAI,CAAE;kBAC/CmG,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,eAE7C9G,OAAA,CAACH,OAAO;oBAACgH,SAAS,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNpH,OAAA;gBAAK6G,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B9G,OAAA;kBAAK6G,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD9G,OAAA;oBAAI6G,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAErG,sBAAsB,CAACmB;kBAAI;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpFpH,OAAA;oBACE6G,SAAS,EAAE,8CACTpG,sBAAsB,CAACqB,MAAM,KAAK,WAAW,GACzC,gCAAgC,GAChCrB,sBAAsB,CAACqB,MAAM,KAAK,WAAW,GAC7C,yBAAyB,GACzB,gCAAgC,EACnC;oBAAAgF,QAAA,EAEFrG,sBAAsB,CAACqB;kBAAM;oBAAAmF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNpH,OAAA;kBAAK6G,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrC9G,OAAA;oBAAA8G,QAAA,gBACE9G,OAAA;sBAAG6G,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACzDpH,OAAA;sBAAG6G,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAE7B,UAAU,CAACxE,sBAAsB,CAACiB,IAAI;oBAAC;sBAAAuF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC,eACNpH,OAAA;oBAAA8G,QAAA,gBACE9G,OAAA;sBAAG6G,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACzDpH,OAAA;sBAAG6G,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEtB,UAAU,CAAC/E,sBAAsB,CAACkB,IAAI;oBAAC;sBAAAsF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EACL3G,sBAAsB,CAAC0B,cAAc,iBACpCnC,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAG6G,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACpEpH,OAAA;oBAAG6G,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAErG,sBAAsB,CAAC0B;kBAAc;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CACN,EACA3G,sBAAsB,CAACoB,KAAK,iBAC3B7B,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAG6G,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC1DpH,OAAA;oBAAG6G,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAErG,sBAAsB,CAACoB;kBAAK;oBAAAoF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CACN,eACDpH,OAAA;kBAAK6G,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,eACrC9G,OAAA,CAACR,MAAM,CAAC2I,MAAM;oBACZC,UAAU,EAAE;sBAAEZ,KAAK,EAAE;oBAAK,CAAE;oBAC5Ba,QAAQ,EAAE;sBAAEb,KAAK,EAAE;oBAAK,CAAE;oBAC1Bc,OAAO,EAAEA,CAAA,KAAMxC,iBAAiB,CAACrF,sBAAsB,CAACwF,GAAG,CAAE;oBAC7DY,SAAS,EAAC,wJAAwJ;oBAAAC,QAAA,EACnK;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClH,EAAA,CA3kBID,YAAY;EAAA,QACQV,OAAO,EAURD,SAAS;AAAA;AAAA4J,EAAA,GAX5BjJ,YAAY;AA6kBlB,eAAeA,YAAY;AAAC,IAAAiJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}