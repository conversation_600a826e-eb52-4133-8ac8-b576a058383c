{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\student\\\\Sheets.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useAuth } from '../context/AuthContext';\nimport Sidebar from './Sidebar';\nimport Navbar from './Navbar';\nimport PatientNav from './PatientNav';\nimport FixedProsthodonticsSheet from './FixedProsthodonticsSheet';\nimport OperativeSheet from './OperativeSheet';\nimport EndodonticSheet from './EndodonticSheet';\nimport RemovableProsthodonticsSheet from './RemovableProsthodonticsSheet';\nimport PeriodonticsSheet from './PeriodonticsSheet';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sheets = ({\n  selectedChart,\n  setSelectedChart,\n  charts\n}) => {\n  _s();\n  const {\n    nationalId\n  } = useParams();\n  const {\n    token\n  } = useAuth();\n  const navigate = useNavigate();\n  const [sheet, setSheet] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedSheetType, setSelectedSheetType] = useState('');\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [showSuccessPopup, setShowSuccessPopup] = useState(false);\n  const [savedSheetId, setSavedSheetId] = useState(null);\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n\n  // Available sheet types\n  const sheetTypes = ['Operative', 'Fixed Prosthodontics', 'Removable Prosthodontics', 'Endodontics', 'Periodontics'];\n\n  // Procedure Selection Component\n  const ProcedureSelection = () => {\n    // Determine background color based on selected sheet type\n    let bgColor = \"bg-white\";\n    let borderColor = \"\";\n    if (selectedSheetType) {\n      switch (selectedSheetType) {\n        case 'Operative':\n          bgColor = \"bg-[#0077B6]/10\";\n          borderColor = \"border-[#0077B6]/30\";\n          break;\n        case 'Fixed Prosthodontics':\n          bgColor = \"bg-[#20B2AA]/10\";\n          borderColor = \"border-[#20B2AA]/30\";\n          break;\n        case 'Removable Prosthodontics':\n          bgColor = \"bg-[#28A745]/10\";\n          borderColor = \"border-[#28A745]/30\";\n          break;\n        case 'Endodontics':\n          bgColor = \"bg-[#0077B6]/10\";\n          borderColor = \"border-[#0077B6]/30\";\n          break;\n        case 'Periodontics':\n          bgColor = \"bg-[#20B2AA]/10\";\n          borderColor = \"border-[#20B2AA]/30\";\n          break;\n        default:\n          bgColor = \"bg-white\";\n      }\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${bgColor} ${borderColor} border rounded-xl shadow-md p-4 mb-6 transition-all duration-300`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 md:mb-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-gray-800\",\n            children: selectedSheetType ? `Current Procedure: ${selectedSheetType}` : 'Select Procedure Type'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600\",\n            children: selectedSheetType ? 'You can change the procedure type using the dropdown' : 'Choose the procedure type for this treatment sheet'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative w-full md:w-64\",\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedSheetType || '',\n            onChange: handleSheetTypeChange,\n            className: \"block w-full px-4 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6] font-medium shadow-md appearance-none pr-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              className: \"bg-white text-gray-800\",\n              children: \"Select Procedure Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), sheetTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: type,\n              className: \"bg-white text-gray-800\",\n              children: type\n            }, type, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"fill-current h-5 w-5\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              viewBox: \"0 0 20 20\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Initialize selectedSheetType from localStorage if available\n  useEffect(() => {\n    // Clear any previously saved sheet type to ensure fresh selection\n    localStorage.removeItem('selectedSheetType');\n    setSelectedSheetType('');\n  }, []);\n\n  // Handle sheet type change\n  const handleSheetTypeChange = e => {\n    const newSheetType = e.target.value;\n\n    // Save to localStorage for persistence across components\n    localStorage.setItem('selectedSheetType', newSheetType);\n    setSelectedSheetType(newSheetType);\n  };\n  useEffect(() => {\n    // If no sheet type is selected, don't do anything\n    if (!selectedSheetType) {\n      setLoading(false);\n      setSheet(null);\n      return;\n    }\n\n    // When a sheet type is selected, create a new empty sheet of that type\n    setLoading(true);\n\n    // Create a new sheet object with the selected type\n    const newSheet = {\n      type: selectedSheetType,\n      details: {\n        diagnosis: '',\n        treatmentPlan: '',\n        notes: '',\n        specificData: {}\n      },\n      _id: `new-${Date.now()}`,\n      // Temporary ID for new sheet\n      createdAt: new Date().toISOString()\n    };\n    console.log(`Creating new sheet of type: ${selectedSheetType}`);\n    setSheet(newSheet);\n    setError('');\n    setLoading(false);\n  }, [selectedSheetType]);\n\n  // Redirect to history after successful save\n  useEffect(() => {\n    if (showSuccessPopup) {\n      const timer = setTimeout(() => {\n        navigate(`/patientprofile/${nationalId}/history`);\n      }, 2000);\n      return () => clearTimeout(timer);\n    }\n  }, [showSuccessPopup, navigate, nationalId]);\n\n  // Function to save sheet data to the database\n  const handleSaveSheet = async (sheetData, diagnosis, treatmentPlan, notes) => {\n    try {\n      setLoading(true);\n      const sheetToSave = {\n        type: sheet.type,\n        diagnosis: diagnosis || '',\n        treatmentPlan: treatmentPlan || '',\n        notes: notes || '',\n        specificData: sheetData\n      };\n      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/patients/${nationalId}/treatment-sheets`, sheetToSave, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      console.log('Sheet saved successfully:', response.data);\n      setSavedSheetId(response.data.sheet._id);\n      setShowSuccessPopup(true);\n      setLoading(false);\n      return true;\n    } catch (err) {\n      var _err$response, _err$response2, _err$response2$data;\n      console.error('Error saving sheet:', (_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data);\n      setError(((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.message) || 'Failed to save sheet');\n      setLoading(false);\n      return false;\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n        isOpen: sidebarOpen,\n        setIsOpen: setSidebarOpen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex flex-col overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(Navbar, {\n          toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(PatientNav, {\n            selectedChart: selectedChart,\n            setSelectedChart: setSelectedChart,\n            charts: charts\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 overflow-y-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-pulse space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-8 bg-gray-200 rounded w-1/4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-4 bg-gray-200 rounded w-3/4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-4 bg-gray-200 rounded w-1/2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this);\n  }\n  if (!selectedSheetType) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n        isOpen: sidebarOpen,\n        setIsOpen: setSidebarOpen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex flex-col overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(Navbar, {\n          toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(PatientNav, {\n            selectedChart: selectedChart,\n            setSelectedChart: setSelectedChart,\n            charts: charts\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 overflow-y-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(ProcedureSelection, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-xl shadow-md p-6 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-800 mb-4\",\n                children: \"Create New Treatment Sheet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: \"Select a procedure type above to create a new treatment sheet for this patient.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-[#0077B6]/10 border border-[#0077B6]/30 rounded-lg p-4 text-[#0077B6] text-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  className: \"h-5 w-5 mr-2 mt-0.5 text-[#0077B6]\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"The sheet will be saved to the patient's history after completion.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !sheet) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n        isOpen: sidebarOpen,\n        setIsOpen: setSidebarOpen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex flex-col overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(Navbar, {\n          toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(PatientNav, {\n            selectedChart: selectedChart,\n            setSelectedChart: setSelectedChart,\n            charts: charts\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 overflow-y-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(ProcedureSelection, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 bg-red-100 text-[#333333] rounded-lg\",\n              children: error || `No ${selectedSheetType} sheet found. Please select a different sheet type.`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-shrink-0\",\n        children: /*#__PURE__*/_jsxDEV(PatientNav, {\n          selectedChart: selectedChart,\n          setSelectedChart: setSelectedChart,\n          charts: charts\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6 overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-6xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(ProcedureSelection, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: ((_sheet$details, _sheet$details$specif, _sheet$details2) => {\n              const sheetProps = {\n                uniqueId: ((_sheet$details = sheet.details) === null || _sheet$details === void 0 ? void 0 : (_sheet$details$specif = _sheet$details.specificData) === null || _sheet$details$specif === void 0 ? void 0 : _sheet$details$specif.uniqueId) || sheet._id || '',\n                initialData: ((_sheet$details2 = sheet.details) === null || _sheet$details2 === void 0 ? void 0 : _sheet$details2.specificData) || {},\n                onSave: handleSaveSheet\n              };\n              switch (sheet.type) {\n                case 'Fixed Prosthodontics':\n                  return /*#__PURE__*/_jsxDEV(FixedProsthodonticsSheet, {\n                    ...sheetProps\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 28\n                  }, this);\n                case 'Operative':\n                  return /*#__PURE__*/_jsxDEV(OperativeSheet, {\n                    ...sheetProps\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 28\n                  }, this);\n                case 'Endodontics':\n                  return /*#__PURE__*/_jsxDEV(EndodonticSheet, {\n                    ...sheetProps\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 28\n                  }, this);\n                case 'Removable Prosthodontics':\n                  return /*#__PURE__*/_jsxDEV(RemovableProsthodonticsSheet, {\n                    ...sheetProps\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 28\n                  }, this);\n                case 'Periodontics':\n                  return /*#__PURE__*/_jsxDEV(PeriodonticsSheet, {\n                    ...sheetProps\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 28\n                  }, this);\n                default:\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-6 bg-white rounded-lg shadow\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-xl font-semibold text-gray-800 mb-4\",\n                      children: [sheet.type, \" Sheet\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 338,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600\",\n                      children: \"This sheet type is not yet implemented.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 339,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n                      className: \"mt-4 p-4 bg-gray-100 rounded text-xs overflow-auto\",\n                      children: JSON.stringify(sheet, null, 2)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 340,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 23\n                  }, this);\n              }\n            })()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: showSuccessPopup && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.8\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        exit: {\n          opacity: 0,\n          scale: 0.8\n        },\n        className: \"fixed inset-0 flex items-center justify-center z-50\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-black bg-opacity-50\",\n          onClick: () => setShowSuccessPopup(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-xl p-6 m-4 max-w-sm w-full relative z-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-[#28A745]/20 rounded-full p-2\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-8 w-8 text-[#28A745]\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M5 13l4 4L19 7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-center text-gray-800 mb-2\",\n            children: \"Sheet Saved Successfully!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-center text-gray-600 mb-4\",\n            children: [\"Your \", sheet.type, \" sheet has been saved.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500\",\n              children: \"Redirecting to history...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 297,\n    columnNumber: 5\n  }, this);\n};\n_s(Sheets, \"5XJUURhYgUlDcca5LN1uw2naGY4=\", false, function () {\n  return [useParams, useAuth, useNavigate];\n});\n_c = Sheets;\nexport default Sheets;\nvar _c;\n$RefreshReg$(_c, \"Sheets\");", "map": {"version": 3, "names": ["useState", "useEffect", "useParams", "useNavigate", "axios", "motion", "AnimatePresence", "useAuth", "Sidebar", "<PERSON><PERSON><PERSON>", "PatientNav", "FixedProsthodonticsSheet", "OperativeSheet", "EndodonticSheet", "RemovableProsthodonticsSheet", "PeriodonticsSheet", "jsxDEV", "_jsxDEV", "Sheets", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>hart", "charts", "_s", "nationalId", "token", "navigate", "sheet", "setSheet", "loading", "setLoading", "error", "setError", "selectedSheetType", "setSelectedSheetType", "sidebarOpen", "setSidebarOpen", "showSuccessPopup", "setShowSuccessPopup", "savedSheetId", "setSavedSheetId", "today", "Date", "setHours", "sheetTypes", "ProcedureSelection", "bgColor", "borderColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "handleSheetTypeChange", "map", "type", "xmlns", "viewBox", "d", "localStorage", "removeItem", "e", "newSheetType", "target", "setItem", "newSheet", "details", "diagnosis", "treatmentPlan", "notes", "specificData", "_id", "now", "createdAt", "toISOString", "console", "log", "timer", "setTimeout", "clearTimeout", "handleSaveSheet", "sheetData", "sheetToSave", "response", "post", "process", "env", "REACT_APP_API_URL", "headers", "Authorization", "data", "err", "_err$response", "_err$response2", "_err$response2$data", "message", "isOpen", "setIsOpen", "toggleSidebar", "fill", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "div", "initial", "opacity", "y", "animate", "transition", "duration", "_sheet$details", "_sheet$details$specif", "_sheet$details2", "sheetProps", "uniqueId", "initialData", "onSave", "JSON", "stringify", "scale", "exit", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/student/Sheets.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport { useParams, useNavigate } from 'react-router-dom';\r\nimport axios from 'axios';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { useAuth } from '../context/AuthContext';\r\nimport Sidebar from './Sidebar';\r\nimport Navbar from './Navbar';\r\nimport PatientNav from './PatientNav';\r\nimport FixedProsthodonticsSheet from './FixedProsthodonticsSheet';\r\nimport OperativeSheet from './OperativeSheet';\r\nimport EndodonticSheet from './EndodonticSheet';\r\nimport RemovableProsthodonticsSheet from './RemovableProsthodonticsSheet';\r\nimport PeriodonticsSheet from './PeriodonticsSheet';\r\n\r\nconst Sheets = ({ selectedChart, setSelectedChart, charts }) => {\r\n  const { nationalId } = useParams();\r\n  const { token } = useAuth();\r\n  const navigate = useNavigate();\r\n  const [sheet, setSheet] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [selectedSheetType, setSelectedSheetType] = useState('');\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const [showSuccessPopup, setShowSuccessPopup] = useState(false);\r\n  const [savedSheetId, setSavedSheetId] = useState(null);\r\n  const today = new Date();\r\n  today.setHours(0, 0, 0, 0);\r\n\r\n  // Available sheet types\r\n  const sheetTypes = [\r\n    'Operative',\r\n    'Fixed Prosthodontics',\r\n    'Removable Prosthodontics',\r\n    'Endodontics',\r\n    'Periodontics',\r\n  ];\r\n\r\n  // Procedure Selection Component\r\n  const ProcedureSelection = () => {\r\n    // Determine background color based on selected sheet type\r\n    let bgColor = \"bg-white\";\r\n    let borderColor = \"\";\r\n\r\n    if (selectedSheetType) {\r\n      switch(selectedSheetType) {\r\n        case 'Operative':\r\n          bgColor = \"bg-[#0077B6]/10\";\r\n          borderColor = \"border-[#0077B6]/30\";\r\n          break;\r\n        case 'Fixed Prosthodontics':\r\n          bgColor = \"bg-[#20B2AA]/10\";\r\n          borderColor = \"border-[#20B2AA]/30\";\r\n          break;\r\n        case 'Removable Prosthodontics':\r\n          bgColor = \"bg-[#28A745]/10\";\r\n          borderColor = \"border-[#28A745]/30\";\r\n          break;\r\n        case 'Endodontics':\r\n          bgColor = \"bg-[#0077B6]/10\";\r\n          borderColor = \"border-[#0077B6]/30\";\r\n          break;\r\n        case 'Periodontics':\r\n          bgColor = \"bg-[#20B2AA]/10\";\r\n          borderColor = \"border-[#20B2AA]/30\";\r\n          break;\r\n        default:\r\n          bgColor = \"bg-white\";\r\n      }\r\n    }\r\n\r\n    return (\r\n      <div className={`${bgColor} ${borderColor} border rounded-xl shadow-md p-4 mb-6 transition-all duration-300`}>\r\n        <div className=\"flex flex-col md:flex-row items-center justify-between\">\r\n          <div className=\"mb-4 md:mb-0\">\r\n            <h2 className=\"text-lg font-semibold text-gray-800\">\r\n              {selectedSheetType ? `Current Procedure: ${selectedSheetType}` : 'Select Procedure Type'}\r\n            </h2>\r\n            <p className=\"text-sm text-gray-600\">\r\n              {selectedSheetType\r\n                ? 'You can change the procedure type using the dropdown'\r\n                : 'Choose the procedure type for this treatment sheet'}\r\n            </p>\r\n          </div>\r\n          <div className=\"relative w-full md:w-64\">\r\n            <select\r\n              value={selectedSheetType || ''}\r\n              onChange={handleSheetTypeChange}\r\n              className=\"block w-full px-4 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6] font-medium shadow-md appearance-none pr-10\"\r\n            >\r\n              <option value=\"\" className=\"bg-white text-gray-800\">Select Procedure Type</option>\r\n              {sheetTypes.map((type) => (\r\n                <option key={type} value={type} className=\"bg-white text-gray-800\">\r\n                  {type}\r\n                </option>\r\n              ))}\r\n            </select>\r\n            <div className=\"pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-white\">\r\n              <svg className=\"fill-current h-5 w-5\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\">\r\n                <path d=\"M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z\" />\r\n              </svg>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Initialize selectedSheetType from localStorage if available\r\n  useEffect(() => {\r\n    // Clear any previously saved sheet type to ensure fresh selection\r\n    localStorage.removeItem('selectedSheetType');\r\n    setSelectedSheetType('');\r\n  }, []);\r\n\r\n  // Handle sheet type change\r\n  const handleSheetTypeChange = (e) => {\r\n    const newSheetType = e.target.value;\r\n\r\n    // Save to localStorage for persistence across components\r\n    localStorage.setItem('selectedSheetType', newSheetType);\r\n    setSelectedSheetType(newSheetType);\r\n  };\r\n\r\n  useEffect(() => {\r\n    // If no sheet type is selected, don't do anything\r\n    if (!selectedSheetType) {\r\n      setLoading(false);\r\n      setSheet(null);\r\n      return;\r\n    }\r\n\r\n    // When a sheet type is selected, create a new empty sheet of that type\r\n    setLoading(true);\r\n\r\n    // Create a new sheet object with the selected type\r\n    const newSheet = {\r\n      type: selectedSheetType,\r\n      details: {\r\n        diagnosis: '',\r\n        treatmentPlan: '',\r\n        notes: '',\r\n        specificData: {}\r\n      },\r\n      _id: `new-${Date.now()}`, // Temporary ID for new sheet\r\n      createdAt: new Date().toISOString()\r\n    };\r\n\r\n    console.log(`Creating new sheet of type: ${selectedSheetType}`);\r\n    setSheet(newSheet);\r\n    setError('');\r\n    setLoading(false);\r\n\r\n  }, [selectedSheetType]);\r\n\r\n  // Redirect to history after successful save\r\n  useEffect(() => {\r\n    if (showSuccessPopup) {\r\n      const timer = setTimeout(() => {\r\n        navigate(`/patientprofile/${nationalId}/history`);\r\n      }, 2000);\r\n\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [showSuccessPopup, navigate, nationalId]);\r\n\r\n  // Function to save sheet data to the database\r\n  const handleSaveSheet = async (sheetData, diagnosis, treatmentPlan, notes) => {\r\n    try {\r\n      setLoading(true);\r\n\r\n      const sheetToSave = {\r\n        type: sheet.type,\r\n        diagnosis: diagnosis || '',\r\n        treatmentPlan: treatmentPlan || '',\r\n        notes: notes || '',\r\n        specificData: sheetData\r\n      };\r\n\r\n      const response = await axios.post(\r\n        `${process.env.REACT_APP_API_URL}/api/patients/${nationalId}/treatment-sheets`,\r\n        sheetToSave,\r\n        { headers: { Authorization: `Bearer ${token}` } }\r\n      );\r\n\r\n      console.log('Sheet saved successfully:', response.data);\r\n      setSavedSheetId(response.data.sheet._id);\r\n      setShowSuccessPopup(true);\r\n      setLoading(false);\r\n\r\n      return true;\r\n    } catch (err) {\r\n      console.error('Error saving sheet:', err.response?.data);\r\n      setError(err.response?.data?.message || 'Failed to save sheet');\r\n      setLoading(false);\r\n      return false;\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex h-screen bg-gray-50\">\r\n        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n        <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\r\n          <div className=\"flex-shrink-0\">\r\n            <PatientNav\r\n              selectedChart={selectedChart}\r\n              setSelectedChart={setSelectedChart}\r\n              charts={charts}\r\n            />\r\n          </div>\r\n          <div className=\"p-6 overflow-y-auto\">\r\n            <div className=\"max-w-6xl mx-auto\">\r\n              <div className=\"animate-pulse space-y-4\">\r\n                <div className=\"h-8 bg-gray-200 rounded w-1/4\"></div>\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\r\n                  <div className=\"h-4 bg-gray-200 rounded w-1/2\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!selectedSheetType) {\r\n    return (\r\n      <div className=\"flex h-screen bg-gray-50\">\r\n        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n        <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\r\n          <div className=\"flex-shrink-0\">\r\n            <PatientNav\r\n              selectedChart={selectedChart}\r\n              setSelectedChart={setSelectedChart}\r\n              charts={charts}\r\n            />\r\n          </div>\r\n          <div className=\"p-6 overflow-y-auto\">\r\n            <div className=\"max-w-6xl mx-auto\">\r\n              <ProcedureSelection />\r\n\r\n              <div className=\"bg-white rounded-xl shadow-md p-6 mb-6\">\r\n                <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">Create New Treatment Sheet</h2>\r\n                <p className=\"text-gray-600\">\r\n                  Select a procedure type above to create a new treatment sheet for this patient.\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"bg-[#0077B6]/10 border border-[#0077B6]/30 rounded-lg p-4 text-[#0077B6] text-sm\">\r\n                <div className=\"flex items-start\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 mt-0.5 text-[#0077B6]\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                  </svg>\r\n                  <p>\r\n                    The sheet will be saved to the patient's history after completion.\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error || !sheet) {\r\n    return (\r\n      <div className=\"flex h-screen bg-gray-50\">\r\n        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n        <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\r\n          <div className=\"flex-shrink-0\">\r\n            <PatientNav\r\n              selectedChart={selectedChart}\r\n              setSelectedChart={setSelectedChart}\r\n              charts={charts}\r\n            />\r\n          </div>\r\n          <div className=\"p-6 overflow-y-auto\">\r\n            <div className=\"max-w-6xl mx-auto\">\r\n              <ProcedureSelection />\r\n\r\n              <div className=\"p-4 bg-red-100 text-[#333333] rounded-lg\">\r\n                {error || `No ${selectedSheetType} sheet found. Please select a different sheet type.`}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex h-screen bg-gray-50\">\r\n      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\r\n        <div className=\"flex-shrink-0\">\r\n          <PatientNav\r\n            selectedChart={selectedChart}\r\n            setSelectedChart={setSelectedChart}\r\n            charts={charts}\r\n          />\r\n        </div>\r\n        <div className=\"p-6 overflow-y-auto\">\r\n          <div className=\"max-w-6xl mx-auto\">\r\n            <ProcedureSelection />\r\n\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.5 }}\r\n            >\r\n              {(() => {\r\n                const sheetProps = {\r\n                  uniqueId: sheet.details?.specificData?.uniqueId || sheet._id || '',\r\n                  initialData: sheet.details?.specificData || {},\r\n                  onSave: handleSaveSheet\r\n                };\r\n\r\n                switch(sheet.type) {\r\n                  case 'Fixed Prosthodontics':\r\n                    return <FixedProsthodonticsSheet {...sheetProps} />;\r\n                  case 'Operative':\r\n                    return <OperativeSheet {...sheetProps} />;\r\n                  case 'Endodontics':\r\n                    return <EndodonticSheet {...sheetProps} />;\r\n                  case 'Removable Prosthodontics':\r\n                    return <RemovableProsthodonticsSheet {...sheetProps} />;\r\n                  case 'Periodontics':\r\n                    return <PeriodonticsSheet {...sheetProps} />;\r\n                  default:\r\n                    return (\r\n                      <div className=\"p-6 bg-white rounded-lg shadow\">\r\n                        <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">{sheet.type} Sheet</h2>\r\n                        <p className=\"text-gray-600\">This sheet type is not yet implemented.</p>\r\n                        <pre className=\"mt-4 p-4 bg-gray-100 rounded text-xs overflow-auto\">\r\n                          {JSON.stringify(sheet, null, 2)}\r\n                        </pre>\r\n                      </div>\r\n                    );\r\n                }\r\n              })()}\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Success Popup */}\r\n      <AnimatePresence>\r\n        {showSuccessPopup && (\r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            exit={{ opacity: 0, scale: 0.8 }}\r\n            className=\"fixed inset-0 flex items-center justify-center z-50\"\r\n          >\r\n            <div className=\"absolute inset-0 bg-black bg-opacity-50\" onClick={() => setShowSuccessPopup(false)}></div>\r\n            <div className=\"bg-white rounded-lg shadow-xl p-6 m-4 max-w-sm w-full relative z-10\">\r\n              <div className=\"flex items-center justify-center mb-4\">\r\n                <div className=\"bg-[#28A745]/20 rounded-full p-2\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-[#28A745]\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n              <h3 className=\"text-lg font-semibold text-center text-gray-800 mb-2\">Sheet Saved Successfully!</h3>\r\n              <p className=\"text-center text-gray-600 mb-4\">Your {sheet.type} sheet has been saved.</p>\r\n              <div className=\"flex justify-center\">\r\n                <p className=\"text-sm text-gray-500\">Redirecting to history...</p>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Sheets;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,4BAA4B,MAAM,gCAAgC;AACzE,OAAOC,iBAAiB,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,MAAM,GAAGA,CAAC;EAAEC,aAAa;EAAEC,gBAAgB;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC9D,MAAM;IAAEC;EAAW,CAAC,GAAGrB,SAAS,CAAC,CAAC;EAClC,MAAM;IAAEsB;EAAM,CAAC,GAAGjB,OAAO,CAAC,CAAC;EAC3B,MAAMkB,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8B,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAMwC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;EACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;EAE1B;EACA,MAAMC,UAAU,GAAG,CACjB,WAAW,EACX,sBAAsB,EACtB,0BAA0B,EAC1B,aAAa,EACb,cAAc,CACf;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACA,IAAIC,OAAO,GAAG,UAAU;IACxB,IAAIC,WAAW,GAAG,EAAE;IAEpB,IAAId,iBAAiB,EAAE;MACrB,QAAOA,iBAAiB;QACtB,KAAK,WAAW;UACda,OAAO,GAAG,iBAAiB;UAC3BC,WAAW,GAAG,qBAAqB;UACnC;QACF,KAAK,sBAAsB;UACzBD,OAAO,GAAG,iBAAiB;UAC3BC,WAAW,GAAG,qBAAqB;UACnC;QACF,KAAK,0BAA0B;UAC7BD,OAAO,GAAG,iBAAiB;UAC3BC,WAAW,GAAG,qBAAqB;UACnC;QACF,KAAK,aAAa;UAChBD,OAAO,GAAG,iBAAiB;UAC3BC,WAAW,GAAG,qBAAqB;UACnC;QACF,KAAK,cAAc;UACjBD,OAAO,GAAG,iBAAiB;UAC3BC,WAAW,GAAG,qBAAqB;UACnC;QACF;UACED,OAAO,GAAG,UAAU;MACxB;IACF;IAEA,oBACE5B,OAAA;MAAK8B,SAAS,EAAE,GAAGF,OAAO,IAAIC,WAAW,mEAAoE;MAAAE,QAAA,eAC3G/B,OAAA;QAAK8B,SAAS,EAAC,wDAAwD;QAAAC,QAAA,gBACrE/B,OAAA;UAAK8B,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B/B,OAAA;YAAI8B,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAChDhB,iBAAiB,GAAG,sBAAsBA,iBAAiB,EAAE,GAAG;UAAuB;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF,CAAC,eACLnC,OAAA;YAAG8B,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EACjChB,iBAAiB,GACd,sDAAsD,GACtD;UAAoD;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNnC,OAAA;UAAK8B,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC/B,OAAA;YACEoC,KAAK,EAAErB,iBAAiB,IAAI,EAAG;YAC/BsB,QAAQ,EAAEC,qBAAsB;YAChCR,SAAS,EAAC,gMAAgM;YAAAC,QAAA,gBAE1M/B,OAAA;cAAQoC,KAAK,EAAC,EAAE;cAACN,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACjFT,UAAU,CAACa,GAAG,CAAEC,IAAI,iBACnBxC,OAAA;cAAmBoC,KAAK,EAAEI,IAAK;cAACV,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAC/DS;YAAI,GADMA,IAAI;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAET,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACTnC,OAAA;YAAK8B,SAAS,EAAC,kFAAkF;YAAAC,QAAA,eAC/F/B,OAAA;cAAK8B,SAAS,EAAC,sBAAsB;cAACW,KAAK,EAAC,4BAA4B;cAACC,OAAO,EAAC,WAAW;cAAAX,QAAA,eAC1F/B,OAAA;gBAAM2C,CAAC,EAAC;cAA4E;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACAnD,SAAS,CAAC,MAAM;IACd;IACA4D,YAAY,CAACC,UAAU,CAAC,mBAAmB,CAAC;IAC5C7B,oBAAoB,CAAC,EAAE,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMsB,qBAAqB,GAAIQ,CAAC,IAAK;IACnC,MAAMC,YAAY,GAAGD,CAAC,CAACE,MAAM,CAACZ,KAAK;;IAEnC;IACAQ,YAAY,CAACK,OAAO,CAAC,mBAAmB,EAAEF,YAAY,CAAC;IACvD/B,oBAAoB,CAAC+B,YAAY,CAAC;EACpC,CAAC;EAED/D,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAAC+B,iBAAiB,EAAE;MACtBH,UAAU,CAAC,KAAK,CAAC;MACjBF,QAAQ,CAAC,IAAI,CAAC;MACd;IACF;;IAEA;IACAE,UAAU,CAAC,IAAI,CAAC;;IAEhB;IACA,MAAMsC,QAAQ,GAAG;MACfV,IAAI,EAAEzB,iBAAiB;MACvBoC,OAAO,EAAE;QACPC,SAAS,EAAE,EAAE;QACbC,aAAa,EAAE,EAAE;QACjBC,KAAK,EAAE,EAAE;QACTC,YAAY,EAAE,CAAC;MACjB,CAAC;MACDC,GAAG,EAAE,OAAOhC,IAAI,CAACiC,GAAG,CAAC,CAAC,EAAE;MAAE;MAC1BC,SAAS,EAAE,IAAIlC,IAAI,CAAC,CAAC,CAACmC,WAAW,CAAC;IACpC,CAAC;IAEDC,OAAO,CAACC,GAAG,CAAC,+BAA+B9C,iBAAiB,EAAE,CAAC;IAC/DL,QAAQ,CAACwC,QAAQ,CAAC;IAClBpC,QAAQ,CAAC,EAAE,CAAC;IACZF,UAAU,CAAC,KAAK,CAAC;EAEnB,CAAC,EAAE,CAACG,iBAAiB,CAAC,CAAC;;EAEvB;EACA/B,SAAS,CAAC,MAAM;IACd,IAAImC,gBAAgB,EAAE;MACpB,MAAM2C,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BvD,QAAQ,CAAC,mBAAmBF,UAAU,UAAU,CAAC;MACnD,CAAC,EAAE,IAAI,CAAC;MAER,OAAO,MAAM0D,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAAC3C,gBAAgB,EAAEX,QAAQ,EAAEF,UAAU,CAAC,CAAC;;EAE5C;EACA,MAAM2D,eAAe,GAAG,MAAAA,CAAOC,SAAS,EAAEd,SAAS,EAAEC,aAAa,EAAEC,KAAK,KAAK;IAC5E,IAAI;MACF1C,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMuD,WAAW,GAAG;QAClB3B,IAAI,EAAE/B,KAAK,CAAC+B,IAAI;QAChBY,SAAS,EAAEA,SAAS,IAAI,EAAE;QAC1BC,aAAa,EAAEA,aAAa,IAAI,EAAE;QAClCC,KAAK,EAAEA,KAAK,IAAI,EAAE;QAClBC,YAAY,EAAEW;MAChB,CAAC;MAED,MAAME,QAAQ,GAAG,MAAMjF,KAAK,CAACkF,IAAI,CAC/B,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,iBAAiBlE,UAAU,mBAAmB,EAC9E6D,WAAW,EACX;QAAEM,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUnE,KAAK;QAAG;MAAE,CAClD,CAAC;MAEDqD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEO,QAAQ,CAACO,IAAI,CAAC;MACvDrD,eAAe,CAAC8C,QAAQ,CAACO,IAAI,CAAClE,KAAK,CAAC+C,GAAG,CAAC;MACxCpC,mBAAmB,CAAC,IAAI,CAAC;MACzBR,UAAU,CAAC,KAAK,CAAC;MAEjB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOgE,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,mBAAA;MACZnB,OAAO,CAAC/C,KAAK,CAAC,qBAAqB,GAAAgE,aAAA,GAAED,GAAG,CAACR,QAAQ,cAAAS,aAAA,uBAAZA,aAAA,CAAcF,IAAI,CAAC;MACxD7D,QAAQ,CAAC,EAAAgE,cAAA,GAAAF,GAAG,CAACR,QAAQ,cAAAU,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcH,IAAI,cAAAI,mBAAA,uBAAlBA,mBAAA,CAAoBC,OAAO,KAAI,sBAAsB,CAAC;MAC/DpE,UAAU,CAAC,KAAK,CAAC;MACjB,OAAO,KAAK;IACd;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACEX,OAAA;MAAK8B,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvC/B,OAAA,CAACT,OAAO;QAAC0F,MAAM,EAAEhE,WAAY;QAACiE,SAAS,EAAEhE;MAAe;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DnC,OAAA;QAAK8B,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnD/B,OAAA,CAACR,MAAM;UAAC2F,aAAa,EAAEA,CAAA,KAAMjE,cAAc,CAAC,CAACD,WAAW;QAAE;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DnC,OAAA;UAAK8B,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B/B,OAAA,CAACP,UAAU;YACTS,aAAa,EAAEA,aAAc;YAC7BC,gBAAgB,EAAEA,gBAAiB;YACnCC,MAAM,EAAEA;UAAO;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNnC,OAAA;UAAK8B,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClC/B,OAAA;YAAK8B,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChC/B,OAAA;cAAK8B,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC/B,OAAA;gBAAK8B,SAAS,EAAC;cAA+B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDnC,OAAA;gBAAK8B,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB/B,OAAA;kBAAK8B,SAAS,EAAC;gBAA+B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrDnC,OAAA;kBAAK8B,SAAS,EAAC;gBAA+B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACpB,iBAAiB,EAAE;IACtB,oBACEf,OAAA;MAAK8B,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvC/B,OAAA,CAACT,OAAO;QAAC0F,MAAM,EAAEhE,WAAY;QAACiE,SAAS,EAAEhE;MAAe;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DnC,OAAA;QAAK8B,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnD/B,OAAA,CAACR,MAAM;UAAC2F,aAAa,EAAEA,CAAA,KAAMjE,cAAc,CAAC,CAACD,WAAW;QAAE;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DnC,OAAA;UAAK8B,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B/B,OAAA,CAACP,UAAU;YACTS,aAAa,EAAEA,aAAc;YAC7BC,gBAAgB,EAAEA,gBAAiB;YACnCC,MAAM,EAAEA;UAAO;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNnC,OAAA;UAAK8B,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClC/B,OAAA;YAAK8B,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC/B,OAAA,CAAC2B,kBAAkB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEtBnC,OAAA;cAAK8B,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD/B,OAAA;gBAAI8B,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxFnC,OAAA;gBAAG8B,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAE7B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENnC,OAAA;cAAK8B,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/F/B,OAAA;gBAAK8B,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/B/B,OAAA;kBAAKyC,KAAK,EAAC,4BAA4B;kBAACX,SAAS,EAAC,oCAAoC;kBAACsD,IAAI,EAAC,MAAM;kBAAC1C,OAAO,EAAC,WAAW;kBAAC2C,MAAM,EAAC,cAAc;kBAAAtD,QAAA,eAC1I/B,OAAA;oBAAMsF,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAAC7C,CAAC,EAAC;kBAA2D;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChI,CAAC,eACNnC,OAAA;kBAAA+B,QAAA,EAAG;gBAEH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAItB,KAAK,IAAI,CAACJ,KAAK,EAAE;IACnB,oBACET,OAAA;MAAK8B,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvC/B,OAAA,CAACT,OAAO;QAAC0F,MAAM,EAAEhE,WAAY;QAACiE,SAAS,EAAEhE;MAAe;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DnC,OAAA;QAAK8B,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnD/B,OAAA,CAACR,MAAM;UAAC2F,aAAa,EAAEA,CAAA,KAAMjE,cAAc,CAAC,CAACD,WAAW;QAAE;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DnC,OAAA;UAAK8B,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B/B,OAAA,CAACP,UAAU;YACTS,aAAa,EAAEA,aAAc;YAC7BC,gBAAgB,EAAEA,gBAAiB;YACnCC,MAAM,EAAEA;UAAO;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNnC,OAAA;UAAK8B,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClC/B,OAAA;YAAK8B,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC/B,OAAA,CAAC2B,kBAAkB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEtBnC,OAAA;cAAK8B,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EACtDlB,KAAK,IAAI,MAAME,iBAAiB;YAAqD;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEnC,OAAA;IAAK8B,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvC/B,OAAA,CAACT,OAAO;MAAC0F,MAAM,EAAEhE,WAAY;MAACiE,SAAS,EAAEhE;IAAe;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC3DnC,OAAA;MAAK8B,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnD/B,OAAA,CAACR,MAAM;QAAC2F,aAAa,EAAEA,CAAA,KAAMjE,cAAc,CAAC,CAACD,WAAW;MAAE;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7DnC,OAAA;QAAK8B,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5B/B,OAAA,CAACP,UAAU;UACTS,aAAa,EAAEA,aAAc;UAC7BC,gBAAgB,EAAEA,gBAAiB;UACnCC,MAAM,EAAEA;QAAO;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNnC,OAAA;QAAK8B,SAAS,EAAC,qBAAqB;QAAAC,QAAA,eAClC/B,OAAA;UAAK8B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC/B,OAAA,CAAC2B,kBAAkB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEtBnC,OAAA,CAACZ,MAAM,CAACqG,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAhE,QAAA,EAE7B,CAAC,CAAAiE,cAAA,EAAAC,qBAAA,EAAAC,eAAA,KAAM;cACN,MAAMC,UAAU,GAAG;gBACjBC,QAAQ,EAAE,EAAAJ,cAAA,GAAAvF,KAAK,CAAC0C,OAAO,cAAA6C,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAezC,YAAY,cAAA0C,qBAAA,uBAA3BA,qBAAA,CAA6BG,QAAQ,KAAI3F,KAAK,CAAC+C,GAAG,IAAI,EAAE;gBAClE6C,WAAW,EAAE,EAAAH,eAAA,GAAAzF,KAAK,CAAC0C,OAAO,cAAA+C,eAAA,uBAAbA,eAAA,CAAe3C,YAAY,KAAI,CAAC,CAAC;gBAC9C+C,MAAM,EAAErC;cACV,CAAC;cAED,QAAOxD,KAAK,CAAC+B,IAAI;gBACf,KAAK,sBAAsB;kBACzB,oBAAOxC,OAAA,CAACN,wBAAwB;oBAAA,GAAKyG;kBAAU;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBACrD,KAAK,WAAW;kBACd,oBAAOnC,OAAA,CAACL,cAAc;oBAAA,GAAKwG;kBAAU;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAC3C,KAAK,aAAa;kBAChB,oBAAOnC,OAAA,CAACJ,eAAe;oBAAA,GAAKuG;kBAAU;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAC5C,KAAK,0BAA0B;kBAC7B,oBAAOnC,OAAA,CAACH,4BAA4B;oBAAA,GAAKsG;kBAAU;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBACzD,KAAK,cAAc;kBACjB,oBAAOnC,OAAA,CAACF,iBAAiB;oBAAA,GAAKqG;kBAAU;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAC9C;kBACE,oBACEnC,OAAA;oBAAK8B,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7C/B,OAAA;sBAAI8B,SAAS,EAAC,0CAA0C;sBAAAC,QAAA,GAAEtB,KAAK,CAAC+B,IAAI,EAAC,QAAM;oBAAA;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAChFnC,OAAA;sBAAG8B,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAuC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACxEnC,OAAA;sBAAK8B,SAAS,EAAC,oDAAoD;sBAAAC,QAAA,EAChEwE,IAAI,CAACC,SAAS,CAAC/F,KAAK,EAAE,IAAI,EAAE,CAAC;oBAAC;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;cAEZ;YACF,CAAC,EAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA,CAACX,eAAe;MAAA0C,QAAA,EACbZ,gBAAgB,iBACfnB,OAAA,CAACZ,MAAM,CAACqG,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEc,KAAK,EAAE;QAAI,CAAE;QACpCZ,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEc,KAAK,EAAE;QAAE,CAAE;QAClCC,IAAI,EAAE;UAAEf,OAAO,EAAE,CAAC;UAAEc,KAAK,EAAE;QAAI,CAAE;QACjC3E,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAE/D/B,OAAA;UAAK8B,SAAS,EAAC,yCAAyC;UAAC6E,OAAO,EAAEA,CAAA,KAAMvF,mBAAmB,CAAC,KAAK;QAAE;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1GnC,OAAA;UAAK8B,SAAS,EAAC,qEAAqE;UAAAC,QAAA,gBAClF/B,OAAA;YAAK8B,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpD/B,OAAA;cAAK8B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,eAC/C/B,OAAA;gBAAKyC,KAAK,EAAC,4BAA4B;gBAACX,SAAS,EAAC,wBAAwB;gBAACsD,IAAI,EAAC,MAAM;gBAAC1C,OAAO,EAAC,WAAW;gBAAC2C,MAAM,EAAC,cAAc;gBAAAtD,QAAA,eAC9H/B,OAAA;kBAAMsF,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAAC7C,CAAC,EAAC;gBAAgB;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnC,OAAA;YAAI8B,SAAS,EAAC,sDAAsD;YAAAC,QAAA,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnGnC,OAAA;YAAG8B,SAAS,EAAC,gCAAgC;YAAAC,QAAA,GAAC,OAAK,EAACtB,KAAK,CAAC+B,IAAI,EAAC,wBAAsB;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACzFnC,OAAA;YAAK8B,SAAS,EAAC,qBAAqB;YAAAC,QAAA,eAClC/B,OAAA;cAAG8B,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CAAC;AAEV,CAAC;AAAC9B,EAAA,CA9WIJ,MAAM;EAAA,QACahB,SAAS,EACdK,OAAO,EACRJ,WAAW;AAAA;AAAA0H,EAAA,GAHxB3G,MAAM;AAgXZ,eAAeA,MAAM;AAAC,IAAA2G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}