{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\superadmin\\\\Activity.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { motion } from 'framer-motion';\nimport { FaUser, FaCalendarAlt, FaFilter, FaDownload, FaEye } from 'react-icons/fa';\nimport Navbar from '../student/Navbar';\nimport Loader from '../components/Loader';\nimport { useAuth } from '../context/AuthContext';\nimport SuperAdminSidebar from './SuperAdminSidebar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Activity = () => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [activities, setActivities] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [filters, setFilters] = useState({\n    dateRange: '7days',\n    userType: 'all',\n    actionType: 'all'\n  });\n  const [pagination, setPagination] = useState({\n    page: 1,\n    limit: 20,\n    total: 0,\n    totalPages: 0\n  });\n  const navigate = useNavigate();\n  const {\n    user,\n    token\n  } = useAuth();\n  useEffect(() => {\n    fetchActivities();\n  }, [filters, pagination.page]);\n  const fetchActivities = async () => {\n    if (!user || !token) {\n      setError('Please log in to view activities.');\n      setLoading(false);\n      return;\n    }\n    setLoading(true);\n    try {\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        },\n        params: {\n          ...filters,\n          page: pagination.page,\n          limit: pagination.limit\n        }\n      };\n      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/activity`, config);\n      setActivities(response.data.activities || []);\n      setPagination(prev => ({\n        ...prev,\n        total: response.data.total || 0,\n        totalPages: response.data.totalPages || 0\n      }));\n    } catch (err) {\n      var _err$response, _err$response2, _err$response3, _err$response3$data, _err$response4;\n      console.error('Fetch error:', ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n      const errorMessage = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.status) === 401 ? 'Unauthorized. Please log in again.' : ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || 'Failed to load activities';\n      setError(errorMessage);\n      if (((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 401) {\n        navigate('/login');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleFilterChange = (filterType, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [filterType]: value\n    }));\n    setPagination(prev => ({\n      ...prev,\n      page: 1\n    }));\n  };\n  const handlePageChange = newPage => {\n    setPagination(prev => ({\n      ...prev,\n      page: newPage\n    }));\n  };\n  const exportActivities = async () => {\n    try {\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        },\n        params: filters,\n        responseType: 'blob'\n      };\n      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/activity/export`, config);\n      const url = window.URL.createObjectURL(new Blob([response.data]));\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', `activity-log-${new Date().toISOString().split('T')[0]}.csv`);\n      document.body.appendChild(link);\n      link.click();\n      link.remove();\n    } catch (err) {\n      setError('Failed to export activities');\n    }\n  };\n  const getActionIcon = action => {\n    if (action.includes('login') || action.includes('logout')) return '🔐';\n    if (action.includes('create') || action.includes('add')) return '➕';\n    if (action.includes('update') || action.includes('edit')) return '✏️';\n    if (action.includes('delete') || action.includes('remove')) return '🗑️';\n    if (action.includes('view') || action.includes('access')) return '👁️';\n    return '📝';\n  };\n  const getUserTypeColor = userType => {\n    switch (userType) {\n      case 'superadmin':\n        return 'bg-purple-100 text-purple-800';\n      case 'admin':\n        return 'bg-blue-100 text-blue-800';\n      case 'supervisor':\n        return 'bg-green-100 text-green-800';\n      case 'student':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'assistant':\n        return 'bg-orange-100 text-orange-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const container = {\n    hidden: {\n      opacity: 0\n    },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const item = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    show: {\n      opacity: 1,\n      y: 0\n    }\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 133,\n    columnNumber: 23\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(SuperAdminSidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: [error && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: -20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-5 w-5 text-red-500 mr-3\",\n                viewBox: \"0 0 20 20\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-700 font-medium\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\",\n                  children: \"Activity Log\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-[#333333]\",\n                  children: \"Monitor system activities and user actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: exportActivities,\n                className: \"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaDownload, {\n                  className: \"h-5 w-5 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this), \"Export CSV\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: item,\n              className: \"bg-white rounded-xl shadow-sm p-6 mb-6 border border-[rgba(0,119,182,0.1)]\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(FaFilter, {\n                  className: \"h-5 w-5 text-[#0077B6] mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-lg font-semibold text-[#0077B6]\",\n                  children: \"Filters\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Date Range\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: filters.dateRange,\n                    onChange: e => handleFilterChange('dateRange', e.target.value),\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"1day\",\n                      children: \"Last 24 Hours\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 198,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"7days\",\n                      children: \"Last 7 Days\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 199,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"30days\",\n                      children: \"Last 30 Days\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 200,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"90days\",\n                      children: \"Last 90 Days\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"all\",\n                      children: \"All Time\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 202,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"User Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: filters.userType,\n                    onChange: e => handleFilterChange('userType', e.target.value),\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"all\",\n                      children: \"All Users\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 212,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"superadmin\",\n                      children: \"Super Admins\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 213,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"admin\",\n                      children: \"Admins\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 214,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"supervisor\",\n                      children: \"Supervisors\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 215,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"student\",\n                      children: \"Students\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"assistant\",\n                      children: \"Assistants\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 217,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Action Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: filters.actionType,\n                    onChange: e => handleFilterChange('actionType', e.target.value),\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"all\",\n                      children: \"All Actions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 227,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"auth\",\n                      children: \"Authentication\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 228,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"create\",\n                      children: \"Create\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 229,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"update\",\n                      children: \"Update\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 230,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"delete\",\n                      children: \"Delete\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 231,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"view\",\n                      children: \"View\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 232,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: container,\n              initial: \"hidden\",\n              whileInView: \"show\",\n              viewport: {\n                once: true\n              },\n              className: \"bg-white rounded-xl shadow-sm border border-[rgba(0,119,182,0.1)] overflow-hidden\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-6\",\n                  children: /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6] flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(FaEye, {\n                      className: \"h-5 w-5 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 249,\n                      columnNumber: 23\n                    }, this), \"Recent Activities (\", pagination.total, \" total)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 19\n                }, this), activities.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center py-12\",\n                  children: [/*#__PURE__*/_jsxDEV(FaUser, {\n                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-medium text-gray-900\",\n                    children: \"No activities found\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-1 text-gray-500\",\n                    children: \"Try adjusting your filters to see more results.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: activities.map((activity, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                    variants: item,\n                    className: \"flex items-start space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-shrink-0\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-10 h-10 bg-[rgba(0,119,182,0.1)] rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-lg\",\n                          children: getActionIcon(activity.action)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 270,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 269,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 268,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 min-w-0\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center space-x-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm font-medium text-gray-900\",\n                            children: activity.userName || 'Unknown User'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 276,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getUserTypeColor(activity.userRole)}`,\n                            children: activity.userRole || 'Unknown'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 277,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 275,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center text-sm text-gray-500\",\n                          children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                            className: \"h-4 w-4 mr-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 282,\n                            columnNumber: 33\n                          }, this), new Date(activity.timestamp).toLocaleString()]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 281,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 274,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"mt-1 text-sm text-gray-600\",\n                        children: activity.action\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 286,\n                        columnNumber: 29\n                      }, this), activity.details && /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"mt-1 text-xs text-gray-500\",\n                        children: activity.details\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 288,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 273,\n                      columnNumber: 27\n                    }, this)]\n                  }, activity._id || index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this), pagination.totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-6 flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-700\",\n                    children: [\"Showing \", (pagination.page - 1) * pagination.limit + 1, \" to \", Math.min(pagination.page * pagination.limit, pagination.total), \" of \", pagination.total, \" results\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handlePageChange(pagination.page - 1),\n                      disabled: pagination.page === 1,\n                      className: \"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                      children: \"Previous\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 303,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"px-3 py-2 text-sm font-medium text-[#0077B6] bg-[rgba(0,119,182,0.1)] border border-[#0077B6] rounded-md\",\n                      children: [pagination.page, \" of \", pagination.totalPages]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 310,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handlePageChange(pagination.page + 1),\n                      disabled: pagination.page === pagination.totalPages,\n                      className: \"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                      children: \"Next\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 313,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 5\n  }, this);\n};\n_s(Activity, \"EW76dUIambUauGCHLnmK0AlrHGw=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Activity;\nexport default Activity;\nvar _c;\n$RefreshReg$(_c, \"Activity\");", "map": {"version": 3, "names": ["useState", "useEffect", "useNavigate", "axios", "motion", "FaUser", "FaCalendarAlt", "FaFilter", "FaDownload", "FaEye", "<PERSON><PERSON><PERSON>", "Loader", "useAuth", "SuperAdminSidebar", "jsxDEV", "_jsxDEV", "Activity", "_s", "sidebarOpen", "setSidebarOpen", "activities", "setActivities", "loading", "setLoading", "error", "setError", "filters", "setFilters", "date<PERSON><PERSON><PERSON>", "userType", "actionType", "pagination", "setPagination", "page", "limit", "total", "totalPages", "navigate", "user", "token", "fetchActivities", "config", "headers", "Authorization", "params", "response", "get", "process", "env", "REACT_APP_API_URL", "data", "prev", "err", "_err$response", "_err$response2", "_err$response3", "_err$response3$data", "_err$response4", "console", "message", "errorMessage", "status", "handleFilterChange", "filterType", "value", "handlePageChange", "newPage", "exportActivities", "responseType", "url", "window", "URL", "createObjectURL", "Blob", "link", "document", "createElement", "href", "setAttribute", "Date", "toISOString", "split", "body", "append<PERSON><PERSON><PERSON>", "click", "remove", "getActionIcon", "action", "includes", "getUserTypeColor", "container", "hidden", "opacity", "show", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "item", "y", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "isOpen", "setIsOpen", "toggleSidebar", "div", "initial", "animate", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "duration", "button", "whileHover", "scale", "whileTap", "onClick", "variants", "onChange", "e", "target", "whileInView", "viewport", "once", "length", "map", "activity", "index", "userName", "userRole", "timestamp", "toLocaleString", "details", "_id", "Math", "min", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/superadmin/Activity.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { motion } from 'framer-motion';\nimport { FaUser, FaCalendarAlt, FaFilter, FaDownload, FaEye } from 'react-icons/fa';\nimport Navbar from '../student/Navbar';\nimport Loader from '../components/Loader';\nimport { useAuth } from '../context/AuthContext';\nimport SuperAdminSidebar from './SuperAdminSidebar';\n\nconst Activity = () => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [activities, setActivities] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [filters, setFilters] = useState({\n    dateRange: '7days',\n    userType: 'all',\n    actionType: 'all'\n  });\n  const [pagination, setPagination] = useState({\n    page: 1,\n    limit: 20,\n    total: 0,\n    totalPages: 0\n  });\n\n  const navigate = useNavigate();\n  const { user, token } = useAuth();\n\n  useEffect(() => {\n    fetchActivities();\n  }, [filters, pagination.page]);\n\n  const fetchActivities = async () => {\n    if (!user || !token) {\n      setError('Please log in to view activities.');\n      setLoading(false);\n      return;\n    }\n\n    setLoading(true);\n    try {\n      const config = {\n        headers: { Authorization: `Bearer ${token}` },\n        params: {\n          ...filters,\n          page: pagination.page,\n          limit: pagination.limit\n        }\n      };\n      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/activity`, config);\n      setActivities(response.data.activities || []);\n      setPagination(prev => ({\n        ...prev,\n        total: response.data.total || 0,\n        totalPages: response.data.totalPages || 0\n      }));\n    } catch (err) {\n      console.error('Fetch error:', err.response?.data || err.message);\n      const errorMessage = err.response?.status === 401\n        ? 'Unauthorized. Please log in again.'\n        : err.response?.data?.message || 'Failed to load activities';\n      setError(errorMessage);\n      if (err.response?.status === 401) {\n        navigate('/login');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleFilterChange = (filterType, value) => {\n    setFilters(prev => ({ ...prev, [filterType]: value }));\n    setPagination(prev => ({ ...prev, page: 1 }));\n  };\n\n  const handlePageChange = (newPage) => {\n    setPagination(prev => ({ ...prev, page: newPage }));\n  };\n\n  const exportActivities = async () => {\n    try {\n      const config = {\n        headers: { Authorization: `Bearer ${token}` },\n        params: filters,\n        responseType: 'blob'\n      };\n      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/activity/export`, config);\n      \n      const url = window.URL.createObjectURL(new Blob([response.data]));\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', `activity-log-${new Date().toISOString().split('T')[0]}.csv`);\n      document.body.appendChild(link);\n      link.click();\n      link.remove();\n    } catch (err) {\n      setError('Failed to export activities');\n    }\n  };\n\n  const getActionIcon = (action) => {\n    if (action.includes('login') || action.includes('logout')) return '🔐';\n    if (action.includes('create') || action.includes('add')) return '➕';\n    if (action.includes('update') || action.includes('edit')) return '✏️';\n    if (action.includes('delete') || action.includes('remove')) return '🗑️';\n    if (action.includes('view') || action.includes('access')) return '👁️';\n    return '📝';\n  };\n\n  const getUserTypeColor = (userType) => {\n    switch (userType) {\n      case 'superadmin': return 'bg-purple-100 text-purple-800';\n      case 'admin': return 'bg-blue-100 text-blue-800';\n      case 'supervisor': return 'bg-green-100 text-green-800';\n      case 'student': return 'bg-yellow-100 text-yellow-800';\n      case 'assistant': return 'bg-orange-100 text-orange-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const container = {\n    hidden: { opacity: 0 },\n    show: { opacity: 1, transition: { staggerChildren: 0.1 } },\n  };\n\n  const item = {\n    hidden: { opacity: 0, y: 20 },\n    show: { opacity: 1, y: 0 },\n  };\n\n  if (loading) return <Loader />;\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\">\n      <SuperAdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\n\n        <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\">\n          <div className=\"max-w-7xl mx-auto\">\n            {error && (\n              <motion.div\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                className=\"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\"\n              >\n                <div className=\"flex items-center\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-red-500 mr-3\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <p className=\"text-red-700 font-medium\">{error}</p>\n                </div>\n              </motion.div>\n            )}\n\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.5 }}\n            >\n              {/* Header */}\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\">\n                <div>\n                  <h1 className=\"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\">Activity Log</h1>\n                  <p className=\"text-[#333333]\">Monitor system activities and user actions</p>\n                </div>\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={exportActivities}\n                  className=\"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center\"\n                >\n                  <FaDownload className=\"h-5 w-5 mr-2\" />\n                  Export CSV\n                </motion.button>\n              </div>\n\n              {/* Filters */}\n              <motion.div\n                variants={item}\n                className=\"bg-white rounded-xl shadow-sm p-6 mb-6 border border-[rgba(0,119,182,0.1)]\"\n              >\n                <div className=\"flex items-center mb-4\">\n                  <FaFilter className=\"h-5 w-5 text-[#0077B6] mr-2\" />\n                  <h2 className=\"text-lg font-semibold text-[#0077B6]\">Filters</h2>\n                </div>\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Date Range</label>\n                    <select\n                      value={filters.dateRange}\n                      onChange={(e) => handleFilterChange('dateRange', e.target.value)}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                    >\n                      <option value=\"1day\">Last 24 Hours</option>\n                      <option value=\"7days\">Last 7 Days</option>\n                      <option value=\"30days\">Last 30 Days</option>\n                      <option value=\"90days\">Last 90 Days</option>\n                      <option value=\"all\">All Time</option>\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">User Type</label>\n                    <select\n                      value={filters.userType}\n                      onChange={(e) => handleFilterChange('userType', e.target.value)}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                    >\n                      <option value=\"all\">All Users</option>\n                      <option value=\"superadmin\">Super Admins</option>\n                      <option value=\"admin\">Admins</option>\n                      <option value=\"supervisor\">Supervisors</option>\n                      <option value=\"student\">Students</option>\n                      <option value=\"assistant\">Assistants</option>\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Action Type</label>\n                    <select\n                      value={filters.actionType}\n                      onChange={(e) => handleFilterChange('actionType', e.target.value)}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n                    >\n                      <option value=\"all\">All Actions</option>\n                      <option value=\"auth\">Authentication</option>\n                      <option value=\"create\">Create</option>\n                      <option value=\"update\">Update</option>\n                      <option value=\"delete\">Delete</option>\n                      <option value=\"view\">View</option>\n                    </select>\n                  </div>\n                </div>\n              </motion.div>\n\n              {/* Activity List */}\n              <motion.div\n                variants={container}\n                initial=\"hidden\"\n                whileInView=\"show\"\n                viewport={{ once: true }}\n                className=\"bg-white rounded-xl shadow-sm border border-[rgba(0,119,182,0.1)] overflow-hidden\"\n              >\n                <div className=\"p-6\">\n                  <div className=\"flex items-center justify-between mb-6\">\n                    <h2 className=\"text-xl font-bold text-[#0077B6] flex items-center\">\n                      <FaEye className=\"h-5 w-5 mr-2\" />\n                      Recent Activities ({pagination.total} total)\n                    </h2>\n                  </div>\n\n                  {activities.length === 0 ? (\n                    <div className=\"text-center py-12\">\n                      <FaUser className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                      <h3 className=\"text-lg font-medium text-gray-900\">No activities found</h3>\n                      <p className=\"mt-1 text-gray-500\">Try adjusting your filters to see more results.</p>\n                    </div>\n                  ) : (\n                    <div className=\"space-y-4\">\n                      {activities.map((activity, index) => (\n                        <motion.div\n                          key={activity._id || index}\n                          variants={item}\n                          className=\"flex items-start space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\"\n                        >\n                          <div className=\"flex-shrink-0\">\n                            <div className=\"w-10 h-10 bg-[rgba(0,119,182,0.1)] rounded-full flex items-center justify-center\">\n                              <span className=\"text-lg\">{getActionIcon(activity.action)}</span>\n                            </div>\n                          </div>\n                          <div className=\"flex-1 min-w-0\">\n                            <div className=\"flex items-center justify-between\">\n                              <div className=\"flex items-center space-x-2\">\n                                <p className=\"text-sm font-medium text-gray-900\">{activity.userName || 'Unknown User'}</p>\n                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getUserTypeColor(activity.userRole)}`}>\n                                  {activity.userRole || 'Unknown'}\n                                </span>\n                              </div>\n                              <div className=\"flex items-center text-sm text-gray-500\">\n                                <FaCalendarAlt className=\"h-4 w-4 mr-1\" />\n                                {new Date(activity.timestamp).toLocaleString()}\n                              </div>\n                            </div>\n                            <p className=\"mt-1 text-sm text-gray-600\">{activity.action}</p>\n                            {activity.details && (\n                              <p className=\"mt-1 text-xs text-gray-500\">{activity.details}</p>\n                            )}\n                          </div>\n                        </motion.div>\n                      ))}\n                    </div>\n                  )}\n\n                  {/* Pagination */}\n                  {pagination.totalPages > 1 && (\n                    <div className=\"mt-6 flex items-center justify-between\">\n                      <div className=\"text-sm text-gray-700\">\n                        Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} results\n                      </div>\n                      <div className=\"flex space-x-2\">\n                        <button\n                          onClick={() => handlePageChange(pagination.page - 1)}\n                          disabled={pagination.page === 1}\n                          className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                        >\n                          Previous\n                        </button>\n                        <span className=\"px-3 py-2 text-sm font-medium text-[#0077B6] bg-[rgba(0,119,182,0.1)] border border-[#0077B6] rounded-md\">\n                          {pagination.page} of {pagination.totalPages}\n                        </span>\n                        <button\n                          onClick={() => handlePageChange(pagination.page + 1)}\n                          disabled={pagination.page === pagination.totalPages}\n                          className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                        >\n                          Next\n                        </button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </motion.div>\n            </motion.div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default Activity;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,QAAQ,gBAAgB;AACnF,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,iBAAiB,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC;IACrC4B,SAAS,EAAE,OAAO;IAClBC,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC;IAC3CiC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,CAAC;IACRC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAMC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEoC,IAAI;IAAEC;EAAM,CAAC,GAAG3B,OAAO,CAAC,CAAC;EAEjCX,SAAS,CAAC,MAAM;IACduC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACd,OAAO,EAAEK,UAAU,CAACE,IAAI,CAAC,CAAC;EAE9B,MAAMO,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACF,IAAI,IAAI,CAACC,KAAK,EAAE;MACnBd,QAAQ,CAAC,mCAAmC,CAAC;MAC7CF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEAA,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMkB,MAAM,GAAG;QACbC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUJ,KAAK;QAAG,CAAC;QAC7CK,MAAM,EAAE;UACN,GAAGlB,OAAO;UACVO,IAAI,EAAEF,UAAU,CAACE,IAAI;UACrBC,KAAK,EAAEH,UAAU,CAACG;QACpB;MACF,CAAC;MACD,MAAMW,QAAQ,GAAG,MAAM1C,KAAK,CAAC2C,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,0BAA0B,EAAER,MAAM,CAAC;MACpGpB,aAAa,CAACwB,QAAQ,CAACK,IAAI,CAAC9B,UAAU,IAAI,EAAE,CAAC;MAC7CY,aAAa,CAACmB,IAAI,KAAK;QACrB,GAAGA,IAAI;QACPhB,KAAK,EAAEU,QAAQ,CAACK,IAAI,CAACf,KAAK,IAAI,CAAC;QAC/BC,UAAU,EAAES,QAAQ,CAACK,IAAI,CAACd,UAAU,IAAI;MAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOgB,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA,EAAAC,cAAA;MACZC,OAAO,CAAClC,KAAK,CAAC,cAAc,EAAE,EAAA6B,aAAA,GAAAD,GAAG,CAACP,QAAQ,cAAAQ,aAAA,uBAAZA,aAAA,CAAcH,IAAI,KAAIE,GAAG,CAACO,OAAO,CAAC;MAChE,MAAMC,YAAY,GAAG,EAAAN,cAAA,GAAAF,GAAG,CAACP,QAAQ,cAAAS,cAAA,uBAAZA,cAAA,CAAcO,MAAM,MAAK,GAAG,GAC7C,oCAAoC,GACpC,EAAAN,cAAA,GAAAH,GAAG,CAACP,QAAQ,cAAAU,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcL,IAAI,cAAAM,mBAAA,uBAAlBA,mBAAA,CAAoBG,OAAO,KAAI,2BAA2B;MAC9DlC,QAAQ,CAACmC,YAAY,CAAC;MACtB,IAAI,EAAAH,cAAA,GAAAL,GAAG,CAACP,QAAQ,cAAAY,cAAA,uBAAZA,cAAA,CAAcI,MAAM,MAAK,GAAG,EAAE;QAChCxB,QAAQ,CAAC,QAAQ,CAAC;MACpB;IACF,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuC,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,KAAK,KAAK;IAChDrC,UAAU,CAACwB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACY,UAAU,GAAGC;IAAM,CAAC,CAAC,CAAC;IACtDhC,aAAa,CAACmB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAElB,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;EAC/C,CAAC;EAED,MAAMgC,gBAAgB,GAAIC,OAAO,IAAK;IACpClC,aAAa,CAACmB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAElB,IAAI,EAAEiC;IAAQ,CAAC,CAAC,CAAC;EACrD,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAM1B,MAAM,GAAG;QACbC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUJ,KAAK;QAAG,CAAC;QAC7CK,MAAM,EAAElB,OAAO;QACf0C,YAAY,EAAE;MAChB,CAAC;MACD,MAAMvB,QAAQ,GAAG,MAAM1C,KAAK,CAAC2C,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,iCAAiC,EAAER,MAAM,CAAC;MAE3G,MAAM4B,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAAC5B,QAAQ,CAACK,IAAI,CAAC,CAAC,CAAC;MACjE,MAAMwB,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGR,GAAG;MACfK,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,gBAAgB,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;MAC3FN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACT,IAAI,CAAC;MAC/BA,IAAI,CAACU,KAAK,CAAC,CAAC;MACZV,IAAI,CAACW,MAAM,CAAC,CAAC;IACf,CAAC,CAAC,OAAOjC,GAAG,EAAE;MACZ3B,QAAQ,CAAC,6BAA6B,CAAC;IACzC;EACF,CAAC;EAED,MAAM6D,aAAa,GAAIC,MAAM,IAAK;IAChC,IAAIA,MAAM,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAID,MAAM,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,IAAI;IACtE,IAAID,MAAM,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAID,MAAM,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,GAAG;IACnE,IAAID,MAAM,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAID,MAAM,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,IAAI;IACrE,IAAID,MAAM,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAID,MAAM,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,KAAK;IACxE,IAAID,MAAM,CAACC,QAAQ,CAAC,MAAM,CAAC,IAAID,MAAM,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,KAAK;IACtE,OAAO,IAAI;EACb,CAAC;EAED,MAAMC,gBAAgB,GAAI5D,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,YAAY;QAAE,OAAO,+BAA+B;MACzD,KAAK,OAAO;QAAE,OAAO,2BAA2B;MAChD,KAAK,YAAY;QAAE,OAAO,6BAA6B;MACvD,KAAK,SAAS;QAAE,OAAO,+BAA+B;MACtD,KAAK,WAAW;QAAE,OAAO,+BAA+B;MACxD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,MAAM6D,SAAS,GAAG;IAChBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEE,UAAU,EAAE;QAAEC,eAAe,EAAE;MAAI;IAAE;EAC3D,CAAC;EAED,MAAMC,IAAI,GAAG;IACXL,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAG,CAAC;IAC7BJ,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAE;EAC3B,CAAC;EAED,IAAI3E,OAAO,EAAE,oBAAOP,OAAA,CAACJ,MAAM;IAAAuF,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAE9B,oBACEtF,OAAA;IAAKuF,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvCxF,OAAA,CAACF,iBAAiB;MAAC2F,MAAM,EAAEtF,WAAY;MAACuF,SAAS,EAAEtF;IAAe;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAErEtF,OAAA;MAAKuF,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDxF,OAAA,CAACL,MAAM;QAACgG,aAAa,EAAEA,CAAA,KAAMvF,cAAc,CAAC,CAACD,WAAW;MAAE;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE7DtF,OAAA;QAAMuF,SAAS,EAAC,0FAA0F;QAAAC,QAAA,eACxGxF,OAAA;UAAKuF,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAC/B/E,KAAK,iBACJT,OAAA,CAACX,MAAM,CAACuG,GAAG;YACTC,OAAO,EAAE;cAAEhB,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCY,OAAO,EAAE;cAAEjB,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE;YAAE,CAAE;YAC9BK,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAE7ExF,OAAA;cAAKuF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCxF,OAAA;gBAAK+F,KAAK,EAAC,4BAA4B;gBAACR,SAAS,EAAC,2BAA2B;gBAACS,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAT,QAAA,eACnHxF,OAAA;kBAAMkG,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,mNAAmN;kBAACC,QAAQ,EAAC;gBAAS;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjQ,CAAC,eACNtF,OAAA;gBAAGuF,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAE/E;cAAK;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eAEDtF,OAAA,CAACX,MAAM,CAACuG,GAAG;YACTC,OAAO,EAAE;cAAEhB,OAAO,EAAE;YAAE,CAAE;YACxBiB,OAAO,EAAE;cAAEjB,OAAO,EAAE;YAAE,CAAE;YACxBE,UAAU,EAAE;cAAEsB,QAAQ,EAAE;YAAI,CAAE;YAAAb,QAAA,gBAG9BxF,OAAA;cAAKuF,SAAS,EAAC,kFAAkF;cAAAC,QAAA,gBAC/FxF,OAAA;gBAAAwF,QAAA,gBACExF,OAAA;kBAAIuF,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAAC;gBAAY;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpFtF,OAAA;kBAAGuF,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAA0C;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACNtF,OAAA,CAACX,MAAM,CAACiH,MAAM;gBACZC,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAC1BE,OAAO,EAAEtD,gBAAiB;gBAC1BmC,SAAS,EAAC,oMAAoM;gBAAAC,QAAA,gBAE9MxF,OAAA,CAACP,UAAU;kBAAC8F,SAAS,EAAC;gBAAc;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,cAEzC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eAGNtF,OAAA,CAACX,MAAM,CAACuG,GAAG;cACTe,QAAQ,EAAE1B,IAAK;cACfM,SAAS,EAAC,4EAA4E;cAAAC,QAAA,gBAEtFxF,OAAA;gBAAKuF,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCxF,OAAA,CAACR,QAAQ;kBAAC+F,SAAS,EAAC;gBAA6B;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpDtF,OAAA;kBAAIuF,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAC;gBAAO;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACNtF,OAAA;gBAAKuF,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDxF,OAAA;kBAAAwF,QAAA,gBACExF,OAAA;oBAAOuF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAU;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAClFtF,OAAA;oBACEiD,KAAK,EAAEtC,OAAO,CAACE,SAAU;oBACzB+F,QAAQ,EAAGC,CAAC,IAAK9D,kBAAkB,CAAC,WAAW,EAAE8D,CAAC,CAACC,MAAM,CAAC7D,KAAK,CAAE;oBACjEsC,SAAS,EAAC,6GAA6G;oBAAAC,QAAA,gBAEvHxF,OAAA;sBAAQiD,KAAK,EAAC,MAAM;sBAAAuC,QAAA,EAAC;oBAAa;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC3CtF,OAAA;sBAAQiD,KAAK,EAAC,OAAO;sBAAAuC,QAAA,EAAC;oBAAW;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC1CtF,OAAA;sBAAQiD,KAAK,EAAC,QAAQ;sBAAAuC,QAAA,EAAC;oBAAY;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5CtF,OAAA;sBAAQiD,KAAK,EAAC,QAAQ;sBAAAuC,QAAA,EAAC;oBAAY;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5CtF,OAAA;sBAAQiD,KAAK,EAAC,KAAK;sBAAAuC,QAAA,EAAC;oBAAQ;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNtF,OAAA;kBAAAwF,QAAA,gBACExF,OAAA;oBAAOuF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAS;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACjFtF,OAAA;oBACEiD,KAAK,EAAEtC,OAAO,CAACG,QAAS;oBACxB8F,QAAQ,EAAGC,CAAC,IAAK9D,kBAAkB,CAAC,UAAU,EAAE8D,CAAC,CAACC,MAAM,CAAC7D,KAAK,CAAE;oBAChEsC,SAAS,EAAC,6GAA6G;oBAAAC,QAAA,gBAEvHxF,OAAA;sBAAQiD,KAAK,EAAC,KAAK;sBAAAuC,QAAA,EAAC;oBAAS;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtCtF,OAAA;sBAAQiD,KAAK,EAAC,YAAY;sBAAAuC,QAAA,EAAC;oBAAY;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAChDtF,OAAA;sBAAQiD,KAAK,EAAC,OAAO;sBAAAuC,QAAA,EAAC;oBAAM;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACrCtF,OAAA;sBAAQiD,KAAK,EAAC,YAAY;sBAAAuC,QAAA,EAAC;oBAAW;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC/CtF,OAAA;sBAAQiD,KAAK,EAAC,SAAS;sBAAAuC,QAAA,EAAC;oBAAQ;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACzCtF,OAAA;sBAAQiD,KAAK,EAAC,WAAW;sBAAAuC,QAAA,EAAC;oBAAU;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNtF,OAAA;kBAAAwF,QAAA,gBACExF,OAAA;oBAAOuF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAW;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnFtF,OAAA;oBACEiD,KAAK,EAAEtC,OAAO,CAACI,UAAW;oBAC1B6F,QAAQ,EAAGC,CAAC,IAAK9D,kBAAkB,CAAC,YAAY,EAAE8D,CAAC,CAACC,MAAM,CAAC7D,KAAK,CAAE;oBAClEsC,SAAS,EAAC,6GAA6G;oBAAAC,QAAA,gBAEvHxF,OAAA;sBAAQiD,KAAK,EAAC,KAAK;sBAAAuC,QAAA,EAAC;oBAAW;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxCtF,OAAA;sBAAQiD,KAAK,EAAC,MAAM;sBAAAuC,QAAA,EAAC;oBAAc;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5CtF,OAAA;sBAAQiD,KAAK,EAAC,QAAQ;sBAAAuC,QAAA,EAAC;oBAAM;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtCtF,OAAA;sBAAQiD,KAAK,EAAC,QAAQ;sBAAAuC,QAAA,EAAC;oBAAM;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtCtF,OAAA;sBAAQiD,KAAK,EAAC,QAAQ;sBAAAuC,QAAA,EAAC;oBAAM;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtCtF,OAAA;sBAAQiD,KAAK,EAAC,MAAM;sBAAAuC,QAAA,EAAC;oBAAI;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGbtF,OAAA,CAACX,MAAM,CAACuG,GAAG;cACTe,QAAQ,EAAEhC,SAAU;cACpBkB,OAAO,EAAC,QAAQ;cAChBkB,WAAW,EAAC,MAAM;cAClBC,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzB1B,SAAS,EAAC,mFAAmF;cAAAC,QAAA,eAE7FxF,OAAA;gBAAKuF,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBxF,OAAA;kBAAKuF,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,eACrDxF,OAAA;oBAAIuF,SAAS,EAAC,oDAAoD;oBAAAC,QAAA,gBAChExF,OAAA,CAACN,KAAK;sBAAC6F,SAAS,EAAC;oBAAc;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,uBACf,EAACtE,UAAU,CAACI,KAAK,EAAC,SACvC;kBAAA;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,EAELjF,UAAU,CAAC6G,MAAM,KAAK,CAAC,gBACtBlH,OAAA;kBAAKuF,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCxF,OAAA,CAACV,MAAM;oBAACiG,SAAS,EAAC;kBAAsC;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC3DtF,OAAA;oBAAIuF,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1EtF,OAAA;oBAAGuF,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAA+C;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC,gBAENtF,OAAA;kBAAKuF,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACvBnF,UAAU,CAAC8G,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,kBAC9BrH,OAAA,CAACX,MAAM,CAACuG,GAAG;oBAETe,QAAQ,EAAE1B,IAAK;oBACfM,SAAS,EAAC,0FAA0F;oBAAAC,QAAA,gBAEpGxF,OAAA;sBAAKuF,SAAS,EAAC,eAAe;sBAAAC,QAAA,eAC5BxF,OAAA;wBAAKuF,SAAS,EAAC,kFAAkF;wBAAAC,QAAA,eAC/FxF,OAAA;0BAAMuF,SAAS,EAAC,SAAS;0BAAAC,QAAA,EAAEjB,aAAa,CAAC6C,QAAQ,CAAC5C,MAAM;wBAAC;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9D;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNtF,OAAA;sBAAKuF,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,gBAC7BxF,OAAA;wBAAKuF,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChDxF,OAAA;0BAAKuF,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,gBAC1CxF,OAAA;4BAAGuF,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAAE4B,QAAQ,CAACE,QAAQ,IAAI;0BAAc;4BAAAnC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAC1FtF,OAAA;4BAAMuF,SAAS,EAAE,2EAA2Eb,gBAAgB,CAAC0C,QAAQ,CAACG,QAAQ,CAAC,EAAG;4BAAA/B,QAAA,EAC/H4B,QAAQ,CAACG,QAAQ,IAAI;0BAAS;4BAAApC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3B,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACNtF,OAAA;0BAAKuF,SAAS,EAAC,yCAAyC;0BAAAC,QAAA,gBACtDxF,OAAA,CAACT,aAAa;4BAACgG,SAAS,EAAC;0BAAc;4BAAAJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,EACzC,IAAItB,IAAI,CAACoD,QAAQ,CAACI,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;wBAAA;0BAAAtC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3C,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNtF,OAAA;wBAAGuF,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EAAE4B,QAAQ,CAAC5C;sBAAM;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EAC9D8B,QAAQ,CAACM,OAAO,iBACf1H,OAAA;wBAAGuF,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EAAE4B,QAAQ,CAACM;sBAAO;wBAAAvC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAChE;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA,GA1BD8B,QAAQ,CAACO,GAAG,IAAIN,KAAK;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA2BhB,CACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN,EAGAtE,UAAU,CAACK,UAAU,GAAG,CAAC,iBACxBrB,OAAA;kBAAKuF,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrDxF,OAAA;oBAAKuF,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,UAC7B,EAAE,CAACxE,UAAU,CAACE,IAAI,GAAG,CAAC,IAAIF,UAAU,CAACG,KAAK,GAAI,CAAC,EAAC,MAAI,EAACyG,IAAI,CAACC,GAAG,CAAC7G,UAAU,CAACE,IAAI,GAAGF,UAAU,CAACG,KAAK,EAAEH,UAAU,CAACI,KAAK,CAAC,EAAC,MAAI,EAACJ,UAAU,CAACI,KAAK,EAAC,UACpJ;kBAAA;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNtF,OAAA;oBAAKuF,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7BxF,OAAA;sBACE0G,OAAO,EAAEA,CAAA,KAAMxD,gBAAgB,CAAClC,UAAU,CAACE,IAAI,GAAG,CAAC,CAAE;sBACrD4G,QAAQ,EAAE9G,UAAU,CAACE,IAAI,KAAK,CAAE;sBAChCqE,SAAS,EAAC,yJAAyJ;sBAAAC,QAAA,EACpK;oBAED;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTtF,OAAA;sBAAMuF,SAAS,EAAC,0GAA0G;sBAAAC,QAAA,GACvHxE,UAAU,CAACE,IAAI,EAAC,MAAI,EAACF,UAAU,CAACK,UAAU;oBAAA;sBAAA8D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACPtF,OAAA;sBACE0G,OAAO,EAAEA,CAAA,KAAMxD,gBAAgB,CAAClC,UAAU,CAACE,IAAI,GAAG,CAAC,CAAE;sBACrD4G,QAAQ,EAAE9G,UAAU,CAACE,IAAI,KAAKF,UAAU,CAACK,UAAW;sBACpDkE,SAAS,EAAC,yJAAyJ;sBAAAC,QAAA,EACpK;oBAED;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpF,EAAA,CAhUID,QAAQ;EAAA,QAiBKd,WAAW,EACJU,OAAO;AAAA;AAAAkI,EAAA,GAlB3B9H,QAAQ;AAkUd,eAAeA,QAAQ;AAAC,IAAA8H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}