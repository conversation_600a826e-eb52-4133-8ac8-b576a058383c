{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\pages\\\\UniversityConfirmation.jsx\",\n  _s = $RefreshSig$();\nimport React, { useRef, useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useLocation } from 'react-router-dom';\nimport { QRCodeSVG } from 'qrcode.react';\nimport html2canvas from 'html2canvas';\nimport { jsPDF } from 'jspdf';\nimport Navbar from '../components/Navbar';\nimport Footer from '../components/Footer';\nimport Loader from '../components/Loader';\nimport { FaTooth } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Confirmation = () => {\n  _s();\n  const {\n    t,\n    i18n\n  } = useTranslation();\n  const location = useLocation();\n  const {\n    appointmentDetails\n  } = location.state || {};\n  const printRef = useRef();\n  const [isClient, setIsClient] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n  const [universityInfo, setUniversityInfo] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Static Dentlyzer information\n  const dentlyzerInfo = {\n    name: 'Odenta',\n    logo: '/imgs/dentlyzer-logo.png',\n    email: '<EMAIL>',\n    phone: '+20 12'\n  };\n  useEffect(() => {\n    setIsClient(true);\n    const handleResize = () => {\n      setIsMobile(window.innerWidth < 768);\n    };\n    handleResize();\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // Fetch university information from DB\n  // Fetch university information from DB\n  useEffect(() => {\n    const fetchUniversityInfo = async () => {\n      if (appointmentDetails !== null && appointmentDetails !== void 0 && appointmentDetails.universityClinic) {\n        setIsLoading(true);\n        try {\n          var _universityData$name, _universityData$conta, _universityData$conta2;\n          const response = await fetch(`${process.env.REACT_APP_API_URL}/api/universities?universityId=${encodeURIComponent(appointmentDetails.universityClinic)}`);\n          if (!response.ok) {\n            throw new Error(`Failed to fetch university: ${response.statusText}`);\n          }\n          const universities = await response.json();\n          if (universities.length === 0) {\n            throw new Error('University not found');\n          }\n          const universityData = universities[0];\n          console.log('University data:', universityData); // Debug log\n          if (!universityData.contactInfo) {\n            console.warn('contactInfo missing in university data:', universityData);\n          }\n          setUniversityInfo({\n            name: ((_universityData$name = universityData.name) === null || _universityData$name === void 0 ? void 0 : _universityData$name[i18n.language]) || universityData.universityId || 'Unknown University',\n            email: (_universityData$conta = universityData.contactInfo) === null || _universityData$conta === void 0 ? void 0 : _universityData$conta.email,\n            phone: (_universityData$conta2 = universityData.contactInfo) === null || _universityData$conta2 === void 0 ? void 0 : _universityData$conta2.phone,\n            logo: universityData.logo || '/imgs/default-university-logo.png'\n          });\n        } catch (error) {\n          console.error('Error fetching university info:', error.message, error.stack);\n          setError(t('appointment.FailedLoadUniversityInfo'));\n        } finally {\n          setIsLoading(false);\n        }\n      }\n    };\n    fetchUniversityInfo();\n  }, [appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.universityClinic, t, i18n.language]);\n  const downloadPDF = async () => {\n    const input = printRef.current;\n\n    // Ensure the element is fully rendered\n    input.style.display = 'block';\n    try {\n      const canvas = await html2canvas(input, {\n        scale: 2,\n        logging: false,\n        useCORS: true,\n        backgroundColor: '#ffffff'\n      });\n      const pdf = new jsPDF({\n        orientation: 'portrait',\n        unit: 'mm',\n        format: 'a4'\n      });\n      const imgWidth = 190;\n      const imgHeight = canvas.height * imgWidth / canvas.width;\n      pdf.addImage(canvas, 'PNG', 10, 10, imgWidth, imgHeight);\n      const filename = i18n.language === 'ar' ? `${t('appointment.AppointmentConfirmation')}-${(appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.universityClinic) || ''}-${(appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.appointmentId) || ''}.pdf` : `${(appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.universityClinic) || ''}-${t('appointment.AppointmentConfirmation')}-${(appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.appointmentId) || ''}.pdf`;\n      pdf.save(filename);\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      setError(t('appointment.FailedGeneratePDF'));\n    } finally {\n      input.style.display = '';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `min-h-screen flex flex-col bg-[#0077B6] bg-opacity-5 ${i18n.language === 'ar' ? 'text-right' : 'text-left'}`,\n    dir: i18n.language === 'ar' ? 'rtl' : 'ltr',\n    style: {\n      fontFamily: i18n.language === 'ar' ? 'sans-serif' : 'inherit'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"flex-grow pt-16 md:pt-24 pb-12 px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-3xl mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white shadow-lg rounded-xl overflow-hidden border border-[#20B2AA] border-opacity-30\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 md:p-8 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mx-auto flex items-center justify-center h-10 w-10 md:h-12 md:w-12 rounded-full bg-[#28A745] bg-opacity-20 mb-3 md:mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-5 w-5 md:h-6 md:w-6 text-[#28A745]\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M5 13l4 4L19 7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl md:text-2xl font-bold text-[#0077B6] mb-2\",\n              children: t('appointment.AppointmentConfirmed')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm md:text-base text-[#333333] mb-4 md:mb-6\",\n              children: t('appointment.SuccessfullyBookedUniversity')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6 p-4 bg-red-100 rounded-xl text-red-800\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: printRef,\n              className: \"bg-white p-4 md:p-6 mx-auto text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `flex ${i18n.language === 'ar' ? 'flex-row-reverse' : 'flex-row'} justify-between items-center mb-4 md:mb-6 border-b pb-4 md:pb-6`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [isLoading ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 155,\n                    columnNumber: 23\n                  }, this) : universityInfo ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-lg md:text-xl font-bold text-[#0077B6]\",\n                      children: universityInfo.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 158,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs md:text-sm text-[#333333]\",\n                      children: t('appointment.UniversityDentalCare')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 159,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 157,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-lg md:text-xl font-bold text-[#0077B6]\",\n                      children: (appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.universityClinic) || t('appointment.UnknownUniversity')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 163,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs md:text-sm text-[#333333]\",\n                      children: t('appointment.UniversityDentalCare')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 166,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 23\n                  }, this), universityInfo && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: universityInfo.logo,\n                    alt: `${universityInfo.name} Logo`,\n                    className: `h-10 md:h-14 object-contain ${i18n.language === 'ar' ? 'mr-2 md:mr-4' : 'ml-2 md:ml-4'}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `flex items-center ${i18n.language === 'ar' ? 'text-left' : 'text-right'}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-lg md:text-xl font-bold text-[#0077B6]\",\n                      children: /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: \"/imgs/odenta-logo2.jpg\" // Update this path based on your project structure\n                        ,\n                        alt: \"ODenta Logo\",\n                        className: \"h-10 w-auto\" // Adjust size as needed\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 180,\n                        columnNumber: 21\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 179,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs md:text-sm text-[#333333]\",\n                      children: t('appointment.DentalBookingPlatform')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 185,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(FaTooth, {\n                    className: \"w-10 h-10 text-[#0077B6]\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 22\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-b border-[#20B2AA] border-opacity-30 pb-3 md:pb-4 mb-4 md:mb-6\",\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-base md:text-lg font-semibold text-center text-[#0077B6]\",\n                  children: t('appointment.AppointmentConfirmation')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this), appointmentDetails && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3 md:space-y-4 mb-6 md:mb-8\",\n                children: [{\n                  label: t('appointment.AppointmentID'),\n                  value: appointmentDetails.appointmentId,\n                  highlight: true\n                }, {\n                  label: t('appointment.UniversityClinic'),\n                  value: appointmentDetails.universityClinic\n                }, {\n                  label: t('appointment.Date'),\n                  value: appointmentDetails.date\n                }, {\n                  label: t('appointment.Time'),\n                  value: appointmentDetails.timeSlot\n                }, {\n                  label: t('appointment.PatientName'),\n                  value: appointmentDetails.fullName\n                }, {\n                  label: t('appointment.PhoneNumber'),\n                  value: appointmentDetails.phoneNumber\n                }, {\n                  label: t('appointment.NationalID'),\n                  value: appointmentDetails.nationalId\n                }, {\n                  label: t('appointment.Age'),\n                  value: appointmentDetails.age\n                }, {\n                  label: t('appointment.Gender'),\n                  value: appointmentDetails.gender\n                }, {\n                  label: t('appointment.ChiefComplaint'),\n                  value: appointmentDetails.chiefComplaint\n                }, {\n                  label: t('appointment.Occupation'),\n                  value: appointmentDetails.occupation || 'N/A'\n                }, {\n                  label: t('appointment.Address'),\n                  value: appointmentDetails.address || 'N/A'\n                }].map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `flex ${i18n.language === 'ar' ? 'flex-row-reverse' : 'flex-row'} ${isMobile && i18n.language === 'ar' ? 'flex-col' : 'justify-between'} text-sm md:text-base mb-2`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `font-medium ${item.highlight ? 'text-[#0077B6]' : 'text-[#333333]'}`,\n                    children: [item.label, \":\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `${item.highlight ? 'font-bold text-[#0077B6]' : ''} ${isMobile && i18n.language === 'ar' ? 'mt-1' : ''} break-all ${isMobile && i18n.language === 'ar' ? 'w-full' : 'max-w-[50%]'} ${i18n.language === 'ar' ? 'text-right' : 'text-left'}`,\n                    children: item.value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col items-center mt-6 md:mt-8 mb-4 md:mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-3 md:p-4 bg-[#0077B6] bg-opacity-10 rounded-lg mb-3 md:mb-4\",\n                  children: isClient && /*#__PURE__*/_jsxDEV(QRCodeSVG, {\n                    value: appointmentDetails ? JSON.stringify({\n                      id: appointmentDetails.appointmentId,\n                      name: appointmentDetails.fullName,\n                      universityClinic: appointmentDetails.universityClinic,\n                      date: appointmentDetails.date,\n                      time: appointmentDetails.timeSlot\n                    }) : '',\n                    size: isMobile ? 100 : 140,\n                    level: \"H\",\n                    includeMargin: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs md:text-sm text-[#333333] text-opacity-70\",\n                  children: t('appointment.ScanQRCode')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `mt-6 md:mt-8 pt-4 md:pt-6 border-t border-[#20B2AA] border-opacity-30 text-xs md:text-sm ${i18n.language === 'ar' ? 'text-right' : 'text-left'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4 md:mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium mb-1 md:mb-2 text-[#0077B6]\",\n                    children: t('appointment.UniversityClinicContact')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 21\n                  }, this), isLoading ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 23\n                  }, this) : universityInfo ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"mb-1 text-[#333333]\",\n                      children: [t('appointment.Email'), \": \", universityInfo.email]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 272,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-[#333333]\",\n                      children: [t('appointment.Phone'), \": \", universityInfo.phone]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 275,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-[#333333] text-opacity-70\",\n                    children: t('appointment.NoContactInfoAvailable')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4 md:mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium mb-1 md:mb-2 text-[#0077B6]\",\n                    children: t('appointment.DentlyzerContact')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 284,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mb-1 text-[#333333]\",\n                    children: [t('appointment.Email'), \": \", dentlyzerInfo.email]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-[#333333]\",\n                    children: [t('appointment.Phone'), \": \", dentlyzerInfo.phone]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6 md:mt-8 flex flex-col sm:flex-row justify-center gap-3 md:gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: downloadPDF,\n                className: \"inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 border border-transparent text-xs md:text-sm font-medium rounded-md shadow-sm text-white bg-[#0077B6] hover:bg-[#0066A0] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0077B6]\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-4 w-4 md:h-5 md:w-5 mr-1 md:mr-2\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: \"2\",\n                    d: \"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 19\n                }, this), t('appointment.DownloadConfirmation')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/universities\",\n                className: \"inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 border border-[#20B2AA] border-opacity-50 text-xs md:text-sm font-medium rounded-md shadow-sm text-[#333333] bg-white hover:bg-[#0077B6] hover:bg-opacity-5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0077B6]\",\n                children: t('appointment.BackToUniversities')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 123,\n    columnNumber: 5\n  }, this);\n};\n_s(Confirmation, \"cpqCYAHka6+yAhEho/rxavY8D4Q=\", false, function () {\n  return [useTranslation, useLocation];\n});\n_c = Confirmation;\nexport default Confirmation;\nvar _c;\n$RefreshReg$(_c, \"Confirmation\");", "map": {"version": 3, "names": ["React", "useRef", "useState", "useEffect", "useTranslation", "useLocation", "QRCodeSVG", "html2canvas", "jsPDF", "<PERSON><PERSON><PERSON>", "Footer", "Loader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Confirmation", "_s", "t", "i18n", "location", "appointmentDetails", "state", "printRef", "isClient", "setIsClient", "isMobile", "setIsMobile", "universityInfo", "setUniversityInfo", "isLoading", "setIsLoading", "error", "setError", "dentlyzerInfo", "name", "logo", "email", "phone", "handleResize", "window", "innerWidth", "addEventListener", "removeEventListener", "fetchUniversityInfo", "universityClinic", "_universityData$name", "_universityData$conta", "_universityData$conta2", "response", "fetch", "process", "env", "REACT_APP_API_URL", "encodeURIComponent", "ok", "Error", "statusText", "universities", "json", "length", "universityData", "console", "log", "contactInfo", "warn", "language", "universityId", "message", "stack", "downloadPDF", "input", "current", "style", "display", "canvas", "scale", "logging", "useCORS", "backgroundColor", "pdf", "orientation", "unit", "format", "imgWidth", "imgHeight", "height", "width", "addImage", "filename", "appointmentId", "save", "className", "dir", "fontFamily", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "ref", "src", "alt", "label", "value", "highlight", "date", "timeSlot", "fullName", "phoneNumber", "nationalId", "age", "gender", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "occupation", "address", "map", "item", "index", "JSON", "stringify", "id", "time", "size", "level", "<PERSON><PERSON><PERSON><PERSON>", "onClick", "href", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/pages/UniversityConfirmation.jsx"], "sourcesContent": ["import React, { useRef, useState, useEffect } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { useLocation } from 'react-router-dom';\r\nimport { QRCodeSVG } from 'qrcode.react';\r\nimport html2canvas from 'html2canvas';\r\nimport { jsPDF } from 'jspdf';\r\nimport Navbar from '../components/Navbar';\r\nimport Footer from '../components/Footer';\r\nimport Loader from '../components/Loader';\r\nimport { FaTooth } from 'react-icons/fa';\r\n\r\n\r\nconst Confirmation = () => {\r\n  const { t, i18n } = useTranslation();\r\n  const location = useLocation();\r\n  const { appointmentDetails } = location.state || {};\r\n  const printRef = useRef();\r\n  const [isClient, setIsClient] = useState(false);\r\n  const [isMobile, setIsMobile] = useState(false);\r\n  const [universityInfo, setUniversityInfo] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n\r\n  // Static Dentlyzer information\r\n  const dentlyzerInfo = {\r\n    name: 'Odenta',\r\n    logo: '/imgs/dentlyzer-logo.png',\r\n    email: '<EMAIL>',\r\n    phone: '+20 12',\r\n  };\r\n\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth < 768);\r\n    };\r\n    handleResize();\r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, []);\r\n\r\n  // Fetch university information from DB\r\n  // Fetch university information from DB\r\nuseEffect(() => {\r\n  const fetchUniversityInfo = async () => {\r\n    if (appointmentDetails?.universityClinic) {\r\n      setIsLoading(true);\r\n      try {\r\n        const response = await fetch(\r\n          `${process.env.REACT_APP_API_URL}/api/universities?universityId=${encodeURIComponent(\r\n            appointmentDetails.universityClinic\r\n          )}`\r\n        );\r\n        if (!response.ok) {\r\n          throw new Error(`Failed to fetch university: ${response.statusText}`);\r\n        }\r\n        const universities = await response.json();\r\n        if (universities.length === 0) {\r\n          throw new Error('University not found');\r\n        }\r\n        const universityData = universities[0];\r\n        console.log('University data:', universityData); // Debug log\r\n        if (!universityData.contactInfo) {\r\n          console.warn('contactInfo missing in university data:', universityData);\r\n        }\r\n        setUniversityInfo({\r\n          name: universityData.name?.[i18n.language] || universityData.universityId || 'Unknown University',\r\n          email: universityData.contactInfo?.email,\r\n          phone: universityData.contactInfo?.phone,\r\n          logo: universityData.logo || '/imgs/default-university-logo.png',\r\n        });\r\n      } catch (error) {\r\n        console.error('Error fetching university info:', error.message, error.stack);\r\n        setError(t('appointment.FailedLoadUniversityInfo'));\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    }\r\n  };\r\n  fetchUniversityInfo();\r\n}, [appointmentDetails?.universityClinic, t, i18n.language]);\r\n\r\n  const downloadPDF = async () => {\r\n    const input = printRef.current;\r\n\r\n    // Ensure the element is fully rendered\r\n    input.style.display = 'block';\r\n\r\n    try {\r\n      const canvas = await html2canvas(input, {\r\n        scale: 2,\r\n        logging: false,\r\n        useCORS: true,\r\n        backgroundColor: '#ffffff',\r\n      });\r\n\r\n      const pdf = new jsPDF({\r\n        orientation: 'portrait',\r\n        unit: 'mm',\r\n        format: 'a4',\r\n      });\r\n\r\n      const imgWidth = 190;\r\n      const imgHeight = (canvas.height * imgWidth) / canvas.width;\r\n\r\n      pdf.addImage(canvas, 'PNG', 10, 10, imgWidth, imgHeight);\r\n\r\n      const filename =\r\n        i18n.language === 'ar'\r\n          ? `${t('appointment.AppointmentConfirmation')}-${appointmentDetails?.universityClinic || ''}-${appointmentDetails?.appointmentId || ''}.pdf`\r\n          : `${appointmentDetails?.universityClinic || ''}-${t('appointment.AppointmentConfirmation')}-${appointmentDetails?.appointmentId || ''}.pdf`;\r\n\r\n      pdf.save(filename);\r\n    } catch (error) {\r\n      console.error('Error generating PDF:', error);\r\n      setError(t('appointment.FailedGeneratePDF'));\r\n    } finally {\r\n      input.style.display = '';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`min-h-screen flex flex-col bg-[#0077B6] bg-opacity-5 ${i18n.language === 'ar' ? 'text-right' : 'text-left'}`}\r\n      dir={i18n.language === 'ar' ? 'rtl' : 'ltr'}\r\n      style={{ fontFamily: i18n.language === 'ar' ? 'sans-serif' : 'inherit' }}\r\n    >\r\n      <Navbar />\r\n      <main className=\"flex-grow pt-16 md:pt-24 pb-12 px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"max-w-3xl mx-auto\">\r\n          <div className=\"bg-white shadow-lg rounded-xl overflow-hidden border border-[#20B2AA] border-opacity-30\">\r\n            <div className=\"p-4 md:p-8 text-center\">\r\n              <div className=\"mx-auto flex items-center justify-center h-10 w-10 md:h-12 md:w-12 rounded-full bg-[#28A745] bg-opacity-20 mb-3 md:mb-4\">\r\n                <svg className=\"h-5 w-5 md:h-6 md:w-6 text-[#28A745]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\r\n                </svg>\r\n              </div>\r\n              <h1 className=\"text-xl md:text-2xl font-bold text-[#0077B6] mb-2\">\r\n                {t('appointment.AppointmentConfirmed')}\r\n              </h1>\r\n              <p className=\"text-sm md:text-base text-[#333333] mb-4 md:mb-6\">\r\n                {t('appointment.SuccessfullyBookedUniversity')}\r\n              </p>\r\n\r\n              {error && (\r\n                <div className=\"mb-6 p-4 bg-red-100 rounded-xl text-red-800\">\r\n                  {error}\r\n                </div>\r\n              )}\r\n\r\n              <div ref={printRef} className=\"bg-white p-4 md:p-6 mx-auto text-left\">\r\n                <div className={`flex ${i18n.language === 'ar' ? 'flex-row-reverse' : 'flex-row'} justify-between items-center mb-4 md:mb-6 border-b pb-4 md:pb-6`}>\r\n                  <div className=\"flex items-center\">\r\n                    {isLoading ? (\r\n                      <Loader />\r\n                    ) : universityInfo ? (\r\n                      <div>\r\n                        <h2 className=\"text-lg md:text-xl font-bold text-[#0077B6]\">{universityInfo.name}</h2>\r\n                        <p className=\"text-xs md:text-sm text-[#333333]\">{t('appointment.UniversityDentalCare')}</p>\r\n                      </div>\r\n                    ) : (\r\n                      <div>\r\n                        <h2 className=\"text-lg md:text-xl font-bold text-[#0077B6]\">\r\n                          {appointmentDetails?.universityClinic || t('appointment.UnknownUniversity')}\r\n                        </h2>\r\n                        <p className=\"text-xs md:text-sm text-[#333333]\">{t('appointment.UniversityDentalCare')}</p>\r\n                      </div>\r\n                    )}\r\n                    {universityInfo && (\r\n                      <img\r\n                        src={universityInfo.logo}\r\n                        alt={`${universityInfo.name} Logo`}\r\n                        className={`h-10 md:h-14 object-contain ${i18n.language === 'ar' ? 'mr-2 md:mr-4' : 'ml-2 md:ml-4'}`}\r\n                      />\r\n                    )}\r\n                  </div>\r\n                  <div className={`flex items-center ${i18n.language === 'ar' ? 'text-left' : 'text-right'}`}>\r\n                    <div>\r\n                      <h2 className=\"text-lg md:text-xl font-bold text-[#0077B6]\">\r\n                    <img \r\n                      src=\"/imgs/odenta-logo2.jpg\" // Update this path based on your project structure\r\n                      alt=\"ODenta Logo\"\r\n                      className=\"h-10 w-auto\" // Adjust size as needed\r\n                    /></h2>\r\n                      <p className=\"text-xs md:text-sm text-[#333333]\">{t('appointment.DentalBookingPlatform')}</p>\r\n                    </div>\r\n                     <FaTooth className=\"w-10 h-10 text-[#0077B6]\" />\r\n\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"border-b border-[#20B2AA] border-opacity-30 pb-3 md:pb-4 mb-4 md:mb-6\">\r\n                  <h3 className=\"text-base md:text-lg font-semibold text-center text-[#0077B6]\">\r\n                    {t('appointment.AppointmentConfirmation')}\r\n                  </h3>\r\n                </div>\r\n\r\n                {appointmentDetails && (\r\n                  <div className=\"space-y-3 md:space-y-4 mb-6 md:mb-8\">\r\n                    {[\r\n                      { label: t('appointment.AppointmentID'), value: appointmentDetails.appointmentId, highlight: true },\r\n                      { label: t('appointment.UniversityClinic'), value: appointmentDetails.universityClinic },\r\n                      { label: t('appointment.Date'), value: appointmentDetails.date },\r\n                      { label: t('appointment.Time'), value: appointmentDetails.timeSlot },\r\n                      { label: t('appointment.PatientName'), value: appointmentDetails.fullName },\r\n                      { label: t('appointment.PhoneNumber'), value: appointmentDetails.phoneNumber },\r\n                      { label: t('appointment.NationalID'), value: appointmentDetails.nationalId },\r\n                      { label: t('appointment.Age'), value: appointmentDetails.age },\r\n                      { label: t('appointment.Gender'), value: appointmentDetails.gender },\r\n                      { label: t('appointment.ChiefComplaint'), value: appointmentDetails.chiefComplaint },\r\n                      { label: t('appointment.Occupation'), value: appointmentDetails.occupation || 'N/A' },\r\n                      { label: t('appointment.Address'), value: appointmentDetails.address || 'N/A' },\r\n                    ].map((item, index) => (\r\n                      <div\r\n                        key={index}\r\n                        className={`flex ${i18n.language === 'ar' ? 'flex-row-reverse' : 'flex-row'} ${\r\n                          isMobile && i18n.language === 'ar' ? 'flex-col' : 'justify-between'\r\n                        } text-sm md:text-base mb-2`}\r\n                      >\r\n                        <span className={`font-medium ${item.highlight ? 'text-[#0077B6]' : 'text-[#333333]'}`}>\r\n                          {item.label}:\r\n                        </span>\r\n                        <span\r\n                          className={`${item.highlight ? 'font-bold text-[#0077B6]' : ''} ${\r\n                            isMobile && i18n.language === 'ar' ? 'mt-1' : ''\r\n                          } break-all ${isMobile && i18n.language === 'ar' ? 'w-full' : 'max-w-[50%]'} ${\r\n                            i18n.language === 'ar' ? 'text-right' : 'text-left'\r\n                          }`}\r\n                        >\r\n                          {item.value}\r\n                        </span>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                )}\r\n\r\n                <div className=\"flex flex-col items-center mt-6 md:mt-8 mb-4 md:mb-6\">\r\n                  <div className=\"p-3 md:p-4 bg-[#0077B6] bg-opacity-10 rounded-lg mb-3 md:mb-4\">\r\n                    {isClient && (\r\n                      <QRCodeSVG\r\n                        value={\r\n                          appointmentDetails\r\n                            ? JSON.stringify({\r\n                                id: appointmentDetails.appointmentId,\r\n                                name: appointmentDetails.fullName,\r\n                                universityClinic: appointmentDetails.universityClinic,\r\n                                date: appointmentDetails.date,\r\n                                time: appointmentDetails.timeSlot,\r\n                              })\r\n                            : ''\r\n                        }\r\n                        size={isMobile ? 100 : 140}\r\n                        level=\"H\"\r\n                        includeMargin={true}\r\n                      />\r\n                    )}\r\n                  </div>\r\n                  <p className=\"text-xs md:text-sm text-[#333333] text-opacity-70\">{t('appointment.ScanQRCode')}</p>\r\n                </div>\r\n\r\n                <div\r\n                  className={`mt-6 md:mt-8 pt-4 md:pt-6 border-t border-[#20B2AA] border-opacity-30 text-xs md:text-sm ${\r\n                    i18n.language === 'ar' ? 'text-right' : 'text-left'\r\n                  }`}\r\n                >\r\n                  <div className=\"mb-4 md:mb-6\">\r\n                    <h4 className=\"font-medium mb-1 md:mb-2 text-[#0077B6]\">{t('appointment.UniversityClinicContact')}</h4>\r\n                    {isLoading ? (\r\n                      <Loader />\r\n                    ) : universityInfo ? (\r\n                      <>\r\n                        <p className=\"mb-1 text-[#333333]\">\r\n                          {t('appointment.Email')}: {universityInfo.email}\r\n                        </p>\r\n                        <p className=\"text-[#333333]\">\r\n                          {t('appointment.Phone')}: {universityInfo.phone}\r\n                        </p>\r\n                      </>\r\n                    ) : (\r\n                      <p className=\"text-[#333333] text-opacity-70\">{t('appointment.NoContactInfoAvailable')}</p>\r\n                    )}\r\n                  </div>\r\n                  <div className=\"mb-4 md:mb-6\">\r\n                    <h4 className=\"font-medium mb-1 md:mb-2 text-[#0077B6]\">{t('appointment.DentlyzerContact')}</h4>\r\n                    <p className=\"mb-1 text-[#333333]\">\r\n                      {t('appointment.Email')}: {dentlyzerInfo.email}\r\n                    </p>\r\n                    <p className=\"text-[#333333]\">\r\n                      {t('appointment.Phone')}: {dentlyzerInfo.phone}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"mt-6 md:mt-8 flex flex-col sm:flex-row justify-center gap-3 md:gap-4\">\r\n                <button\r\n                  onClick={downloadPDF}\r\n                  className=\"inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 border border-transparent text-xs md:text-sm font-medium rounded-md shadow-sm text-white bg-[#0077B6] hover:bg-[#0066A0] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0077B6]\"\r\n                >\r\n                  <svg\r\n                    className=\"h-4 w-4 md:h-5 md:w-5 mr-1 md:mr-2\"\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                    viewBox=\"0 0 24 24\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      strokeWidth=\"2\"\r\n                      d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"\r\n                    />\r\n                  </svg>\r\n                  {t('appointment.DownloadConfirmation')}\r\n                </button>\r\n                <a\r\n                  href=\"/universities\"\r\n                  className=\"inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 border border-[#20B2AA] border-opacity-50 text-xs md:text-sm font-medium rounded-md shadow-sm text-[#333333] bg-white hover:bg-[#0077B6] hover:bg-opacity-5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0077B6]\"\r\n                >\r\n                  {t('appointment.BackToUniversities')}\r\n                </a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </main>\r\n      <Footer />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Confirmation;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAOC,WAAW,MAAM,aAAa;AACrC,SAASC,KAAK,QAAQ,OAAO;AAC7B,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGzC,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC,CAAC;IAAEC;EAAK,CAAC,GAAGhB,cAAc,CAAC,CAAC;EACpC,MAAMiB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiB;EAAmB,CAAC,GAAGD,QAAQ,CAACE,KAAK,IAAI,CAAC,CAAC;EACnD,MAAMC,QAAQ,GAAGvB,MAAM,CAAC,CAAC;EACzB,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAMiC,aAAa,GAAG;IACpBC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,0BAA0B;IAChCC,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE;EACT,CAAC;EAEDpC,SAAS,CAAC,MAAM;IACduB,WAAW,CAAC,IAAI,CAAC;IACjB,MAAMc,YAAY,GAAGA,CAAA,KAAM;MACzBZ,WAAW,CAACa,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC;IACtC,CAAC;IACDF,YAAY,CAAC,CAAC;IACdC,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAC/C,OAAO,MAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACFrC,SAAS,CAAC,MAAM;IACd,MAAM0C,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACtC,IAAIvB,kBAAkB,aAAlBA,kBAAkB,eAAlBA,kBAAkB,CAAEwB,gBAAgB,EAAE;QACxCd,YAAY,CAAC,IAAI,CAAC;QAClB,IAAI;UAAA,IAAAe,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA;UACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,kCAAkCC,kBAAkB,CAClFjC,kBAAkB,CAACwB,gBACrB,CAAC,EACH,CAAC;UACD,IAAI,CAACI,QAAQ,CAACM,EAAE,EAAE;YAChB,MAAM,IAAIC,KAAK,CAAC,+BAA+BP,QAAQ,CAACQ,UAAU,EAAE,CAAC;UACvE;UACA,MAAMC,YAAY,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;UAC1C,IAAID,YAAY,CAACE,MAAM,KAAK,CAAC,EAAE;YAC7B,MAAM,IAAIJ,KAAK,CAAC,sBAAsB,CAAC;UACzC;UACA,MAAMK,cAAc,GAAGH,YAAY,CAAC,CAAC,CAAC;UACtCI,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,cAAc,CAAC,CAAC,CAAC;UACjD,IAAI,CAACA,cAAc,CAACG,WAAW,EAAE;YAC/BF,OAAO,CAACG,IAAI,CAAC,yCAAyC,EAAEJ,cAAc,CAAC;UACzE;UACAhC,iBAAiB,CAAC;YAChBM,IAAI,EAAE,EAAAW,oBAAA,GAAAe,cAAc,CAAC1B,IAAI,cAAAW,oBAAA,uBAAnBA,oBAAA,CAAsB3B,IAAI,CAAC+C,QAAQ,CAAC,KAAIL,cAAc,CAACM,YAAY,IAAI,oBAAoB;YACjG9B,KAAK,GAAAU,qBAAA,GAAEc,cAAc,CAACG,WAAW,cAAAjB,qBAAA,uBAA1BA,qBAAA,CAA4BV,KAAK;YACxCC,KAAK,GAAAU,sBAAA,GAAEa,cAAc,CAACG,WAAW,cAAAhB,sBAAA,uBAA1BA,sBAAA,CAA4BV,KAAK;YACxCF,IAAI,EAAEyB,cAAc,CAACzB,IAAI,IAAI;UAC/B,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOJ,KAAK,EAAE;UACd8B,OAAO,CAAC9B,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAACoC,OAAO,EAAEpC,KAAK,CAACqC,KAAK,CAAC;UAC5EpC,QAAQ,CAACf,CAAC,CAAC,sCAAsC,CAAC,CAAC;QACrD,CAAC,SAAS;UACRa,YAAY,CAAC,KAAK,CAAC;QACrB;MACF;IACF,CAAC;IACDa,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACvB,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEwB,gBAAgB,EAAE3B,CAAC,EAAEC,IAAI,CAAC+C,QAAQ,CAAC,CAAC;EAE1D,MAAMI,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,MAAMC,KAAK,GAAGhD,QAAQ,CAACiD,OAAO;;IAE9B;IACAD,KAAK,CAACE,KAAK,CAACC,OAAO,GAAG,OAAO;IAE7B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMrE,WAAW,CAACiE,KAAK,EAAE;QACtCK,KAAK,EAAE,CAAC;QACRC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,IAAI;QACbC,eAAe,EAAE;MACnB,CAAC,CAAC;MAEF,MAAMC,GAAG,GAAG,IAAIzE,KAAK,CAAC;QACpB0E,WAAW,EAAE,UAAU;QACvBC,IAAI,EAAE,IAAI;QACVC,MAAM,EAAE;MACV,CAAC,CAAC;MAEF,MAAMC,QAAQ,GAAG,GAAG;MACpB,MAAMC,SAAS,GAAIV,MAAM,CAACW,MAAM,GAAGF,QAAQ,GAAIT,MAAM,CAACY,KAAK;MAE3DP,GAAG,CAACQ,QAAQ,CAACb,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAES,QAAQ,EAAEC,SAAS,CAAC;MAExD,MAAMI,QAAQ,GACZtE,IAAI,CAAC+C,QAAQ,KAAK,IAAI,GAClB,GAAGhD,CAAC,CAAC,qCAAqC,CAAC,IAAI,CAAAG,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEwB,gBAAgB,KAAI,EAAE,IAAI,CAAAxB,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEqE,aAAa,KAAI,EAAE,MAAM,GAC1I,GAAG,CAAArE,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEwB,gBAAgB,KAAI,EAAE,IAAI3B,CAAC,CAAC,qCAAqC,CAAC,IAAI,CAAAG,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEqE,aAAa,KAAI,EAAE,MAAM;MAEhJV,GAAG,CAACW,IAAI,CAACF,QAAQ,CAAC;IACpB,CAAC,CAAC,OAAOzD,KAAK,EAAE;MACd8B,OAAO,CAAC9B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAACf,CAAC,CAAC,+BAA+B,CAAC,CAAC;IAC9C,CAAC,SAAS;MACRqD,KAAK,CAACE,KAAK,CAACC,OAAO,GAAG,EAAE;IAC1B;EACF,CAAC;EAED,oBACE7D,OAAA;IACE+E,SAAS,EAAE,wDAAwDzE,IAAI,CAAC+C,QAAQ,KAAK,IAAI,GAAG,YAAY,GAAG,WAAW,EAAG;IACzH2B,GAAG,EAAE1E,IAAI,CAAC+C,QAAQ,KAAK,IAAI,GAAG,KAAK,GAAG,KAAM;IAC5CO,KAAK,EAAE;MAAEqB,UAAU,EAAE3E,IAAI,CAAC+C,QAAQ,KAAK,IAAI,GAAG,YAAY,GAAG;IAAU,CAAE;IAAA6B,QAAA,gBAEzElF,OAAA,CAACL,MAAM;MAAAwF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVtF,OAAA;MAAM+E,SAAS,EAAC,qDAAqD;MAAAG,QAAA,eACnElF,OAAA;QAAK+E,SAAS,EAAC,mBAAmB;QAAAG,QAAA,eAChClF,OAAA;UAAK+E,SAAS,EAAC,yFAAyF;UAAAG,QAAA,eACtGlF,OAAA;YAAK+E,SAAS,EAAC,wBAAwB;YAAAG,QAAA,gBACrClF,OAAA;cAAK+E,SAAS,EAAC,yHAAyH;cAAAG,QAAA,eACtIlF,OAAA;gBAAK+E,SAAS,EAAC,sCAAsC;gBAACQ,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAP,QAAA,eACzGlF,OAAA;kBAAM0F,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,CAAC,EAAC;gBAAgB;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtF,OAAA;cAAI+E,SAAS,EAAC,mDAAmD;cAAAG,QAAA,EAC9D7E,CAAC,CAAC,kCAAkC;YAAC;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACLtF,OAAA;cAAG+E,SAAS,EAAC,kDAAkD;cAAAG,QAAA,EAC5D7E,CAAC,CAAC,0CAA0C;YAAC;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,EAEHnE,KAAK,iBACJnB,OAAA;cAAK+E,SAAS,EAAC,6CAA6C;cAAAG,QAAA,EACzD/D;YAAK;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,eAEDtF,OAAA;cAAK8F,GAAG,EAAEpF,QAAS;cAACqE,SAAS,EAAC,uCAAuC;cAAAG,QAAA,gBACnElF,OAAA;gBAAK+E,SAAS,EAAE,QAAQzE,IAAI,CAAC+C,QAAQ,KAAK,IAAI,GAAG,kBAAkB,GAAG,UAAU,kEAAmE;gBAAA6B,QAAA,gBACjJlF,OAAA;kBAAK+E,SAAS,EAAC,mBAAmB;kBAAAG,QAAA,GAC/BjE,SAAS,gBACRjB,OAAA,CAACH,MAAM;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,GACRvE,cAAc,gBAChBf,OAAA;oBAAAkF,QAAA,gBACElF,OAAA;sBAAI+E,SAAS,EAAC,6CAA6C;sBAAAG,QAAA,EAAEnE,cAAc,CAACO;oBAAI;sBAAA6D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACtFtF,OAAA;sBAAG+E,SAAS,EAAC,mCAAmC;sBAAAG,QAAA,EAAE7E,CAAC,CAAC,kCAAkC;oBAAC;sBAAA8E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzF,CAAC,gBAENtF,OAAA;oBAAAkF,QAAA,gBACElF,OAAA;sBAAI+E,SAAS,EAAC,6CAA6C;sBAAAG,QAAA,EACxD,CAAA1E,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEwB,gBAAgB,KAAI3B,CAAC,CAAC,+BAA+B;oBAAC;sBAAA8E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzE,CAAC,eACLtF,OAAA;sBAAG+E,SAAS,EAAC,mCAAmC;sBAAAG,QAAA,EAAE7E,CAAC,CAAC,kCAAkC;oBAAC;sBAAA8E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzF,CACN,EACAvE,cAAc,iBACbf,OAAA;oBACE+F,GAAG,EAAEhF,cAAc,CAACQ,IAAK;oBACzByE,GAAG,EAAE,GAAGjF,cAAc,CAACO,IAAI,OAAQ;oBACnCyD,SAAS,EAAE,+BAA+BzE,IAAI,CAAC+C,QAAQ,KAAK,IAAI,GAAG,cAAc,GAAG,cAAc;kBAAG;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtG,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNtF,OAAA;kBAAK+E,SAAS,EAAE,qBAAqBzE,IAAI,CAAC+C,QAAQ,KAAK,IAAI,GAAG,WAAW,GAAG,YAAY,EAAG;kBAAA6B,QAAA,gBACzFlF,OAAA;oBAAAkF,QAAA,gBACElF,OAAA;sBAAI+E,SAAS,EAAC,6CAA6C;sBAAAG,QAAA,eAC7DlF,OAAA;wBACE+F,GAAG,EAAC,wBAAwB,CAAC;wBAAA;wBAC7BC,GAAG,EAAC,aAAa;wBACjBjB,SAAS,EAAC,aAAa,CAAC;sBAAA;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLtF,OAAA;sBAAG+E,SAAS,EAAC,mCAAmC;sBAAAG,QAAA,EAAE7E,CAAC,CAAC,mCAAmC;oBAAC;sBAAA8E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1F,CAAC,eACLtF,OAAA,CAACF,OAAO;oBAACiF,SAAS,EAAC;kBAA0B;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtF,OAAA;gBAAK+E,SAAS,EAAC,uEAAuE;gBAAAG,QAAA,eACpFlF,OAAA;kBAAI+E,SAAS,EAAC,+DAA+D;kBAAAG,QAAA,EAC1E7E,CAAC,CAAC,qCAAqC;gBAAC;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,EAEL9E,kBAAkB,iBACjBR,OAAA;gBAAK+E,SAAS,EAAC,qCAAqC;gBAAAG,QAAA,EACjD,CACC;kBAAEe,KAAK,EAAE5F,CAAC,CAAC,2BAA2B,CAAC;kBAAE6F,KAAK,EAAE1F,kBAAkB,CAACqE,aAAa;kBAAEsB,SAAS,EAAE;gBAAK,CAAC,EACnG;kBAAEF,KAAK,EAAE5F,CAAC,CAAC,8BAA8B,CAAC;kBAAE6F,KAAK,EAAE1F,kBAAkB,CAACwB;gBAAiB,CAAC,EACxF;kBAAEiE,KAAK,EAAE5F,CAAC,CAAC,kBAAkB,CAAC;kBAAE6F,KAAK,EAAE1F,kBAAkB,CAAC4F;gBAAK,CAAC,EAChE;kBAAEH,KAAK,EAAE5F,CAAC,CAAC,kBAAkB,CAAC;kBAAE6F,KAAK,EAAE1F,kBAAkB,CAAC6F;gBAAS,CAAC,EACpE;kBAAEJ,KAAK,EAAE5F,CAAC,CAAC,yBAAyB,CAAC;kBAAE6F,KAAK,EAAE1F,kBAAkB,CAAC8F;gBAAS,CAAC,EAC3E;kBAAEL,KAAK,EAAE5F,CAAC,CAAC,yBAAyB,CAAC;kBAAE6F,KAAK,EAAE1F,kBAAkB,CAAC+F;gBAAY,CAAC,EAC9E;kBAAEN,KAAK,EAAE5F,CAAC,CAAC,wBAAwB,CAAC;kBAAE6F,KAAK,EAAE1F,kBAAkB,CAACgG;gBAAW,CAAC,EAC5E;kBAAEP,KAAK,EAAE5F,CAAC,CAAC,iBAAiB,CAAC;kBAAE6F,KAAK,EAAE1F,kBAAkB,CAACiG;gBAAI,CAAC,EAC9D;kBAAER,KAAK,EAAE5F,CAAC,CAAC,oBAAoB,CAAC;kBAAE6F,KAAK,EAAE1F,kBAAkB,CAACkG;gBAAO,CAAC,EACpE;kBAAET,KAAK,EAAE5F,CAAC,CAAC,4BAA4B,CAAC;kBAAE6F,KAAK,EAAE1F,kBAAkB,CAACmG;gBAAe,CAAC,EACpF;kBAAEV,KAAK,EAAE5F,CAAC,CAAC,wBAAwB,CAAC;kBAAE6F,KAAK,EAAE1F,kBAAkB,CAACoG,UAAU,IAAI;gBAAM,CAAC,EACrF;kBAAEX,KAAK,EAAE5F,CAAC,CAAC,qBAAqB,CAAC;kBAAE6F,KAAK,EAAE1F,kBAAkB,CAACqG,OAAO,IAAI;gBAAM,CAAC,CAChF,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAChBhH,OAAA;kBAEE+E,SAAS,EAAE,QAAQzE,IAAI,CAAC+C,QAAQ,KAAK,IAAI,GAAG,kBAAkB,GAAG,UAAU,IACzExC,QAAQ,IAAIP,IAAI,CAAC+C,QAAQ,KAAK,IAAI,GAAG,UAAU,GAAG,iBAAiB,4BACxC;kBAAA6B,QAAA,gBAE7BlF,OAAA;oBAAM+E,SAAS,EAAE,eAAegC,IAAI,CAACZ,SAAS,GAAG,gBAAgB,GAAG,gBAAgB,EAAG;oBAAAjB,QAAA,GACpF6B,IAAI,CAACd,KAAK,EAAC,GACd;kBAAA;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPtF,OAAA;oBACE+E,SAAS,EAAE,GAAGgC,IAAI,CAACZ,SAAS,GAAG,0BAA0B,GAAG,EAAE,IAC5DtF,QAAQ,IAAIP,IAAI,CAAC+C,QAAQ,KAAK,IAAI,GAAG,MAAM,GAAG,EAAE,cACpCxC,QAAQ,IAAIP,IAAI,CAAC+C,QAAQ,KAAK,IAAI,GAAG,QAAQ,GAAG,aAAa,IACzE/C,IAAI,CAAC+C,QAAQ,KAAK,IAAI,GAAG,YAAY,GAAG,WAAW,EAClD;oBAAA6B,QAAA,EAEF6B,IAAI,CAACb;kBAAK;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA,GAhBF0B,KAAK;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiBP,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,eAEDtF,OAAA;gBAAK+E,SAAS,EAAC,sDAAsD;gBAAAG,QAAA,gBACnElF,OAAA;kBAAK+E,SAAS,EAAC,+DAA+D;kBAAAG,QAAA,EAC3EvE,QAAQ,iBACPX,OAAA,CAACR,SAAS;oBACR0G,KAAK,EACH1F,kBAAkB,GACdyG,IAAI,CAACC,SAAS,CAAC;sBACbC,EAAE,EAAE3G,kBAAkB,CAACqE,aAAa;sBACpCvD,IAAI,EAAEd,kBAAkB,CAAC8F,QAAQ;sBACjCtE,gBAAgB,EAAExB,kBAAkB,CAACwB,gBAAgB;sBACrDoE,IAAI,EAAE5F,kBAAkB,CAAC4F,IAAI;sBAC7BgB,IAAI,EAAE5G,kBAAkB,CAAC6F;oBAC3B,CAAC,CAAC,GACF,EACL;oBACDgB,IAAI,EAAExG,QAAQ,GAAG,GAAG,GAAG,GAAI;oBAC3ByG,KAAK,EAAC,GAAG;oBACTC,aAAa,EAAE;kBAAK;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNtF,OAAA;kBAAG+E,SAAS,EAAC,mDAAmD;kBAAAG,QAAA,EAAE7E,CAAC,CAAC,wBAAwB;gBAAC;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/F,CAAC,eAENtF,OAAA;gBACE+E,SAAS,EAAE,4FACTzE,IAAI,CAAC+C,QAAQ,KAAK,IAAI,GAAG,YAAY,GAAG,WAAW,EAClD;gBAAA6B,QAAA,gBAEHlF,OAAA;kBAAK+E,SAAS,EAAC,cAAc;kBAAAG,QAAA,gBAC3BlF,OAAA;oBAAI+E,SAAS,EAAC,yCAAyC;oBAAAG,QAAA,EAAE7E,CAAC,CAAC,qCAAqC;kBAAC;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,EACtGrE,SAAS,gBACRjB,OAAA,CAACH,MAAM;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,GACRvE,cAAc,gBAChBf,OAAA,CAAAE,SAAA;oBAAAgF,QAAA,gBACElF,OAAA;sBAAG+E,SAAS,EAAC,qBAAqB;sBAAAG,QAAA,GAC/B7E,CAAC,CAAC,mBAAmB,CAAC,EAAC,IAAE,EAACU,cAAc,CAACS,KAAK;oBAAA;sBAAA2D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC,eACJtF,OAAA;sBAAG+E,SAAS,EAAC,gBAAgB;sBAAAG,QAAA,GAC1B7E,CAAC,CAAC,mBAAmB,CAAC,EAAC,IAAE,EAACU,cAAc,CAACU,KAAK;oBAAA;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC;kBAAA,eACJ,CAAC,gBAEHtF,OAAA;oBAAG+E,SAAS,EAAC,gCAAgC;oBAAAG,QAAA,EAAE7E,CAAC,CAAC,oCAAoC;kBAAC;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAC3F;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNtF,OAAA;kBAAK+E,SAAS,EAAC,cAAc;kBAAAG,QAAA,gBAC3BlF,OAAA;oBAAI+E,SAAS,EAAC,yCAAyC;oBAAAG,QAAA,EAAE7E,CAAC,CAAC,8BAA8B;kBAAC;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChGtF,OAAA;oBAAG+E,SAAS,EAAC,qBAAqB;oBAAAG,QAAA,GAC/B7E,CAAC,CAAC,mBAAmB,CAAC,EAAC,IAAE,EAACgB,aAAa,CAACG,KAAK;kBAAA;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACJtF,OAAA;oBAAG+E,SAAS,EAAC,gBAAgB;oBAAAG,QAAA,GAC1B7E,CAAC,CAAC,mBAAmB,CAAC,EAAC,IAAE,EAACgB,aAAa,CAACI,KAAK;kBAAA;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENtF,OAAA;cAAK+E,SAAS,EAAC,sEAAsE;cAAAG,QAAA,gBACnFlF,OAAA;gBACEwH,OAAO,EAAE/D,WAAY;gBACrBsB,SAAS,EAAC,wPAAwP;gBAAAG,QAAA,gBAElQlF,OAAA;kBACE+E,SAAS,EAAC,oCAAoC;kBAC9CQ,IAAI,EAAC,MAAM;kBACXC,MAAM,EAAC,cAAc;kBACrBC,OAAO,EAAC,WAAW;kBAAAP,QAAA,eAEnBlF,OAAA;oBACE0F,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC,OAAO;oBACtBC,WAAW,EAAC,GAAG;oBACfC,CAAC,EAAC;kBAAgE;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EACLjF,CAAC,CAAC,kCAAkC,CAAC;cAAA;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACTtF,OAAA;gBACEyH,IAAI,EAAC,eAAe;gBACpB1C,SAAS,EAAC,2RAA2R;gBAAAG,QAAA,EAEpS7E,CAAC,CAAC,gCAAgC;cAAC;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACPtF,OAAA,CAACJ,MAAM;MAAAuF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAClF,EAAA,CA5TID,YAAY;EAAA,QACIb,cAAc,EACjBC,WAAW;AAAA;AAAAmI,EAAA,GAFxBvH,YAAY;AA8TlB,eAAeA,YAAY;AAAC,IAAAuH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}