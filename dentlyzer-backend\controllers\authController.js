const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const Joi = require('joi');
const config = require('../config/config');
const Student = require('../models/Student');
const Supervisor = require('../models/Supervisor');
const Admin = require('../models/Admin');
const Superadmin = require('../models/Superadmin');
const Assistant = require('../models/Assistant');
const Dentist = require('../models/Dentist');
const ActivityLog = require('../models/ActivityLog');


const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required(),
});

const login = async (req, res) => {
  const { error } = loginSchema.validate(req.body);
  if (error) {
    return res.status(400).json({ message: error.details[0].message });
  }

  const { email, password } = req.body;

  try {
    const userTypes = [
      { model: Student, role: 'student' },
      { model: Supervisor, role: 'supervisor' },
      { model: Admin, role: 'admin' },
      { model: Superadmin, role: 'superadmin' },
      { model: Assistant, role: 'assistant' },
    ];

    let foundUser = null;
    let role = '';

    // Check all standard models first
    for (const { model, role: roleName } of userTypes) {
      const user = await model.findOne({ email });
      console.log(`Checking ${roleName} for email ${email}: ${user ? 'Found' : 'Not found'}`);
      if (user) {
        const isMatch = await bcrypt.compare(password, user.password);
        console.log(`Password match for ${roleName}: ${isMatch}`);
        if (isMatch) {
          foundUser = user;
          role = roleName;
          break;
        }
      }
    }

    // Check dentist model separately since email is in contactInfo.email
    if (!foundUser) {
      const dentist = await Dentist.findOne({ 'contactInfo.email': email });
      console.log(`Checking dentist for email ${email}: ${dentist ? 'Found' : 'Not found'}`);
      if (dentist) {
        const isMatch = await bcrypt.compare(password, dentist.password);
        console.log(`Password match for dentist: ${isMatch}`);
        if (isMatch) {
          foundUser = dentist;
          role = 'dentist';
        }
      }
    }

    if (!foundUser) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    console.log('Signing JWT for:', { id: foundUser._id, role }); // Debug log
    const token = jwt.sign(
      { id: foundUser._id, role },
      config.JWT_SECRET,
      { expiresIn: config.JWT_ACCESS_EXPIRATION }
    );

    // Customize the user object based on role
    let userResponse;

    if (role === 'dentist') {
      userResponse = {
        id: foundUser._id,
        email: foundUser.contactInfo.email,
        name: foundUser.name.en,
        role,
        dentistId: foundUser.dentistId
      };
    } else {
      userResponse = {
        id: foundUser._id,
        email: foundUser.email,
        name: foundUser.name,
        role,
        university: foundUser.university || '',
      };

      // Add role-specific fields
      if (role === 'student') {
        userResponse.studentId = foundUser.studentId || foundUser._id;
      } else if (role === 'assistant') {
        userResponse.dentistId = foundUser.dentistId;
        // Include affiliation information if available
        if (foundUser.affiliation) {
          userResponse.affiliation = foundUser.affiliation;
        }
      }
    }

    // Log successful login
    try {
      await ActivityLog.create({
        userId: foundUser._id,
        userName: role === 'dentist' ? foundUser.name.en : foundUser.name,
        userRole: role,
        action: 'User logged in',
        details: `Email: ${email}`,
        ipAddress: req.ip || req.connection.remoteAddress || 'Unknown',
        timestamp: new Date()
      });
    } catch (logError) {
      console.error('Error logging login activity:', logError);
      // Don't fail login if logging fails
    }

    res.json({
      token,
      role,
      user: userResponse,
    });
  } catch (err) {
    console.error('Login error:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

const getCurrentUser = async (req, res) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'User not authenticated' });
    }

    const userData = {
      id: req.user.id,
      role: req.user.role,
      name: req.user.name,
      email: req.user.email,
      university: req.user.university || '',
    };
    if (req.user.role === 'student') {
      userData.studentId = req.user.studentId || req.user.id;
    }

    res.json(userData);
  } catch (error) {
    console.error('getCurrentUser error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get user profile
const getProfile = async (req, res) => {
  try {
    const { id, role } = req.user;
    let user;

    // Fetch user based on role
    if (role === 'student') {
      user = await Student.findById(id).select('-password');
    } else if (role === 'supervisor') {
      user = await Supervisor.findById(id).select('-password');
    } else if (role === 'admin') {
      user = await Admin.findById(id).select('-password');
    } else if (role === 'superadmin') {
      user = await Superadmin.findById(id).select('-password');
    } else if (role === 'assistant') {
      user = await Assistant.findById(id).select('-password');
    }

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Format response
    const userData = {
      id: user._id.toString(),
      name: user.name,
      email: user.email,
      role: role,
      university: user.university || '',
      studentId: role === 'student' ? user.studentId || user._id.toString() : undefined,
    };

    res.json(userData);
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ message: error.message });
  }
};


module.exports = { login, getCurrentUser, getProfile };