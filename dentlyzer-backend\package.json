{"name": "dentlyzer-backend", "version": "1.0.0", "description": "Backend API for ODenta dental management system", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'No build step required for Node.js backend'", "test": "echo 'No tests specified'", "seed": "node seed.js", "seed:universities": "node seedUniversity.js", "seed:supervisors": "node seedSupervisor.js"}, "keywords": ["dental", "management", "api", "nodejs", "express", "mongodb"], "author": "ODenta Team", "license": "ISC", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "multer": "^1.4.5-lts.2", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.9"}}