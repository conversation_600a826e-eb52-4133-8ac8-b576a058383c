{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\admin\\\\Reviews.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\nimport Navbar from '../student/Navbar';\nimport AdminSidebar from './AdminSidebar';\nimport Loader from '../components/Loader';\nimport { motion } from 'framer-motion';\nimport { FaStar, FaSearch, FaUserGraduate, FaUserMd, FaCalendarAlt, FaClipboardList } from 'react-icons/fa';\n\n// Website color palette\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst websiteColorPalette = {\n  primary: '#0077B6',\n  secondary: '#20B2AA',\n  background: '#FFFFFF',\n  text: '#333333',\n  accent: '#28A745'\n};\nconst Reviews = () => {\n  _s();\n  var _selectedReview$patie, _selectedReview$patie2;\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [reviews, setReviews] = useState([]);\n  const [filteredReviews, setFilteredReviews] = useState([]);\n  const [selectedReview, setSelectedReview] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [procedureFilter, setProcedureFilter] = useState('all');\n  const [sortBy, setSortBy] = useState('date');\n  const [sortOrder, setSortOrder] = useState('desc');\n  const [students, setStudents] = useState([]);\n  const [supervisors, setSupervisors] = useState([]);\n  const [procedureTypes, setProcedureTypes] = useState([]);\n  const navigate = useNavigate();\n  const {\n    user,\n    token\n  } = useAuth();\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!user || !token) {\n        setError('Please log in to view reviews.');\n        setLoading(false);\n        return;\n      }\n      if (!user.university) {\n        setError('User profile incomplete. Missing university information.');\n        setLoading(false);\n        return;\n      }\n      try {\n        const config = {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        };\n\n        // First, fetch students and supervisors from the university\n        const [studentsRes, supervisorsRes] = await Promise.all([axios.get(`http://localhost:5000/api/admin/students?university=${encodeURIComponent(user.university)}`, config), axios.get(`http://localhost:5000/api/admin/supervisors?university=${encodeURIComponent(user.university)}`, config)]);\n\n        // Get students and supervisors from this university\n        const students = studentsRes.data || [];\n        const supervisors = supervisorsRes.data || [];\n        console.log(`University has ${students.length} students and ${supervisors.length} supervisors`);\n\n        // Log student IDs for debugging\n        if (students.length > 0) {\n          console.log('Sample student data:', {\n            name: students[0].name,\n            _id: students[0]._id,\n            studentId: students[0].studentId\n          });\n        }\n        setStudents(students);\n        setSupervisors(supervisors);\n\n        // If no students found, no need to fetch reviews\n        if (students.length === 0) {\n          setError('No students found in your university.');\n          setLoading(false);\n          return;\n        }\n\n        // Now fetch reviews for each student in the university\n        let allReviews = [];\n\n        // Create an array of promises for fetching reviews for each student\n        console.log('Fetching reviews for students...');\n        const reviewPromises = students.map(student => {\n          const url = `${process.env.REACT_APP_API_URL}/api/reviews/student?studentId=${student.studentId}`;\n          console.log(`Fetching reviews for student ${student.name} with URL: ${url}`);\n          return axios.get(url, config).then(response => {\n            console.log(`Successfully fetched ${response.data.length} reviews for student ${student.name}`);\n            return response;\n          }).catch(error => {\n            console.error(`Error fetching reviews for student ${student.name} (ID: ${student.studentId}):`, error.message);\n            // Try with _id as fallback\n            console.log(`Trying fallback with _id for student ${student.name}`);\n            return axios.get(`http://localhost:5000/api/reviews/student?studentId=${student._id}`, config).then(response => {\n              console.log(`Fallback successful! Fetched ${response.data.length} reviews for student ${student.name}`);\n              return response;\n            }).catch(fallbackError => {\n              console.error(`Fallback also failed for student ${student.name}:`, fallbackError.message);\n              return {\n                data: []\n              }; // Return empty array on error\n            });\n          });\n        });\n\n        // Execute all promises in parallel\n        const reviewsResults = await Promise.all(reviewPromises);\n\n        // Combine all reviews into a single array\n        reviewsResults.forEach(result => {\n          if (result.data && Array.isArray(result.data)) {\n            allReviews = [...allReviews, ...result.data];\n          }\n        });\n        console.log(`Found ${allReviews.length} total reviews for students in university ${user.university}`);\n\n        // Remove any duplicate reviews (in case a student appears in multiple queries)\n        const uniqueReviews = Array.from(new Map(allReviews.map(review => [review._id, review])).values());\n\n        // Filter out signature storage reviews\n        const filteredReviews = uniqueReviews.filter(review => !(review.patientId && review.patientId.nationalId === 'signature-storage'));\n        setReviews(filteredReviews);\n        setFilteredReviews(filteredReviews);\n\n        // Extract unique procedure types\n        const uniqueProcedureTypes = [...new Set(filteredReviews.map(review => review.procedureType).filter(Boolean))];\n        setProcedureTypes(uniqueProcedureTypes);\n        if (filteredReviews.length === 0) {\n          setError('No reviews found for students in your university.');\n        }\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response4$data, _err$response5;\n        console.error('Fetch error:', ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n        const errorMessage = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.status) === 404 ? 'Reviews endpoint not found.' : ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401 ? 'Unauthorized. Please log in again.' : ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.message) || 'Failed to load reviews';\n        setError(errorMessage);\n        if (((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : _err$response5.status) === 401) navigate('/login');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [user, token, navigate]);\n\n  // Filter and sort reviews when filters change\n  useEffect(() => {\n    if (!reviews.length) return;\n    let result = [...reviews];\n\n    // Apply search filter\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      result = result.filter(review => {\n        var _review$studentName, _review$supervisorNam, _review$patientId, _review$patientId$ful, _review$procedureType;\n        return ((_review$studentName = review.studentName) === null || _review$studentName === void 0 ? void 0 : _review$studentName.toLowerCase().includes(searchLower)) || ((_review$supervisorNam = review.supervisorName) === null || _review$supervisorNam === void 0 ? void 0 : _review$supervisorNam.toLowerCase().includes(searchLower)) || ((_review$patientId = review.patientId) === null || _review$patientId === void 0 ? void 0 : (_review$patientId$ful = _review$patientId.fullName) === null || _review$patientId$ful === void 0 ? void 0 : _review$patientId$ful.toLowerCase().includes(searchLower)) || ((_review$procedureType = review.procedureType) === null || _review$procedureType === void 0 ? void 0 : _review$procedureType.toLowerCase().includes(searchLower));\n      });\n    }\n\n    // Apply status filter\n    if (statusFilter !== 'all') {\n      if (statusFilter === 'declined') {\n        // Handle both 'declined' and 'denied' statuses\n        result = result.filter(review => review.status === 'declined' || review.status === 'denied');\n      } else {\n        result = result.filter(review => review.status === statusFilter);\n      }\n    }\n\n    // Apply procedure filter\n    if (procedureFilter !== 'all') {\n      result = result.filter(review => review.procedureType === procedureFilter);\n    }\n\n    // Apply sorting\n    result.sort((a, b) => {\n      let comparison = 0;\n      if (sortBy === 'date') {\n        comparison = new Date(a.submittedDate) - new Date(b.submittedDate);\n      } else if (sortBy === 'student') {\n        comparison = a.studentName.localeCompare(b.studentName);\n      } else if (sortBy === 'procedure') {\n        comparison = a.procedureType.localeCompare(b.procedureType);\n      } else if (sortBy === 'status') {\n        comparison = a.status.localeCompare(b.status);\n      }\n      return sortOrder === 'asc' ? comparison : -comparison;\n    });\n    setFilteredReviews(result);\n  }, [reviews, searchTerm, statusFilter, procedureFilter, sortBy, sortOrder]);\n\n  // Helper function to get student details by ID\n  const getStudentDetails = studentId => {\n    if (!studentId || !students.length) return null;\n    try {\n      // Handle case where studentId is an object\n      let idToFind;\n      if (typeof studentId === 'object') {\n        // Safely access properties with null checks\n        idToFind = studentId ? studentId.studentId || (studentId._id ? studentId._id : null) : null;\n      } else {\n        idToFind = studentId;\n      }\n      if (!idToFind) return null;\n      const student = students.find(student => student && (student.studentId === idToFind || student._id === idToFind));\n      if (!student) return null;\n\n      // Return a plain object with just the properties we need\n      return {\n        name: student.name || 'N/A',\n        studentId: student.studentId || 'N/A',\n        university: student.university || 'N/A',\n        email: student.email || 'N/A'\n      };\n    } catch (error) {\n      console.error('Error getting student details:', error);\n      return null;\n    }\n  };\n\n  // Helper function to get supervisor details by ID\n  const getSupervisorDetails = supervisorId => {\n    if (!supervisorId || !supervisors.length) return null;\n    try {\n      // Handle case where supervisorId is an object\n      let idToFind;\n      if (typeof supervisorId === 'object') {\n        // Safely access properties with null checks\n        idToFind = supervisorId && supervisorId._id ? supervisorId._id : null;\n      } else {\n        idToFind = supervisorId;\n      }\n      if (!idToFind) return null;\n      const supervisor = supervisors.find(supervisor => supervisor && supervisor._id === idToFind);\n      if (!supervisor) return null;\n\n      // Return a plain object with just the properties we need\n      return {\n        name: supervisor.name || 'N/A',\n        university: supervisor.university || 'N/A',\n        email: supervisor.email || 'N/A'\n      };\n    } catch (error) {\n      console.error('Error getting supervisor details:', error);\n      return null;\n    }\n  };\n  const container = {\n    hidden: {\n      opacity: 0\n    },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const item = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    show: {\n      opacity: 1,\n      y: 0\n    }\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 295,\n    columnNumber: 23\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-6 bg-gradient-to-br from-blue-50 to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: [error && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: -20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5 text-red-500 mr-3\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-700 font-medium\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: `text-3xl md:text-4xl font-bold text-[${websiteColorPalette.primary}] mb-1`,\n                children: \"Reviews\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: `text-[${websiteColorPalette.text}]`,\n                children: \"Manage student performance reviews\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: container,\n              initial: \"hidden\",\n              whileInView: \"show\",\n              viewport: {\n                once: true\n              },\n              className: \"bg-white rounded-xl shadow-sm mb-6 p-6 border border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                      children: /*#__PURE__*/_jsxDEV(FaSearch, {\n                        className: \"h-4 w-4 text-gray-400\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 335,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 334,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      className: `block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-[${websiteColorPalette.primary}] focus:border-[${websiteColorPalette.primary}] sm:text-sm`,\n                      placeholder: \"Search by student, supervisor, patient, or procedure...\",\n                      value: searchTerm,\n                      onChange: e => setSearchTerm(e.target.value)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 337,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-wrap gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                    className: `block w-full md:w-auto px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-[${websiteColorPalette.primary}] focus:border-[${websiteColorPalette.primary}] sm:text-sm`,\n                    value: statusFilter,\n                    onChange: e => setStatusFilter(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"all\",\n                      children: \"All Statuses\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 352,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"pending\",\n                      children: \"Pending\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 353,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"accepted\",\n                      children: \"Accepted\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 354,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"declined\",\n                      children: \"Declined\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 355,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    className: `block w-full md:w-auto px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-[${websiteColorPalette.primary}] focus:border-[${websiteColorPalette.primary}] sm:text-sm`,\n                    value: procedureFilter,\n                    onChange: e => setProcedureFilter(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"all\",\n                      children: \"All Procedures\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 362,\n                      columnNumber: 23\n                    }, this), procedureTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: type,\n                      children: type\n                    }, type, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 364,\n                      columnNumber: 25\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col md:flex-row md:items-center md:justify-between gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: filteredReviews.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 21\n                  }, this), \" reviews found\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Sort by:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    className: `block w-full md:w-auto px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-[${websiteColorPalette.primary}] focus:border-[${websiteColorPalette.primary}] sm:text-sm`,\n                    value: sortBy,\n                    onChange: e => setSortBy(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"date\",\n                      children: \"Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 382,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"student\",\n                      children: \"Student\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 383,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"procedure\",\n                      children: \"Procedure\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 384,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"status\",\n                      children: \"Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: `p-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-[${websiteColorPalette.primary}] focus:border-[${websiteColorPalette.primary}]`,\n                    onClick: () => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc'),\n                    children: sortOrder === 'asc' ? '↑' : '↓'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: container,\n              initial: \"hidden\",\n              whileInView: \"show\",\n              viewport: {\n                once: true\n              },\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                variants: item,\n                className: \"bg-white rounded-xl shadow-sm p-4 border border-gray-100\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-3 rounded-full bg-yellow-100 text-[${websiteColorPalette.primary}]`,\n                    children: /*#__PURE__*/_jsxDEV(FaClipboardList, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 408,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500 font-medium\",\n                      children: \"Pending Reviews\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 411,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-gray-900\",\n                      children: reviews.filter(r => r.status === 'pending').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 412,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                variants: item,\n                className: \"bg-white rounded-xl shadow-sm p-4 border border-gray-100\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-3 rounded-full bg-green-100 text-[${websiteColorPalette.accent}]`,\n                    children: /*#__PURE__*/_jsxDEV(FaStar, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 421,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500 font-medium\",\n                      children: \"Accepted Reviews\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 424,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-gray-900\",\n                      children: reviews.filter(r => r.status === 'accepted').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 425,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 423,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                variants: item,\n                className: \"bg-white rounded-xl shadow-sm p-4 border border-gray-100\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-3 rounded-full bg-red-100 text-red-500`,\n                    children: /*#__PURE__*/_jsxDEV(FaClipboardList, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 434,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500 font-medium\",\n                      children: \"Declined Reviews\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 437,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-gray-900\",\n                      children: reviews.filter(r => r.status === 'declined' || r.status === 'denied').length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 438,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 436,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: container,\n              initial: \"hidden\",\n              whileInView: \"show\",\n              viewport: {\n                once: true\n              },\n              className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: `text-xl font-bold text-[${websiteColorPalette.primary}] mb-6 flex items-center`,\n                  children: [/*#__PURE__*/_jsxDEV(FaStar, {\n                    className: `h-5 w-5 mr-2 text-[${websiteColorPalette.primary}]`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 21\n                  }, this), \"Review List\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"overflow-x-auto\",\n                  children: /*#__PURE__*/_jsxDEV(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                      className: \"bg-gray-50\",\n                      children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Student\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 463,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Patient\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 464,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Procedure\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 465,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Status\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 466,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Supervisor\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 467,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                          children: \"Submitted\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 468,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 462,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 461,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                      className: \"bg-white divide-y divide-gray-200\",\n                      children: filteredReviews.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: /*#__PURE__*/_jsxDEV(\"td\", {\n                          colSpan: \"6\",\n                          className: \"px-6 py-8 text-center\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex flex-col items-center justify-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                              xmlns: \"http://www.w3.org/2000/svg\",\n                              className: \"h-12 w-12 text-gray-400 mb-4\",\n                              fill: \"none\",\n                              viewBox: \"0 0 24 24\",\n                              stroke: \"currentColor\",\n                              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 483,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 476,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                              className: \"text-lg font-medium text-gray-900\",\n                              children: \"No reviews found\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 490,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"mt-1 text-gray-500\",\n                              children: reviews.length > 0 ? \"No reviews match your current filters.\" : \"No reviews submitted for your university.\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 491,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 475,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 474,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 473,\n                        columnNumber: 27\n                      }, this) : filteredReviews.map(review => {\n                        var _review$patientId2;\n                        return /*#__PURE__*/_jsxDEV(motion.tr, {\n                          variants: item,\n                          className: \"hover:bg-gray-50 cursor-pointer\",\n                          onClick: () => {\n                            // Create a safe copy of the review to prevent rendering objects directly\n                            const safeReview = {\n                              ...review,\n                              // Ensure studentId is a string if it's an object\n                              studentId: review.studentId ? typeof review.studentId === 'object' ? review.studentId.studentId || review.studentId._id || 'Unknown ID' : review.studentId : 'Unknown ID',\n                              // Ensure supervisorId is a string if it's an object\n                              supervisorId: review.supervisorId ? typeof review.supervisorId === 'object' ? review.supervisorId._id || 'Unknown ID' : review.supervisorId : null,\n                              // Ensure patientId is properly formatted\n                              patientId: review.patientId ? typeof review.patientId === 'object' ? {\n                                ...review.patientId,\n                                // Ensure fullName is a string\n                                fullName: review.patientId.fullName || 'Unknown Patient',\n                                // Ensure nationalId is a string\n                                nationalId: review.patientId.nationalId || 'Unknown ID'\n                              } : review.patientId : 'Unknown Patient',\n                              // Ensure steps are properly formatted\n                              steps: Array.isArray(review.steps) ? review.steps.map((step, index) => {\n                                if (!step) return {\n                                  name: `Step ${index + 1}`,\n                                  completed: false,\n                                  description: ''\n                                };\n                                return {\n                                  ...step,\n                                  name: step.name || step.description || `Step ${index + 1}`,\n                                  completed: !!step.completed,\n                                  description: step.description || ''\n                                };\n                              }) : [],\n                              // Ensure reviewSteps are properly formatted\n                              reviewSteps: Array.isArray(review.reviewSteps) ? review.reviewSteps.map((step, index) => {\n                                if (!step) return {\n                                  description: `Step ${index + 1}`,\n                                  completed: false\n                                };\n                                return {\n                                  ...step,\n                                  description: step.description || `Step ${index + 1}`,\n                                  completed: !!step.completed\n                                };\n                              }) : [],\n                              // Include supervisor signature if available\n                              supervisorSignature: review.supervisorSignature || null\n                            };\n                            setSelectedReview(safeReview);\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center\",\n                              children: [/*#__PURE__*/_jsxDEV(FaUserGraduate, {\n                                className: `h-4 w-4 text-[${websiteColorPalette.primary}] mr-2`\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 565,\n                                columnNumber: 35\n                              }, this), review.studentName]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 564,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 563,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: typeof review.patientId === 'object' && (_review$patientId2 = review.patientId) !== null && _review$patientId2 !== void 0 && _review$patientId2.fullName ? review.patientId.fullName : typeof review.patientId === 'string' ? review.patientId : \"Unknown Patient\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 569,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: review.procedureType\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 576,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap\",\n                            children: /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: `px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${review.status === 'accepted' ? `bg-green-100 text-[${websiteColorPalette.accent}]` : review.status === 'denied' || review.status === 'declined' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}`,\n                              children: review.status === 'denied' ? 'declined' : review.status\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 578,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 577,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: review.supervisorName ? /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center\",\n                              children: [/*#__PURE__*/_jsxDEV(FaUserMd, {\n                                className: `h-4 w-4 text-[${websiteColorPalette.secondary}] mr-2`\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 593,\n                                columnNumber: 37\n                              }, this), review.supervisorName]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 592,\n                              columnNumber: 35\n                            }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"text-gray-400\",\n                              children: \"Not assigned\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 597,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 590,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center\",\n                              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                                className: `h-4 w-4 text-[${websiteColorPalette.primary}] mr-2`\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 602,\n                                columnNumber: 35\n                              }, this), new Date(review.submittedDate).toLocaleDateString()]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 601,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 600,\n                            columnNumber: 31\n                          }, this)]\n                        }, review._id, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 501,\n                          columnNumber: 29\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 471,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), selectedReview && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            exit: {\n              opacity: 0\n            },\n            transition: {\n              duration: 0.2\n            },\n            className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 backdrop-blur-sm\",\n            onClick: e => {\n              if (e.target === e.currentTarget) setSelectedReview(null);\n            },\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0.9,\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                scale: 1,\n                opacity: 1,\n                y: 0\n              },\n              exit: {\n                scale: 0.9,\n                opacity: 0,\n                y: 20\n              },\n              transition: {\n                type: \"spring\",\n                stiffness: 300,\n                damping: 30\n              },\n              className: \"bg-white rounded-xl shadow-lg p-0 max-w-4xl w-full mx-4 overflow-hidden border border-gray-200\",\n              onClick: e => e.stopPropagation(),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `bg-gradient-to-r from-[${websiteColorPalette.primary}] to-[${websiteColorPalette.secondary}] p-5 text-white`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(FaStar, {\n                      className: \"h-6 w-6 mr-3 text-yellow-300\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 637,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-xl font-bold\",\n                      children: \"Review Details\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 638,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 636,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-3 py-1 text-xs font-medium rounded-full ${selectedReview.status === 'accepted' ? 'bg-green-100 text-green-800' : selectedReview.status === 'denied' || selectedReview.status === 'declined' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}`,\n                      children: selectedReview.status === 'accepted' ? 'Accepted' : selectedReview.status === 'denied' ? 'Declined' : selectedReview.status === 'declined' ? 'Declined' : selectedReview.status || 'Pending'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 641,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setSelectedReview(null),\n                      className: `text-white hover:text-blue-200 transition-colors bg-[${websiteColorPalette.primary}] hover:bg-blue-600 rounded-full p-2 focus:outline-none focus:ring-2 focus:ring-blue-300 focus:ring-opacity-50`,\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        className: \"h-5 w-5\",\n                        viewBox: \"0 0 20 20\",\n                        fill: \"currentColor\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          fillRule: \"evenodd\",\n                          d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                          clipRule: \"evenodd\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 658,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 657,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 653,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 640,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 max-h-[80vh] overflow-y-auto\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: `text-lg font-semibold text-[${websiteColorPalette.primary}] mb-3 flex items-center`,\n                      children: [/*#__PURE__*/_jsxDEV(FaUserGraduate, {\n                        className: `mr-2 text-[${websiteColorPalette.primary}]`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 669,\n                        columnNumber: 27\n                      }, this), \" Student Information\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 668,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100 space-y-3`,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"w-24 text-sm font-medium text-gray-600\",\n                          children: \"Name:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 673,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-gray-900 font-medium\",\n                          children: selectedReview.studentName\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 674,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 672,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"w-24 text-sm font-medium text-gray-600\",\n                          children: \"ID:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 677,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-gray-900\",\n                          children: typeof selectedReview.studentId === 'object' ? selectedReview.studentId.studentId || selectedReview.studentId._id || 'N/A' : selectedReview.studentId || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 678,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 676,\n                        columnNumber: 27\n                      }, this), (() => {\n                        const studentDetails = getStudentDetails(selectedReview.studentId);\n                        return studentDetails ? /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"w-24 text-sm font-medium text-gray-600\",\n                            children: \"University:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 688,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-gray-900\",\n                            children: studentDetails.university || 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 689,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 687,\n                          columnNumber: 31\n                        }, this) : null;\n                      })()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 671,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 667,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: `text-lg font-semibold text-[${websiteColorPalette.primary}] mb-3 flex items-center`,\n                      children: [/*#__PURE__*/_jsxDEV(FaUserMd, {\n                        className: `mr-2 text-[${websiteColorPalette.primary}]`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 698,\n                        columnNumber: 27\n                      }, this), \" Supervisor Information\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 697,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100 space-y-3`,\n                      children: selectedReview.supervisorName ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"w-24 text-sm font-medium text-gray-600\",\n                            children: \"Name:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 704,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-gray-900 font-medium\",\n                            children: selectedReview.supervisorName\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 705,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 703,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"w-24 text-sm font-medium text-gray-600\",\n                            children: \"Reviewed:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 708,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-gray-900\",\n                            children: selectedReview.reviewedDate ? new Date(selectedReview.reviewedDate).toLocaleString() : 'Not yet reviewed'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 709,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 707,\n                          columnNumber: 31\n                        }, this), (() => {\n                          const supervisorDetails = getSupervisorDetails(selectedReview.supervisorId);\n                          return supervisorDetails ? /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"w-24 text-sm font-medium text-gray-600\",\n                              children: \"University:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 716,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"text-gray-900\",\n                              children: supervisorDetails.university || 'N/A'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 717,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 715,\n                            columnNumber: 35\n                          }, this) : null;\n                        })()]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-gray-500 italic\",\n                        children: \"No supervisor assigned yet\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 723,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 700,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 696,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 666,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: `text-lg font-semibold text-[${websiteColorPalette.primary}] mb-3 flex items-center`,\n                    children: [/*#__PURE__*/_jsxDEV(FaUserMd, {\n                      className: `mr-2 text-[${websiteColorPalette.primary}]`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 732,\n                      columnNumber: 25\n                    }, this), \" Patient Information\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 731,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          className: \"text-sm font-medium text-gray-500\",\n                          children: \"Name\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 737,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm text-gray-900 mt-1\",\n                          children: typeof selectedReview.patientId === 'object' && (_selectedReview$patie = selectedReview.patientId) !== null && _selectedReview$patie !== void 0 && _selectedReview$patie.fullName ? selectedReview.patientId.fullName : typeof selectedReview.patientId === 'string' ? selectedReview.patientId : 'Unknown'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 738,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 736,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          className: \"text-sm font-medium text-gray-500\",\n                          children: \"National ID\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 747,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm text-gray-900 mt-1\",\n                          children: typeof selectedReview.patientId === 'object' && (_selectedReview$patie2 = selectedReview.patientId) !== null && _selectedReview$patie2 !== void 0 && _selectedReview$patie2.nationalId ? selectedReview.patientId.nationalId : 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 748,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 746,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 735,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 734,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 730,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: `text-lg font-semibold text-[${websiteColorPalette.primary}] mb-3 flex items-center`,\n                    children: [/*#__PURE__*/_jsxDEV(FaClipboardList, {\n                      className: `mr-2 text-[${websiteColorPalette.primary}]`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 761,\n                      columnNumber: 25\n                    }, this), \" Procedure Details\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 760,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          className: \"text-sm font-medium text-gray-500\",\n                          children: \"Procedure Type\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 766,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm text-gray-900 mt-1 font-medium\",\n                          children: selectedReview.procedureType\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 767,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 765,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          className: \"text-sm font-medium text-gray-500\",\n                          children: \"Status\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 770,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm mt-1\",\n                          children: /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: `px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${selectedReview.status === 'accepted' ? 'bg-green-100 text-green-800' : selectedReview.status === 'denied' || selectedReview.status === 'declined' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}`,\n                            children: selectedReview.status === 'denied' ? 'Declined' : selectedReview.status === 'accepted' ? 'Accepted' : selectedReview.status === 'pending' ? 'Pending' : selectedReview.status\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 772,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 771,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 769,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 764,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 763,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 759,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: `text-lg font-semibold text-[${websiteColorPalette.primary}] mb-3 flex items-center`,\n                    children: [/*#__PURE__*/_jsxDEV(FaStar, {\n                      className: `mr-2 text-[${websiteColorPalette.primary}]`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 792,\n                      columnNumber: 25\n                    }, this), \" Ratings\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 791,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-white p-4 rounded-lg shadow-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"font-medium mb-3 text-gray-700\",\n                          children: \"Procedure Quality\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 797,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center\",\n                          children: [[1, 2, 3, 4, 5].map(star => /*#__PURE__*/_jsxDEV(FaStar, {\n                            className: `h-6 w-6 ${star <= selectedReview.procedureQuality ? 'text-yellow-400' : 'text-gray-200'}`\n                          }, star, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 800,\n                            columnNumber: 33\n                          }, this)), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"ml-3 text-gray-700 font-bold\",\n                            children: [selectedReview.procedureQuality, \"/5\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 809,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 798,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 796,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-white p-4 rounded-lg shadow-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"font-medium mb-3 text-gray-700\",\n                          children: \"Patient Interaction\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 813,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center\",\n                          children: [[1, 2, 3, 4, 5].map(star => /*#__PURE__*/_jsxDEV(FaStar, {\n                            className: `h-6 w-6 ${star <= selectedReview.patientInteraction ? 'text-yellow-400' : 'text-gray-200'}`\n                          }, star, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 816,\n                            columnNumber: 33\n                          }, this)), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"ml-3 text-gray-700 font-bold\",\n                            children: [selectedReview.patientInteraction, \"/5\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 825,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 814,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 812,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 795,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 790,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: `text-lg font-semibold text-[${websiteColorPalette.primary}] mb-3 flex items-center`,\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: `h-5 w-5 mr-2 text-[${websiteColorPalette.primary}]`,\n                      viewBox: \"0 0 20 20\",\n                      fill: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 836,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 835,\n                      columnNumber: 25\n                    }, this), \"Comments\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 834,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100 space-y-5`,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-4 rounded-lg shadow-sm\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"font-medium text-gray-700 mb-2 flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaUserGraduate, {\n                          className: `h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 843,\n                          columnNumber: 29\n                        }, this), \"Student Comment:\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 842,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"mt-1 text-gray-600 bg-gray-50 p-3 rounded-md border border-gray-100\",\n                        children: selectedReview.comment || 'No comment provided'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 846,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 841,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-4 rounded-lg shadow-sm\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"font-medium text-gray-700 mb-2 flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaUserMd, {\n                          className: `h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 852,\n                          columnNumber: 29\n                        }, this), \"Supervisor Note:\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 851,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"mt-1 text-gray-600 bg-gray-50 p-3 rounded-md border border-gray-100\",\n                        children: selectedReview.note || 'No note provided'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 855,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 850,\n                      columnNumber: 25\n                    }, this), selectedReview.supervisorSignature && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-4 rounded-lg shadow-sm\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"font-medium text-gray-700 mb-2 flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaUserMd, {\n                          className: `h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 864,\n                          columnNumber: 31\n                        }, this), \"Supervisor Signature:\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 863,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-1 bg-gray-50 p-3 rounded-md border border-gray-100 flex justify-center\",\n                        children: selectedReview.supervisorSignature.startsWith('data:image') ? /*#__PURE__*/_jsxDEV(\"img\", {\n                          src: selectedReview.supervisorSignature,\n                          alt: \"Supervisor Signature\",\n                          className: \"max-h-20\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 869,\n                          columnNumber: 33\n                        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"font-signature text-lg text-gray-700\",\n                          children: selectedReview.supervisorSignature\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 875,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 867,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 862,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 840,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 833,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: `text-lg font-semibold text-[${websiteColorPalette.primary}] mb-3 flex items-center`,\n                    children: [/*#__PURE__*/_jsxDEV(FaClipboardList, {\n                      className: `mr-2 text-[${websiteColorPalette.primary}]`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 888,\n                      columnNumber: 25\n                    }, this), \" Procedure Steps\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 887,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`,\n                    children: selectedReview.reviewSteps && Array.isArray(selectedReview.reviewSteps) && selectedReview.reviewSteps.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white rounded-lg overflow-hidden\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"divide-y divide-gray-200\",\n                        children: selectedReview.reviewSteps.map((step, index) => {\n                          // Ensure step is an object\n                          const safeStep = step || {\n                            completed: false\n                          };\n                          return /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `p-3 flex items-center justify-between ${safeStep.completed ? 'bg-green-50' : 'bg-white'}`,\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center\",\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                className: `inline-block w-6 text-center mr-2 text-[${websiteColorPalette.primary}] font-bold`,\n                                children: [index + 1, \".\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 905,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"text-sm font-medium text-gray-900\",\n                                children: safeStep.description || safeStep.name || `Step ${index + 1}`\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 906,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 904,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: safeStep.completed ? /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                                  className: \"w-4 h-4 mr-1 text-green-500\",\n                                  fill: \"currentColor\",\n                                  viewBox: \"0 0 20 20\",\n                                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                    clipRule: \"evenodd\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 914,\n                                    columnNumber: 45\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 913,\n                                  columnNumber: 43\n                                }, this), \"Completed\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 912,\n                                columnNumber: 41\n                              }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                                  className: \"w-4 h-4 mr-1 text-gray-500\",\n                                  fill: \"currentColor\",\n                                  viewBox: \"0 0 20 20\",\n                                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8 9a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z\",\n                                    clipRule: \"evenodd\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 921,\n                                    columnNumber: 45\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 920,\n                                  columnNumber: 43\n                                }, this), \"Not completed\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 919,\n                                columnNumber: 41\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 910,\n                              columnNumber: 37\n                            }, this)]\n                          }, index, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 898,\n                            columnNumber: 35\n                          }, this);\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 893,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 892,\n                      columnNumber: 27\n                    }, this) : selectedReview.steps && Array.isArray(selectedReview.steps) && selectedReview.steps.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white rounded-lg overflow-hidden\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"divide-y divide-gray-200\",\n                        children: selectedReview.steps.map((step, index) => {\n                          // Ensure step is an object\n                          const safeStep = step || {\n                            completed: false\n                          };\n                          return /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `p-3 flex items-center justify-between ${safeStep.completed ? 'bg-green-50' : 'bg-white'}`,\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center\",\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                className: `inline-block w-6 text-center mr-2 text-[${websiteColorPalette.primary}] font-bold`,\n                                children: [index + 1, \".\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 946,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"text-sm font-medium text-gray-900\",\n                                children: safeStep.description || safeStep.name || `Step ${index + 1}`\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 947,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 945,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: safeStep.completed ? /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                                  className: \"w-4 h-4 mr-1 text-green-500\",\n                                  fill: \"currentColor\",\n                                  viewBox: \"0 0 20 20\",\n                                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                    clipRule: \"evenodd\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 955,\n                                    columnNumber: 45\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 954,\n                                  columnNumber: 43\n                                }, this), \"Completed\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 953,\n                                columnNumber: 41\n                              }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                                  className: \"w-4 h-4 mr-1 text-gray-500\",\n                                  fill: \"currentColor\",\n                                  viewBox: \"0 0 20 20\",\n                                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8 9a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z\",\n                                    clipRule: \"evenodd\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 962,\n                                    columnNumber: 45\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 961,\n                                  columnNumber: 43\n                                }, this), \"Not completed\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 960,\n                                columnNumber: 41\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 951,\n                              columnNumber: 37\n                            }, this)]\n                          }, index, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 939,\n                            columnNumber: 35\n                          }, this);\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 934,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 933,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-500 italic text-center py-4 bg-white rounded-lg\",\n                      children: \"No procedure steps available\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 974,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 890,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 886,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 665,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-t border-gray-200 p-5 flex justify-between items-center bg-gray-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: \"Submitted:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 982,\n                    columnNumber: 23\n                  }, this), \" \", new Date(selectedReview.submittedDate).toLocaleString(), selectedReview.reviewedDate && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Reviewed:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 985,\n                      columnNumber: 27\n                    }, this), \" \", new Date(selectedReview.reviewedDate).toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 984,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 981,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.03\n                  },\n                  whileTap: {\n                    scale: 0.97\n                  },\n                  onClick: () => setSelectedReview(null),\n                  className: `px-5 py-2.5 bg-[${websiteColorPalette.primary}] text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm flex items-center font-medium`,\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-5 w-5 mr-2\",\n                    viewBox: \"0 0 20 20\",\n                    fill: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 996,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 995,\n                    columnNumber: 23\n                  }, this), \"Close\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 989,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 980,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 298,\n    columnNumber: 5\n  }, this);\n};\n_s(Reviews, \"JEZryYbsDZEmmWSegRledgdwLc0=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Reviews;\nexport default Reviews;\nvar _c;\n$RefreshReg$(_c, \"Reviews\");", "map": {"version": 3, "names": ["useState", "useEffect", "useNavigate", "axios", "useAuth", "<PERSON><PERSON><PERSON>", "AdminSidebar", "Loader", "motion", "FaStar", "FaSearch", "FaUserGraduate", "FaUserMd", "FaCalendarAlt", "FaClipboardList", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "websiteColorPalette", "primary", "secondary", "background", "text", "accent", "Reviews", "_s", "_selectedReview$patie", "_selectedReview$patie2", "sidebarOpen", "setSidebarOpen", "reviews", "setReviews", "filteredReviews", "setFilteredReviews", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedReview", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "procedureFilter", "setProcedureFilter", "sortBy", "setSortBy", "sortOrder", "setSortOrder", "students", "setStudents", "supervisors", "setSupervisors", "procedureTypes", "setProcedureTypes", "navigate", "user", "token", "fetchData", "university", "config", "headers", "Authorization", "studentsRes", "supervisorsRes", "Promise", "all", "get", "encodeURIComponent", "data", "console", "log", "length", "name", "_id", "studentId", "allReviews", "reviewPromises", "map", "student", "url", "process", "env", "REACT_APP_API_URL", "then", "response", "catch", "message", "fallback<PERSON><PERSON>r", "reviewsResults", "for<PERSON>ach", "result", "Array", "isArray", "uniqueReviews", "from", "Map", "review", "values", "filter", "patientId", "nationalId", "uniqueProcedureTypes", "Set", "procedureType", "Boolean", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response4$data", "_err$response5", "errorMessage", "status", "searchLower", "toLowerCase", "_review$studentName", "_review$supervisorNam", "_review$patientId", "_review$patientId$ful", "_review$procedureType", "studentName", "includes", "<PERSON><PERSON><PERSON>", "fullName", "sort", "a", "b", "comparison", "Date", "submittedDate", "localeCompare", "getStudentDetails", "idToFind", "find", "email", "getSupervisorDetails", "supervisorId", "supervisor", "container", "hidden", "opacity", "show", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "item", "y", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "isOpen", "setIsOpen", "toggleSidebar", "div", "initial", "animate", "fill", "viewBox", "fillRule", "d", "clipRule", "duration", "variants", "whileInView", "viewport", "once", "type", "placeholder", "value", "onChange", "e", "target", "onClick", "r", "colSpan", "xmlns", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "_review$patientId2", "tr", "safeReview", "steps", "step", "index", "completed", "description", "reviewSteps", "supervisorSignature", "toLocaleDateString", "exit", "currentTarget", "scale", "stiffness", "damping", "stopPropagation", "studentDetails", "reviewedDate", "toLocaleString", "supervisorDetails", "star", "procedureQuality", "patientInteraction", "comment", "note", "startsWith", "src", "alt", "safeStep", "button", "whileHover", "whileTap", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/admin/Reviews.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport axios from 'axios';\r\nimport { useAuth } from '../context/AuthContext';\r\nimport Navbar from '../student/Navbar';\r\nimport AdminSidebar from './AdminSidebar';\r\nimport Loader from '../components/Loader';\r\nimport { motion } from 'framer-motion';\r\nimport { FaStar, FaSearch, FaUserGraduate, FaUserMd, FaCalendarAlt, FaClipboardList } from 'react-icons/fa';\r\n\r\n// Website color palette\r\nconst websiteColorPalette = {\r\n  primary: '#0077B6',\r\n  secondary: '#20B2AA',\r\n  background: '#FFFFFF',\r\n  text: '#333333',\r\n  accent: '#28A745'\r\n};\r\n\r\nconst Reviews = () => {\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const [reviews, setReviews] = useState([]);\r\n  const [filteredReviews, setFilteredReviews] = useState([]);\r\n  const [selectedReview, setSelectedReview] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [statusFilter, setStatusFilter] = useState('all');\r\n  const [procedureFilter, setProcedureFilter] = useState('all');\r\n  const [sortBy, setSortBy] = useState('date');\r\n  const [sortOrder, setSortOrder] = useState('desc');\r\n  const [students, setStudents] = useState([]);\r\n  const [supervisors, setSupervisors] = useState([]);\r\n  const [procedureTypes, setProcedureTypes] = useState([]);\r\n  const navigate = useNavigate();\r\n  const { user, token } = useAuth();\r\n\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      if (!user || !token) {\r\n        setError('Please log in to view reviews.');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n\r\n      if (!user.university) {\r\n        setError('User profile incomplete. Missing university information.');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const config = { headers: { Authorization: `Bearer ${token}` } };\r\n\r\n        // First, fetch students and supervisors from the university\r\n        const [studentsRes, supervisorsRes] = await Promise.all([\r\n          axios.get(`http://localhost:5000/api/admin/students?university=${encodeURIComponent(user.university)}`, config),\r\n          axios.get(`http://localhost:5000/api/admin/supervisors?university=${encodeURIComponent(user.university)}`, config)\r\n        ]);\r\n\r\n        // Get students and supervisors from this university\r\n        const students = studentsRes.data || [];\r\n        const supervisors = supervisorsRes.data || [];\r\n\r\n        console.log(`University has ${students.length} students and ${supervisors.length} supervisors`);\r\n\r\n        // Log student IDs for debugging\r\n        if (students.length > 0) {\r\n          console.log('Sample student data:', {\r\n            name: students[0].name,\r\n            _id: students[0]._id,\r\n            studentId: students[0].studentId\r\n          });\r\n        }\r\n\r\n        setStudents(students);\r\n        setSupervisors(supervisors);\r\n\r\n        // If no students found, no need to fetch reviews\r\n        if (students.length === 0) {\r\n          setError('No students found in your university.');\r\n          setLoading(false);\r\n          return;\r\n        }\r\n\r\n        // Now fetch reviews for each student in the university\r\n        let allReviews = [];\r\n\r\n        // Create an array of promises for fetching reviews for each student\r\n        console.log('Fetching reviews for students...');\r\n\r\n        const reviewPromises = students.map(student => {\r\n          const url = `${process.env.REACT_APP_API_URL}/api/reviews/student?studentId=${student.studentId}`;\r\n          console.log(`Fetching reviews for student ${student.name} with URL: ${url}`);\r\n\r\n          return axios.get(url, config)\r\n            .then(response => {\r\n              console.log(`Successfully fetched ${response.data.length} reviews for student ${student.name}`);\r\n              return response;\r\n            })\r\n            .catch(error => {\r\n              console.error(`Error fetching reviews for student ${student.name} (ID: ${student.studentId}):`, error.message);\r\n              // Try with _id as fallback\r\n              console.log(`Trying fallback with _id for student ${student.name}`);\r\n              return axios.get(`http://localhost:5000/api/reviews/student?studentId=${student._id}`, config)\r\n                .then(response => {\r\n                  console.log(`Fallback successful! Fetched ${response.data.length} reviews for student ${student.name}`);\r\n                  return response;\r\n                })\r\n                .catch(fallbackError => {\r\n                  console.error(`Fallback also failed for student ${student.name}:`, fallbackError.message);\r\n                  return { data: [] }; // Return empty array on error\r\n                });\r\n            });\r\n        });\r\n\r\n        // Execute all promises in parallel\r\n        const reviewsResults = await Promise.all(reviewPromises);\r\n\r\n        // Combine all reviews into a single array\r\n        reviewsResults.forEach(result => {\r\n          if (result.data && Array.isArray(result.data)) {\r\n            allReviews = [...allReviews, ...result.data];\r\n          }\r\n        });\r\n\r\n        console.log(`Found ${allReviews.length} total reviews for students in university ${user.university}`);\r\n\r\n        // Remove any duplicate reviews (in case a student appears in multiple queries)\r\n        const uniqueReviews = Array.from(new Map(allReviews.map(review => [review._id, review])).values());\r\n\r\n        // Filter out signature storage reviews\r\n        const filteredReviews = uniqueReviews.filter(review =>\r\n          !(review.patientId && review.patientId.nationalId === 'signature-storage')\r\n        );\r\n\r\n        setReviews(filteredReviews);\r\n        setFilteredReviews(filteredReviews);\r\n\r\n        // Extract unique procedure types\r\n        const uniqueProcedureTypes = [...new Set(filteredReviews.map(review => review.procedureType).filter(Boolean))];\r\n        setProcedureTypes(uniqueProcedureTypes);\r\n\r\n        if (filteredReviews.length === 0) {\r\n          setError('No reviews found for students in your university.');\r\n        }\r\n      } catch (err) {\r\n        console.error('Fetch error:', err.response?.data || err.message);\r\n        const errorMessage =\r\n          err.response?.status === 404\r\n            ? 'Reviews endpoint not found.'\r\n            : err.response?.status === 401\r\n            ? 'Unauthorized. Please log in again.'\r\n            : err.response?.data?.message || 'Failed to load reviews';\r\n        setError(errorMessage);\r\n        if (err.response?.status === 401) navigate('/login');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    fetchData();\r\n  }, [user, token, navigate]);\r\n\r\n  // Filter and sort reviews when filters change\r\n  useEffect(() => {\r\n    if (!reviews.length) return;\r\n\r\n    let result = [...reviews];\r\n\r\n    // Apply search filter\r\n    if (searchTerm) {\r\n      const searchLower = searchTerm.toLowerCase();\r\n      result = result.filter(\r\n        review =>\r\n          review.studentName?.toLowerCase().includes(searchLower) ||\r\n          review.supervisorName?.toLowerCase().includes(searchLower) ||\r\n          review.patientId?.fullName?.toLowerCase().includes(searchLower) ||\r\n          review.procedureType?.toLowerCase().includes(searchLower)\r\n      );\r\n    }\r\n\r\n    // Apply status filter\r\n    if (statusFilter !== 'all') {\r\n      if (statusFilter === 'declined') {\r\n        // Handle both 'declined' and 'denied' statuses\r\n        result = result.filter(review => review.status === 'declined' || review.status === 'denied');\r\n      } else {\r\n        result = result.filter(review => review.status === statusFilter);\r\n      }\r\n    }\r\n\r\n    // Apply procedure filter\r\n    if (procedureFilter !== 'all') {\r\n      result = result.filter(review => review.procedureType === procedureFilter);\r\n    }\r\n\r\n    // Apply sorting\r\n    result.sort((a, b) => {\r\n      let comparison = 0;\r\n\r\n      if (sortBy === 'date') {\r\n        comparison = new Date(a.submittedDate) - new Date(b.submittedDate);\r\n      } else if (sortBy === 'student') {\r\n        comparison = a.studentName.localeCompare(b.studentName);\r\n      } else if (sortBy === 'procedure') {\r\n        comparison = a.procedureType.localeCompare(b.procedureType);\r\n      } else if (sortBy === 'status') {\r\n        comparison = a.status.localeCompare(b.status);\r\n      }\r\n\r\n      return sortOrder === 'asc' ? comparison : -comparison;\r\n    });\r\n\r\n    setFilteredReviews(result);\r\n  }, [reviews, searchTerm, statusFilter, procedureFilter, sortBy, sortOrder]);\r\n\r\n  // Helper function to get student details by ID\r\n  const getStudentDetails = (studentId) => {\r\n    if (!studentId || !students.length) return null;\r\n\r\n    try {\r\n      // Handle case where studentId is an object\r\n      let idToFind;\r\n\r\n      if (typeof studentId === 'object') {\r\n        // Safely access properties with null checks\r\n        idToFind = studentId ? (studentId.studentId || (studentId._id ? studentId._id : null)) : null;\r\n      } else {\r\n        idToFind = studentId;\r\n      }\r\n\r\n      if (!idToFind) return null;\r\n\r\n      const student = students.find(student =>\r\n        student && (student.studentId === idToFind || student._id === idToFind)\r\n      );\r\n\r\n      if (!student) return null;\r\n\r\n      // Return a plain object with just the properties we need\r\n      return {\r\n        name: student.name || 'N/A',\r\n        studentId: student.studentId || 'N/A',\r\n        university: student.university || 'N/A',\r\n        email: student.email || 'N/A'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error getting student details:', error);\r\n      return null;\r\n    }\r\n  };\r\n\r\n  // Helper function to get supervisor details by ID\r\n  const getSupervisorDetails = (supervisorId) => {\r\n    if (!supervisorId || !supervisors.length) return null;\r\n\r\n    try {\r\n      // Handle case where supervisorId is an object\r\n      let idToFind;\r\n\r\n      if (typeof supervisorId === 'object') {\r\n        // Safely access properties with null checks\r\n        idToFind = supervisorId && supervisorId._id ? supervisorId._id : null;\r\n      } else {\r\n        idToFind = supervisorId;\r\n      }\r\n\r\n      if (!idToFind) return null;\r\n\r\n      const supervisor = supervisors.find(supervisor => supervisor && supervisor._id === idToFind);\r\n      if (!supervisor) return null;\r\n\r\n      // Return a plain object with just the properties we need\r\n      return {\r\n        name: supervisor.name || 'N/A',\r\n        university: supervisor.university || 'N/A',\r\n        email: supervisor.email || 'N/A'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error getting supervisor details:', error);\r\n      return null;\r\n    }\r\n  };\r\n\r\n  const container = {\r\n    hidden: { opacity: 0 },\r\n    show: { opacity: 1, transition: { staggerChildren: 0.1 } },\r\n  };\r\n\r\n  const item = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    show: { opacity: 1, y: 0 },\r\n  };\r\n\r\n  if (loading) return <Loader />;\r\n\r\n  return (\r\n    <div className=\"flex h-screen bg-gray-50\">\r\n      <AdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\r\n        <main className=\"flex-1 overflow-y-auto p-6 bg-gradient-to-br from-blue-50 to-white\">\r\n          <div className=\"max-w-7xl mx-auto\">\r\n            {error && (\r\n              <motion.div\r\n                initial={{ opacity: 0, y: -20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                className=\"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\"\r\n              >\r\n                <div className=\"flex items-center\">\r\n                  <svg className=\"w-5 h-5 text-red-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                  <p className=\"text-red-700 font-medium\">{error}</p>\r\n                </div>\r\n              </motion.div>\r\n            )}\r\n            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>\r\n              <div className=\"mb-8\">\r\n                <h1 className={`text-3xl md:text-4xl font-bold text-[${websiteColorPalette.primary}] mb-1`}>Reviews</h1>\r\n                <p className={`text-[${websiteColorPalette.text}]`}>Manage student performance reviews</p>\r\n              </div>\r\n              {/* Filters and Search */}\r\n              <motion.div\r\n                variants={container}\r\n                initial=\"hidden\"\r\n                whileInView=\"show\"\r\n                viewport={{ once: true }}\r\n                className=\"bg-white rounded-xl shadow-sm mb-6 p-6 border border-gray-100\"\r\n              >\r\n                <div className=\"flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4\">\r\n                  <div className=\"flex-1\">\r\n                    <div className=\"relative\">\r\n                      <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                        <FaSearch className=\"h-4 w-4 text-gray-400\" />\r\n                      </div>\r\n                      <input\r\n                        type=\"text\"\r\n                        className={`block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-[${websiteColorPalette.primary}] focus:border-[${websiteColorPalette.primary}] sm:text-sm`}\r\n                        placeholder=\"Search by student, supervisor, patient, or procedure...\"\r\n                        value={searchTerm}\r\n                        onChange={(e) => setSearchTerm(e.target.value)}\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"flex flex-wrap gap-2\">\r\n                    <select\r\n                      className={`block w-full md:w-auto px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-[${websiteColorPalette.primary}] focus:border-[${websiteColorPalette.primary}] sm:text-sm`}\r\n                      value={statusFilter}\r\n                      onChange={(e) => setStatusFilter(e.target.value)}\r\n                    >\r\n                      <option value=\"all\">All Statuses</option>\r\n                      <option value=\"pending\">Pending</option>\r\n                      <option value=\"accepted\">Accepted</option>\r\n                      <option value=\"declined\">Declined</option>\r\n                    </select>\r\n                    <select\r\n                      className={`block w-full md:w-auto px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-[${websiteColorPalette.primary}] focus:border-[${websiteColorPalette.primary}] sm:text-sm`}\r\n                      value={procedureFilter}\r\n                      onChange={(e) => setProcedureFilter(e.target.value)}\r\n                    >\r\n                      <option value=\"all\">All Procedures</option>\r\n                      {procedureTypes.map((type) => (\r\n                        <option key={type} value={type}>\r\n                          {type}\r\n                        </option>\r\n                      ))}\r\n                    </select>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex flex-col md:flex-row md:items-center md:justify-between gap-4\">\r\n                  <div className=\"text-sm text-gray-500\">\r\n                    <span className=\"font-medium\">{filteredReviews.length}</span> reviews found\r\n                  </div>\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <span className=\"text-sm text-gray-500\">Sort by:</span>\r\n                    <select\r\n                      className={`block w-full md:w-auto px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-[${websiteColorPalette.primary}] focus:border-[${websiteColorPalette.primary}] sm:text-sm`}\r\n                      value={sortBy}\r\n                      onChange={(e) => setSortBy(e.target.value)}\r\n                    >\r\n                      <option value=\"date\">Date</option>\r\n                      <option value=\"student\">Student</option>\r\n                      <option value=\"procedure\">Procedure</option>\r\n                      <option value=\"status\">Status</option>\r\n                    </select>\r\n                    <button\r\n                      className={`p-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-[${websiteColorPalette.primary}] focus:border-[${websiteColorPalette.primary}]`}\r\n                      onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}\r\n                    >\r\n                      {sortOrder === 'asc' ? '↑' : '↓'}\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n\r\n              {/* Review Stats */}\r\n              <motion.div\r\n                variants={container}\r\n                initial=\"hidden\"\r\n                whileInView=\"show\"\r\n                viewport={{ once: true }}\r\n                className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\"\r\n              >\r\n                <motion.div variants={item} className=\"bg-white rounded-xl shadow-sm p-4 border border-gray-100\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className={`p-3 rounded-full bg-yellow-100 text-[${websiteColorPalette.primary}]`}>\r\n                      <FaClipboardList className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <div className=\"ml-4\">\r\n                      <p className=\"text-sm text-gray-500 font-medium\">Pending Reviews</p>\r\n                      <p className=\"text-2xl font-bold text-gray-900\">\r\n                        {reviews.filter(r => r.status === 'pending').length}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n                <motion.div variants={item} className=\"bg-white rounded-xl shadow-sm p-4 border border-gray-100\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className={`p-3 rounded-full bg-green-100 text-[${websiteColorPalette.accent}]`}>\r\n                      <FaStar className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <div className=\"ml-4\">\r\n                      <p className=\"text-sm text-gray-500 font-medium\">Accepted Reviews</p>\r\n                      <p className=\"text-2xl font-bold text-gray-900\">\r\n                        {reviews.filter(r => r.status === 'accepted').length}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n                <motion.div variants={item} className=\"bg-white rounded-xl shadow-sm p-4 border border-gray-100\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className={`p-3 rounded-full bg-red-100 text-red-500`}>\r\n                      <FaClipboardList className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <div className=\"ml-4\">\r\n                      <p className=\"text-sm text-gray-500 font-medium\">Declined Reviews</p>\r\n                      <p className=\"text-2xl font-bold text-gray-900\">\r\n                        {reviews.filter(r => r.status === 'declined' || r.status === 'denied').length}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              </motion.div>\r\n\r\n              {/* Review List */}\r\n              <motion.div\r\n                variants={container}\r\n                initial=\"hidden\"\r\n                whileInView=\"show\"\r\n                viewport={{ once: true }}\r\n                className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden\"\r\n              >\r\n                <div className=\"p-6\">\r\n                  <h2 className={`text-xl font-bold text-[${websiteColorPalette.primary}] mb-6 flex items-center`}>\r\n                    <FaStar className={`h-5 w-5 mr-2 text-[${websiteColorPalette.primary}]`} />\r\n                    Review List\r\n                  </h2>\r\n                  <div className=\"overflow-x-auto\">\r\n                    <table className=\"min-w-full divide-y divide-gray-200\">\r\n                      <thead className=\"bg-gray-50\">\r\n                        <tr>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Student</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Patient</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Procedure</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Status</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Supervisor</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Submitted</th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                        {filteredReviews.length === 0 ? (\r\n                          <tr>\r\n                            <td colSpan=\"6\" className=\"px-6 py-8 text-center\">\r\n                              <div className=\"flex flex-col items-center justify-center\">\r\n                                <svg\r\n                                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                                  className=\"h-12 w-12 text-gray-400 mb-4\"\r\n                                  fill=\"none\"\r\n                                  viewBox=\"0 0 24 24\"\r\n                                  stroke=\"currentColor\"\r\n                                >\r\n                                  <path\r\n                                    strokeLinecap=\"round\"\r\n                                    strokeLinejoin=\"round\"\r\n                                    strokeWidth={2}\r\n                                    d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\r\n                                  />\r\n                                </svg>\r\n                                <h3 className=\"text-lg font-medium text-gray-900\">No reviews found</h3>\r\n                                <p className=\"mt-1 text-gray-500\">\r\n                                  {reviews.length > 0\r\n                                    ? \"No reviews match your current filters.\"\r\n                                    : \"No reviews submitted for your university.\"}\r\n                                </p>\r\n                              </div>\r\n                            </td>\r\n                          </tr>\r\n                        ) : (\r\n                          filteredReviews.map((review) => (\r\n                            <motion.tr\r\n                              key={review._id}\r\n                              variants={item}\r\n                              className=\"hover:bg-gray-50 cursor-pointer\"\r\n                              onClick={() => {\r\n                                // Create a safe copy of the review to prevent rendering objects directly\r\n                                const safeReview = {\r\n                                  ...review,\r\n                                  // Ensure studentId is a string if it's an object\r\n                                  studentId: review.studentId\r\n                                    ? (typeof review.studentId === 'object'\r\n                                      ? (review.studentId.studentId || review.studentId._id || 'Unknown ID')\r\n                                      : review.studentId)\r\n                                    : 'Unknown ID',\r\n                                  // Ensure supervisorId is a string if it's an object\r\n                                  supervisorId: review.supervisorId\r\n                                    ? (typeof review.supervisorId === 'object'\r\n                                      ? (review.supervisorId._id || 'Unknown ID')\r\n                                      : review.supervisorId)\r\n                                    : null,\r\n                                  // Ensure patientId is properly formatted\r\n                                  patientId: review.patientId\r\n                                    ? (typeof review.patientId === 'object'\r\n                                      ? {\r\n                                          ...review.patientId,\r\n                                          // Ensure fullName is a string\r\n                                          fullName: review.patientId.fullName || 'Unknown Patient',\r\n                                          // Ensure nationalId is a string\r\n                                          nationalId: review.patientId.nationalId || 'Unknown ID'\r\n                                        }\r\n                                      : review.patientId)\r\n                                    : 'Unknown Patient',\r\n                                  // Ensure steps are properly formatted\r\n                                  steps: Array.isArray(review.steps)\r\n                                    ? review.steps.map((step, index) => {\r\n                                        if (!step) return { name: `Step ${index + 1}`, completed: false, description: '' };\r\n                                        return {\r\n                                          ...step,\r\n                                          name: step.name || step.description || `Step ${index + 1}`,\r\n                                          completed: !!step.completed,\r\n                                          description: step.description || ''\r\n                                        };\r\n                                      })\r\n                                    : [],\r\n                                  // Ensure reviewSteps are properly formatted\r\n                                  reviewSteps: Array.isArray(review.reviewSteps)\r\n                                    ? review.reviewSteps.map((step, index) => {\r\n                                        if (!step) return { description: `Step ${index + 1}`, completed: false };\r\n                                        return {\r\n                                          ...step,\r\n                                          description: step.description || `Step ${index + 1}`,\r\n                                          completed: !!step.completed\r\n                                        };\r\n                                      })\r\n                                    : [],\r\n                                  // Include supervisor signature if available\r\n                                  supervisorSignature: review.supervisorSignature || null\r\n                                };\r\n\r\n                                setSelectedReview(safeReview);\r\n                              }}\r\n                            >\r\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\r\n                                <div className=\"flex items-center\">\r\n                                  <FaUserGraduate className={`h-4 w-4 text-[${websiteColorPalette.primary}] mr-2`} />\r\n                                  {review.studentName}\r\n                                </div>\r\n                              </td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                                {typeof review.patientId === 'object' && review.patientId?.fullName\r\n                                  ? review.patientId.fullName\r\n                                  : (typeof review.patientId === 'string'\r\n                                      ? review.patientId\r\n                                      : \"Unknown Patient\")}\r\n                              </td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{review.procedureType}</td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                <span\r\n                                  className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${\r\n                                    review.status === 'accepted'\r\n                                      ? `bg-green-100 text-[${websiteColorPalette.accent}]`\r\n                                      : review.status === 'denied' || review.status === 'declined'\r\n                                      ? 'bg-red-100 text-red-800'\r\n                                      : 'bg-yellow-100 text-yellow-800'\r\n                                  }`}\r\n                                >\r\n                                  {review.status === 'denied' ? 'declined' : review.status}\r\n                                </span>\r\n                              </td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                                {review.supervisorName ? (\r\n                                  <div className=\"flex items-center\">\r\n                                    <FaUserMd className={`h-4 w-4 text-[${websiteColorPalette.secondary}] mr-2`} />\r\n                                    {review.supervisorName}\r\n                                  </div>\r\n                                ) : (\r\n                                  <span className=\"text-gray-400\">Not assigned</span>\r\n                                )}\r\n                              </td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                                <div className=\"flex items-center\">\r\n                                  <FaCalendarAlt className={`h-4 w-4 text-[${websiteColorPalette.primary}] mr-2`} />\r\n                                  {new Date(review.submittedDate).toLocaleDateString()}\r\n                                </div>\r\n                              </td>\r\n                            </motion.tr>\r\n                          ))\r\n                        )}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            </motion.div>\r\n            {selectedReview && (\r\n              <motion.div\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                exit={{ opacity: 0 }}\r\n                transition={{ duration: 0.2 }}\r\n                className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 backdrop-blur-sm\"\r\n                onClick={(e) => {\r\n                  if (e.target === e.currentTarget) setSelectedReview(null);\r\n                }}\r\n              >\r\n                <motion.div\r\n                  initial={{ scale: 0.9, opacity: 0, y: 20 }}\r\n                  animate={{ scale: 1, opacity: 1, y: 0 }}\r\n                  exit={{ scale: 0.9, opacity: 0, y: 20 }}\r\n                  transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\r\n                  className=\"bg-white rounded-xl shadow-lg p-0 max-w-4xl w-full mx-4 overflow-hidden border border-gray-200\"\r\n                  onClick={(e) => e.stopPropagation()}\r\n                >\r\n                  <div className={`bg-gradient-to-r from-[${websiteColorPalette.primary}] to-[${websiteColorPalette.secondary}] p-5 text-white`}>\r\n                    <div className=\"flex justify-between items-center\">\r\n                      <div className=\"flex items-center\">\r\n                        <FaStar className=\"h-6 w-6 mr-3 text-yellow-300\" />\r\n                        <h3 className=\"text-xl font-bold\">Review Details</h3>\r\n                      </div>\r\n                      <div className=\"flex items-center space-x-2\">\r\n                        <span className={`px-3 py-1 text-xs font-medium rounded-full ${\r\n                          selectedReview.status === 'accepted'\r\n                            ? 'bg-green-100 text-green-800'\r\n                            : selectedReview.status === 'denied' || selectedReview.status === 'declined'\r\n                            ? 'bg-red-100 text-red-800'\r\n                            : 'bg-yellow-100 text-yellow-800'\r\n                        }`}>\r\n                          {selectedReview.status === 'accepted' ? 'Accepted' :\r\n                           selectedReview.status === 'denied' ? 'Declined' :\r\n                           selectedReview.status === 'declined' ? 'Declined' :\r\n                           selectedReview.status || 'Pending'}\r\n                        </span>\r\n                        <button\r\n                          onClick={() => setSelectedReview(null)}\r\n                          className={`text-white hover:text-blue-200 transition-colors bg-[${websiteColorPalette.primary}] hover:bg-blue-600 rounded-full p-2 focus:outline-none focus:ring-2 focus:ring-blue-300 focus:ring-opacity-50`}\r\n                        >\r\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                            <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\r\n                          </svg>\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"p-6 max-h-[80vh] overflow-y-auto\">\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\r\n                      <div>\r\n                        <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-3 flex items-center`}>\r\n                          <FaUserGraduate className={`mr-2 text-[${websiteColorPalette.primary}]`} /> Student Information\r\n                        </h4>\r\n                        <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100 space-y-3`}>\r\n                          <div className=\"flex items-center\">\r\n                            <span className=\"w-24 text-sm font-medium text-gray-600\">Name:</span>\r\n                            <span className=\"text-gray-900 font-medium\">{selectedReview.studentName}</span>\r\n                          </div>\r\n                          <div className=\"flex items-center\">\r\n                            <span className=\"w-24 text-sm font-medium text-gray-600\">ID:</span>\r\n                            <span className=\"text-gray-900\">\r\n                              {typeof selectedReview.studentId === 'object'\r\n                                ? (selectedReview.studentId.studentId || selectedReview.studentId._id || 'N/A')\r\n                                : (selectedReview.studentId || 'N/A')}\r\n                            </span>\r\n                          </div>\r\n                          {(() => {\r\n                            const studentDetails = getStudentDetails(selectedReview.studentId);\r\n                            return studentDetails ? (\r\n                              <div className=\"flex items-center\">\r\n                                <span className=\"w-24 text-sm font-medium text-gray-600\">University:</span>\r\n                                <span className=\"text-gray-900\">{studentDetails.university || 'N/A'}</span>\r\n                              </div>\r\n                            ) : null;\r\n                          })()}\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div>\r\n                        <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-3 flex items-center`}>\r\n                          <FaUserMd className={`mr-2 text-[${websiteColorPalette.primary}]`} /> Supervisor Information\r\n                        </h4>\r\n                        <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100 space-y-3`}>\r\n                          {selectedReview.supervisorName ? (\r\n                            <>\r\n                              <div className=\"flex items-center\">\r\n                                <span className=\"w-24 text-sm font-medium text-gray-600\">Name:</span>\r\n                                <span className=\"text-gray-900 font-medium\">{selectedReview.supervisorName}</span>\r\n                              </div>\r\n                              <div className=\"flex items-center\">\r\n                                <span className=\"w-24 text-sm font-medium text-gray-600\">Reviewed:</span>\r\n                                <span className=\"text-gray-900\">{selectedReview.reviewedDate ?\r\n                                  new Date(selectedReview.reviewedDate).toLocaleString() : 'Not yet reviewed'}</span>\r\n                              </div>\r\n                              {(() => {\r\n                                const supervisorDetails = getSupervisorDetails(selectedReview.supervisorId);\r\n                                return supervisorDetails ? (\r\n                                  <div className=\"flex items-center\">\r\n                                    <span className=\"w-24 text-sm font-medium text-gray-600\">University:</span>\r\n                                    <span className=\"text-gray-900\">{supervisorDetails.university || 'N/A'}</span>\r\n                                  </div>\r\n                                ) : null;\r\n                              })()}\r\n                            </>\r\n                          ) : (\r\n                            <p className=\"text-gray-500 italic\">No supervisor assigned yet</p>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Patient Information */}\r\n                    <div className=\"mb-6\">\r\n                      <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-3 flex items-center`}>\r\n                        <FaUserMd className={`mr-2 text-[${websiteColorPalette.primary}]`} /> Patient Information\r\n                      </h4>\r\n                      <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`}>\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                          <div>\r\n                            <h4 className=\"text-sm font-medium text-gray-500\">Name</h4>\r\n                            <p className=\"text-sm text-gray-900 mt-1\">\r\n                              {typeof selectedReview.patientId === 'object' && selectedReview.patientId?.fullName\r\n                                ? selectedReview.patientId.fullName\r\n                                : (typeof selectedReview.patientId === 'string'\r\n                                    ? selectedReview.patientId\r\n                                    : 'Unknown')}\r\n                            </p>\r\n                          </div>\r\n                          <div>\r\n                            <h4 className=\"text-sm font-medium text-gray-500\">National ID</h4>\r\n                            <p className=\"text-sm text-gray-900 mt-1\">\r\n                              {typeof selectedReview.patientId === 'object' && selectedReview.patientId?.nationalId\r\n                                ? selectedReview.patientId.nationalId\r\n                                : 'N/A'}\r\n                            </p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Procedure Details */}\r\n                    <div className=\"mb-6\">\r\n                      <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-3 flex items-center`}>\r\n                        <FaClipboardList className={`mr-2 text-[${websiteColorPalette.primary}]`} /> Procedure Details\r\n                      </h4>\r\n                      <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`}>\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                          <div>\r\n                            <h4 className=\"text-sm font-medium text-gray-500\">Procedure Type</h4>\r\n                            <p className=\"text-sm text-gray-900 mt-1 font-medium\">{selectedReview.procedureType}</p>\r\n                          </div>\r\n                          <div>\r\n                            <h4 className=\"text-sm font-medium text-gray-500\">Status</h4>\r\n                            <p className=\"text-sm mt-1\">\r\n                              <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${\r\n                                selectedReview.status === 'accepted'\r\n                                  ? 'bg-green-100 text-green-800'\r\n                                  : selectedReview.status === 'denied' || selectedReview.status === 'declined'\r\n                                  ? 'bg-red-100 text-red-800'\r\n                                  : 'bg-yellow-100 text-yellow-800'\r\n                              }`}>\r\n                                {selectedReview.status === 'denied' ? 'Declined' :\r\n                                 selectedReview.status === 'accepted' ? 'Accepted' :\r\n                                 selectedReview.status === 'pending' ? 'Pending' :\r\n                                 selectedReview.status}\r\n                              </span>\r\n                            </p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"mb-6\">\r\n                      <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-3 flex items-center`}>\r\n                        <FaStar className={`mr-2 text-[${websiteColorPalette.primary}]`} /> Ratings\r\n                      </h4>\r\n                      <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`}>\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                          <div className=\"bg-white p-4 rounded-lg shadow-sm\">\r\n                            <p className=\"font-medium mb-3 text-gray-700\">Procedure Quality</p>\r\n                            <div className=\"flex items-center\">\r\n                              {[1, 2, 3, 4, 5].map((star) => (\r\n                                <FaStar\r\n                                  key={star}\r\n                                  className={`h-6 w-6 ${\r\n                                    star <= selectedReview.procedureQuality\r\n                                      ? 'text-yellow-400'\r\n                                      : 'text-gray-200'\r\n                                  }`}\r\n                                />\r\n                              ))}\r\n                              <span className=\"ml-3 text-gray-700 font-bold\">{selectedReview.procedureQuality}/5</span>\r\n                            </div>\r\n                          </div>\r\n                          <div className=\"bg-white p-4 rounded-lg shadow-sm\">\r\n                            <p className=\"font-medium mb-3 text-gray-700\">Patient Interaction</p>\r\n                            <div className=\"flex items-center\">\r\n                              {[1, 2, 3, 4, 5].map((star) => (\r\n                                <FaStar\r\n                                  key={star}\r\n                                  className={`h-6 w-6 ${\r\n                                    star <= selectedReview.patientInteraction\r\n                                      ? 'text-yellow-400'\r\n                                      : 'text-gray-200'\r\n                                  }`}\r\n                                />\r\n                              ))}\r\n                              <span className=\"ml-3 text-gray-700 font-bold\">{selectedReview.patientInteraction}/5</span>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Comments Section */}\r\n                    <div className=\"mb-6\">\r\n                      <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-3 flex items-center`}>\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className={`h-5 w-5 mr-2 text-[${websiteColorPalette.primary}]`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                          <path fillRule=\"evenodd\" d=\"M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z\" clipRule=\"evenodd\" />\r\n                        </svg>\r\n                        Comments\r\n                      </h4>\r\n                      <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100 space-y-5`}>\r\n                        <div className=\"bg-white p-4 rounded-lg shadow-sm\">\r\n                          <p className=\"font-medium text-gray-700 mb-2 flex items-center\">\r\n                            <FaUserGraduate className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />\r\n                            Student Comment:\r\n                          </p>\r\n                          <p className=\"mt-1 text-gray-600 bg-gray-50 p-3 rounded-md border border-gray-100\">\r\n                            {selectedReview.comment || 'No comment provided'}\r\n                          </p>\r\n                        </div>\r\n                        <div className=\"bg-white p-4 rounded-lg shadow-sm\">\r\n                          <p className=\"font-medium text-gray-700 mb-2 flex items-center\">\r\n                            <FaUserMd className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />\r\n                            Supervisor Note:\r\n                          </p>\r\n                          <p className=\"mt-1 text-gray-600 bg-gray-50 p-3 rounded-md border border-gray-100\">\r\n                            {selectedReview.note || 'No note provided'}\r\n                          </p>\r\n                        </div>\r\n\r\n                        {/* Supervisor Signature */}\r\n                        {selectedReview.supervisorSignature && (\r\n                          <div className=\"bg-white p-4 rounded-lg shadow-sm\">\r\n                            <p className=\"font-medium text-gray-700 mb-2 flex items-center\">\r\n                              <FaUserMd className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />\r\n                              Supervisor Signature:\r\n                            </p>\r\n                            <div className=\"mt-1 bg-gray-50 p-3 rounded-md border border-gray-100 flex justify-center\">\r\n                              {selectedReview.supervisorSignature.startsWith('data:image') ? (\r\n                                <img\r\n                                  src={selectedReview.supervisorSignature}\r\n                                  alt=\"Supervisor Signature\"\r\n                                  className=\"max-h-20\"\r\n                                />\r\n                              ) : (\r\n                                <p className=\"font-signature text-lg text-gray-700\">\r\n                                  {selectedReview.supervisorSignature}\r\n                                </p>\r\n                              )}\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Review Steps Section */}\r\n                    <div className=\"mb-6\">\r\n                      <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-3 flex items-center`}>\r\n                        <FaClipboardList className={`mr-2 text-[${websiteColorPalette.primary}]`} /> Procedure Steps\r\n                      </h4>\r\n                      <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`}>\r\n                        {selectedReview.reviewSteps && Array.isArray(selectedReview.reviewSteps) && selectedReview.reviewSteps.length > 0 ? (\r\n                          <div className=\"bg-white rounded-lg overflow-hidden\">\r\n                            <div className=\"divide-y divide-gray-200\">\r\n                              {selectedReview.reviewSteps.map((step, index) => {\r\n                                // Ensure step is an object\r\n                                const safeStep = step || { completed: false };\r\n                                return (\r\n                                  <div\r\n                                    key={index}\r\n                                    className={`p-3 flex items-center justify-between ${\r\n                                      safeStep.completed ? 'bg-green-50' : 'bg-white'\r\n                                    }`}\r\n                                  >\r\n                                    <div className=\"flex items-center\">\r\n                                      <span className={`inline-block w-6 text-center mr-2 text-[${websiteColorPalette.primary}] font-bold`}>{index + 1}.</span>\r\n                                      <span className=\"text-sm font-medium text-gray-900\">\r\n                                        {safeStep.description || safeStep.name || `Step ${index + 1}`}\r\n                                      </span>\r\n                                    </div>\r\n                                    <div>\r\n                                      {safeStep.completed ? (\r\n                                        <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\r\n                                          <svg className=\"w-4 h-4 mr-1 text-green-500\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                                            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\r\n                                          </svg>\r\n                                          Completed\r\n                                        </span>\r\n                                      ) : (\r\n                                        <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\">\r\n                                          <svg className=\"w-4 h-4 mr-1 text-gray-500\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                                            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8 9a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z\" clipRule=\"evenodd\" />\r\n                                          </svg>\r\n                                          Not completed\r\n                                        </span>\r\n                                      )}\r\n                                    </div>\r\n                                  </div>\r\n                                );\r\n                              })}\r\n                            </div>\r\n                          </div>\r\n                        ) : selectedReview.steps && Array.isArray(selectedReview.steps) && selectedReview.steps.length > 0 ? (\r\n                          <div className=\"bg-white rounded-lg overflow-hidden\">\r\n                            <div className=\"divide-y divide-gray-200\">\r\n                              {selectedReview.steps.map((step, index) => {\r\n                                // Ensure step is an object\r\n                                const safeStep = step || { completed: false };\r\n                                return (\r\n                                  <div\r\n                                    key={index}\r\n                                    className={`p-3 flex items-center justify-between ${\r\n                                      safeStep.completed ? 'bg-green-50' : 'bg-white'\r\n                                    }`}\r\n                                  >\r\n                                    <div className=\"flex items-center\">\r\n                                      <span className={`inline-block w-6 text-center mr-2 text-[${websiteColorPalette.primary}] font-bold`}>{index + 1}.</span>\r\n                                      <span className=\"text-sm font-medium text-gray-900\">\r\n                                        {safeStep.description || safeStep.name || `Step ${index + 1}`}\r\n                                      </span>\r\n                                    </div>\r\n                                    <div>\r\n                                      {safeStep.completed ? (\r\n                                        <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\r\n                                          <svg className=\"w-4 h-4 mr-1 text-green-500\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                                            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\r\n                                          </svg>\r\n                                          Completed\r\n                                        </span>\r\n                                      ) : (\r\n                                        <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\">\r\n                                          <svg className=\"w-4 h-4 mr-1 text-gray-500\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                                            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8 9a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z\" clipRule=\"evenodd\" />\r\n                                          </svg>\r\n                                          Not completed\r\n                                        </span>\r\n                                      )}\r\n                                    </div>\r\n                                  </div>\r\n                                );\r\n                              })}\r\n                            </div>\r\n                          </div>\r\n                        ) : (\r\n                          <p className=\"text-gray-500 italic text-center py-4 bg-white rounded-lg\">No procedure steps available</p>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"border-t border-gray-200 p-5 flex justify-between items-center bg-gray-50\">\r\n                    <div className=\"text-sm text-gray-500\">\r\n                      <span className=\"font-medium\">Submitted:</span> {new Date(selectedReview.submittedDate).toLocaleString()}\r\n                      {selectedReview.reviewedDate && (\r\n                        <span className=\"ml-4\">\r\n                          <span className=\"font-medium\">Reviewed:</span> {new Date(selectedReview.reviewedDate).toLocaleString()}\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                    <motion.button\r\n                      whileHover={{ scale: 1.03 }}\r\n                      whileTap={{ scale: 0.97 }}\r\n                      onClick={() => setSelectedReview(null)}\r\n                      className={`px-5 py-2.5 bg-[${websiteColorPalette.primary}] text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm flex items-center font-medium`}\r\n                    >\r\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                        <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\r\n                      </svg>\r\n                      Close\r\n                    </motion.button>\r\n                  </div>\r\n                </motion.div>\r\n              </motion.div>\r\n            )}\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Reviews;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,eAAe,QAAQ,gBAAgB;;AAE3G;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,mBAAmB,GAAG;EAC1BC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,UAAU,EAAE,SAAS;EACrBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE;AACV,CAAC;AAED,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EACpB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuC,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6C,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC+C,MAAM,EAAEC,SAAS,CAAC,GAAGhD,QAAQ,CAAC,MAAM,CAAC;EAC5C,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,MAAM,CAAC;EAClD,MAAM,CAACmD,QAAQ,EAAEC,WAAW,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqD,WAAW,EAAEC,cAAc,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuD,cAAc,EAAEC,iBAAiB,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAMyD,QAAQ,GAAGvD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEwD,IAAI;IAAEC;EAAM,CAAC,GAAGvD,OAAO,CAAC,CAAC;EAEjCH,SAAS,CAAC,MAAM;IACd,MAAM2D,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI,CAACF,IAAI,IAAI,CAACC,KAAK,EAAE;QACnBnB,QAAQ,CAAC,gCAAgC,CAAC;QAC1CF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,IAAI,CAACoB,IAAI,CAACG,UAAU,EAAE;QACpBrB,QAAQ,CAAC,0DAA0D,CAAC;QACpEF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,IAAI;QACF,MAAMwB,MAAM,GAAG;UAAEC,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAUL,KAAK;UAAG;QAAE,CAAC;;QAEhE;QACA,MAAM,CAACM,WAAW,EAAEC,cAAc,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACtDjE,KAAK,CAACkE,GAAG,CAAC,uDAAuDC,kBAAkB,CAACZ,IAAI,CAACG,UAAU,CAAC,EAAE,EAAEC,MAAM,CAAC,EAC/G3D,KAAK,CAACkE,GAAG,CAAC,0DAA0DC,kBAAkB,CAACZ,IAAI,CAACG,UAAU,CAAC,EAAE,EAAEC,MAAM,CAAC,CACnH,CAAC;;QAEF;QACA,MAAMX,QAAQ,GAAGc,WAAW,CAACM,IAAI,IAAI,EAAE;QACvC,MAAMlB,WAAW,GAAGa,cAAc,CAACK,IAAI,IAAI,EAAE;QAE7CC,OAAO,CAACC,GAAG,CAAC,kBAAkBtB,QAAQ,CAACuB,MAAM,iBAAiBrB,WAAW,CAACqB,MAAM,cAAc,CAAC;;QAE/F;QACA,IAAIvB,QAAQ,CAACuB,MAAM,GAAG,CAAC,EAAE;UACvBF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;YAClCE,IAAI,EAAExB,QAAQ,CAAC,CAAC,CAAC,CAACwB,IAAI;YACtBC,GAAG,EAAEzB,QAAQ,CAAC,CAAC,CAAC,CAACyB,GAAG;YACpBC,SAAS,EAAE1B,QAAQ,CAAC,CAAC,CAAC,CAAC0B;UACzB,CAAC,CAAC;QACJ;QAEAzB,WAAW,CAACD,QAAQ,CAAC;QACrBG,cAAc,CAACD,WAAW,CAAC;;QAE3B;QACA,IAAIF,QAAQ,CAACuB,MAAM,KAAK,CAAC,EAAE;UACzBlC,QAAQ,CAAC,uCAAuC,CAAC;UACjDF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,IAAIwC,UAAU,GAAG,EAAE;;QAEnB;QACAN,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAE/C,MAAMM,cAAc,GAAG5B,QAAQ,CAAC6B,GAAG,CAACC,OAAO,IAAI;UAC7C,MAAMC,GAAG,GAAG,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,kCAAkCJ,OAAO,CAACJ,SAAS,EAAE;UACjGL,OAAO,CAACC,GAAG,CAAC,gCAAgCQ,OAAO,CAACN,IAAI,cAAcO,GAAG,EAAE,CAAC;UAE5E,OAAO/E,KAAK,CAACkE,GAAG,CAACa,GAAG,EAAEpB,MAAM,CAAC,CAC1BwB,IAAI,CAACC,QAAQ,IAAI;YAChBf,OAAO,CAACC,GAAG,CAAC,wBAAwBc,QAAQ,CAAChB,IAAI,CAACG,MAAM,wBAAwBO,OAAO,CAACN,IAAI,EAAE,CAAC;YAC/F,OAAOY,QAAQ;UACjB,CAAC,CAAC,CACDC,KAAK,CAACjD,KAAK,IAAI;YACdiC,OAAO,CAACjC,KAAK,CAAC,sCAAsC0C,OAAO,CAACN,IAAI,SAASM,OAAO,CAACJ,SAAS,IAAI,EAAEtC,KAAK,CAACkD,OAAO,CAAC;YAC9G;YACAjB,OAAO,CAACC,GAAG,CAAC,wCAAwCQ,OAAO,CAACN,IAAI,EAAE,CAAC;YACnE,OAAOxE,KAAK,CAACkE,GAAG,CAAC,uDAAuDY,OAAO,CAACL,GAAG,EAAE,EAAEd,MAAM,CAAC,CAC3FwB,IAAI,CAACC,QAAQ,IAAI;cAChBf,OAAO,CAACC,GAAG,CAAC,gCAAgCc,QAAQ,CAAChB,IAAI,CAACG,MAAM,wBAAwBO,OAAO,CAACN,IAAI,EAAE,CAAC;cACvG,OAAOY,QAAQ;YACjB,CAAC,CAAC,CACDC,KAAK,CAACE,aAAa,IAAI;cACtBlB,OAAO,CAACjC,KAAK,CAAC,oCAAoC0C,OAAO,CAACN,IAAI,GAAG,EAAEe,aAAa,CAACD,OAAO,CAAC;cACzF,OAAO;gBAAElB,IAAI,EAAE;cAAG,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC;UACN,CAAC,CAAC;QACN,CAAC,CAAC;;QAEF;QACA,MAAMoB,cAAc,GAAG,MAAMxB,OAAO,CAACC,GAAG,CAACW,cAAc,CAAC;;QAExD;QACAY,cAAc,CAACC,OAAO,CAACC,MAAM,IAAI;UAC/B,IAAIA,MAAM,CAACtB,IAAI,IAAIuB,KAAK,CAACC,OAAO,CAACF,MAAM,CAACtB,IAAI,CAAC,EAAE;YAC7CO,UAAU,GAAG,CAAC,GAAGA,UAAU,EAAE,GAAGe,MAAM,CAACtB,IAAI,CAAC;UAC9C;QACF,CAAC,CAAC;QAEFC,OAAO,CAACC,GAAG,CAAC,SAASK,UAAU,CAACJ,MAAM,6CAA6ChB,IAAI,CAACG,UAAU,EAAE,CAAC;;QAErG;QACA,MAAMmC,aAAa,GAAGF,KAAK,CAACG,IAAI,CAAC,IAAIC,GAAG,CAACpB,UAAU,CAACE,GAAG,CAACmB,MAAM,IAAI,CAACA,MAAM,CAACvB,GAAG,EAAEuB,MAAM,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC;;QAElG;QACA,MAAMnE,eAAe,GAAG+D,aAAa,CAACK,MAAM,CAACF,MAAM,IACjD,EAAEA,MAAM,CAACG,SAAS,IAAIH,MAAM,CAACG,SAAS,CAACC,UAAU,KAAK,mBAAmB,CAC3E,CAAC;QAEDvE,UAAU,CAACC,eAAe,CAAC;QAC3BC,kBAAkB,CAACD,eAAe,CAAC;;QAEnC;QACA,MAAMuE,oBAAoB,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACxE,eAAe,CAAC+C,GAAG,CAACmB,MAAM,IAAIA,MAAM,CAACO,aAAa,CAAC,CAACL,MAAM,CAACM,OAAO,CAAC,CAAC,CAAC;QAC9GnD,iBAAiB,CAACgD,oBAAoB,CAAC;QAEvC,IAAIvE,eAAe,CAACyC,MAAM,KAAK,CAAC,EAAE;UAChClC,QAAQ,CAAC,mDAAmD,CAAC;QAC/D;MACF,CAAC,CAAC,OAAOoE,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA,EAAAC,cAAA;QACZ1C,OAAO,CAACjC,KAAK,CAAC,cAAc,EAAE,EAAAsE,aAAA,GAAAD,GAAG,CAACrB,QAAQ,cAAAsB,aAAA,uBAAZA,aAAA,CAActC,IAAI,KAAIqC,GAAG,CAACnB,OAAO,CAAC;QAChE,MAAM0B,YAAY,GAChB,EAAAL,cAAA,GAAAF,GAAG,CAACrB,QAAQ,cAAAuB,cAAA,uBAAZA,cAAA,CAAcM,MAAM,MAAK,GAAG,GACxB,6BAA6B,GAC7B,EAAAL,cAAA,GAAAH,GAAG,CAACrB,QAAQ,cAAAwB,cAAA,uBAAZA,cAAA,CAAcK,MAAM,MAAK,GAAG,GAC5B,oCAAoC,GACpC,EAAAJ,cAAA,GAAAJ,GAAG,CAACrB,QAAQ,cAAAyB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAczC,IAAI,cAAA0C,mBAAA,uBAAlBA,mBAAA,CAAoBxB,OAAO,KAAI,wBAAwB;QAC7DjD,QAAQ,CAAC2E,YAAY,CAAC;QACtB,IAAI,EAAAD,cAAA,GAAAN,GAAG,CAACrB,QAAQ,cAAA2B,cAAA,uBAAZA,cAAA,CAAcE,MAAM,MAAK,GAAG,EAAE3D,QAAQ,CAAC,QAAQ,CAAC;MACtD,CAAC,SAAS;QACRnB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDsB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACF,IAAI,EAAEC,KAAK,EAAEF,QAAQ,CAAC,CAAC;;EAE3B;EACAxD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC8B,OAAO,CAAC2C,MAAM,EAAE;IAErB,IAAImB,MAAM,GAAG,CAAC,GAAG9D,OAAO,CAAC;;IAEzB;IACA,IAAIU,UAAU,EAAE;MACd,MAAM4E,WAAW,GAAG5E,UAAU,CAAC6E,WAAW,CAAC,CAAC;MAC5CzB,MAAM,GAAGA,MAAM,CAACQ,MAAM,CACpBF,MAAM;QAAA,IAAAoB,mBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,qBAAA;QAAA,OACJ,EAAAJ,mBAAA,GAAApB,MAAM,CAACyB,WAAW,cAAAL,mBAAA,uBAAlBA,mBAAA,CAAoBD,WAAW,CAAC,CAAC,CAACO,QAAQ,CAACR,WAAW,CAAC,OAAAG,qBAAA,GACvDrB,MAAM,CAAC2B,cAAc,cAAAN,qBAAA,uBAArBA,qBAAA,CAAuBF,WAAW,CAAC,CAAC,CAACO,QAAQ,CAACR,WAAW,CAAC,OAAAI,iBAAA,GAC1DtB,MAAM,CAACG,SAAS,cAAAmB,iBAAA,wBAAAC,qBAAA,GAAhBD,iBAAA,CAAkBM,QAAQ,cAAAL,qBAAA,uBAA1BA,qBAAA,CAA4BJ,WAAW,CAAC,CAAC,CAACO,QAAQ,CAACR,WAAW,CAAC,OAAAM,qBAAA,GAC/DxB,MAAM,CAACO,aAAa,cAAAiB,qBAAA,uBAApBA,qBAAA,CAAsBL,WAAW,CAAC,CAAC,CAACO,QAAQ,CAACR,WAAW,CAAC;MAAA,CAC7D,CAAC;IACH;;IAEA;IACA,IAAI1E,YAAY,KAAK,KAAK,EAAE;MAC1B,IAAIA,YAAY,KAAK,UAAU,EAAE;QAC/B;QACAkD,MAAM,GAAGA,MAAM,CAACQ,MAAM,CAACF,MAAM,IAAIA,MAAM,CAACiB,MAAM,KAAK,UAAU,IAAIjB,MAAM,CAACiB,MAAM,KAAK,QAAQ,CAAC;MAC9F,CAAC,MAAM;QACLvB,MAAM,GAAGA,MAAM,CAACQ,MAAM,CAACF,MAAM,IAAIA,MAAM,CAACiB,MAAM,KAAKzE,YAAY,CAAC;MAClE;IACF;;IAEA;IACA,IAAIE,eAAe,KAAK,KAAK,EAAE;MAC7BgD,MAAM,GAAGA,MAAM,CAACQ,MAAM,CAACF,MAAM,IAAIA,MAAM,CAACO,aAAa,KAAK7D,eAAe,CAAC;IAC5E;;IAEA;IACAgD,MAAM,CAACmC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACpB,IAAIC,UAAU,GAAG,CAAC;MAElB,IAAIpF,MAAM,KAAK,MAAM,EAAE;QACrBoF,UAAU,GAAG,IAAIC,IAAI,CAACH,CAAC,CAACI,aAAa,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,aAAa,CAAC;MACpE,CAAC,MAAM,IAAItF,MAAM,KAAK,SAAS,EAAE;QAC/BoF,UAAU,GAAGF,CAAC,CAACL,WAAW,CAACU,aAAa,CAACJ,CAAC,CAACN,WAAW,CAAC;MACzD,CAAC,MAAM,IAAI7E,MAAM,KAAK,WAAW,EAAE;QACjCoF,UAAU,GAAGF,CAAC,CAACvB,aAAa,CAAC4B,aAAa,CAACJ,CAAC,CAACxB,aAAa,CAAC;MAC7D,CAAC,MAAM,IAAI3D,MAAM,KAAK,QAAQ,EAAE;QAC9BoF,UAAU,GAAGF,CAAC,CAACb,MAAM,CAACkB,aAAa,CAACJ,CAAC,CAACd,MAAM,CAAC;MAC/C;MAEA,OAAOnE,SAAS,KAAK,KAAK,GAAGkF,UAAU,GAAG,CAACA,UAAU;IACvD,CAAC,CAAC;IAEFjG,kBAAkB,CAAC2D,MAAM,CAAC;EAC5B,CAAC,EAAE,CAAC9D,OAAO,EAAEU,UAAU,EAAEE,YAAY,EAAEE,eAAe,EAAEE,MAAM,EAAEE,SAAS,CAAC,CAAC;;EAE3E;EACA,MAAMsF,iBAAiB,GAAI1D,SAAS,IAAK;IACvC,IAAI,CAACA,SAAS,IAAI,CAAC1B,QAAQ,CAACuB,MAAM,EAAE,OAAO,IAAI;IAE/C,IAAI;MACF;MACA,IAAI8D,QAAQ;MAEZ,IAAI,OAAO3D,SAAS,KAAK,QAAQ,EAAE;QACjC;QACA2D,QAAQ,GAAG3D,SAAS,GAAIA,SAAS,CAACA,SAAS,KAAKA,SAAS,CAACD,GAAG,GAAGC,SAAS,CAACD,GAAG,GAAG,IAAI,CAAC,GAAI,IAAI;MAC/F,CAAC,MAAM;QACL4D,QAAQ,GAAG3D,SAAS;MACtB;MAEA,IAAI,CAAC2D,QAAQ,EAAE,OAAO,IAAI;MAE1B,MAAMvD,OAAO,GAAG9B,QAAQ,CAACsF,IAAI,CAACxD,OAAO,IACnCA,OAAO,KAAKA,OAAO,CAACJ,SAAS,KAAK2D,QAAQ,IAAIvD,OAAO,CAACL,GAAG,KAAK4D,QAAQ,CACxE,CAAC;MAED,IAAI,CAACvD,OAAO,EAAE,OAAO,IAAI;;MAEzB;MACA,OAAO;QACLN,IAAI,EAAEM,OAAO,CAACN,IAAI,IAAI,KAAK;QAC3BE,SAAS,EAAEI,OAAO,CAACJ,SAAS,IAAI,KAAK;QACrChB,UAAU,EAAEoB,OAAO,CAACpB,UAAU,IAAI,KAAK;QACvC6E,KAAK,EAAEzD,OAAO,CAACyD,KAAK,IAAI;MAC1B,CAAC;IACH,CAAC,CAAC,OAAOnG,KAAK,EAAE;MACdiC,OAAO,CAACjC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAO,IAAI;IACb;EACF,CAAC;;EAED;EACA,MAAMoG,oBAAoB,GAAIC,YAAY,IAAK;IAC7C,IAAI,CAACA,YAAY,IAAI,CAACvF,WAAW,CAACqB,MAAM,EAAE,OAAO,IAAI;IAErD,IAAI;MACF;MACA,IAAI8D,QAAQ;MAEZ,IAAI,OAAOI,YAAY,KAAK,QAAQ,EAAE;QACpC;QACAJ,QAAQ,GAAGI,YAAY,IAAIA,YAAY,CAAChE,GAAG,GAAGgE,YAAY,CAAChE,GAAG,GAAG,IAAI;MACvE,CAAC,MAAM;QACL4D,QAAQ,GAAGI,YAAY;MACzB;MAEA,IAAI,CAACJ,QAAQ,EAAE,OAAO,IAAI;MAE1B,MAAMK,UAAU,GAAGxF,WAAW,CAACoF,IAAI,CAACI,UAAU,IAAIA,UAAU,IAAIA,UAAU,CAACjE,GAAG,KAAK4D,QAAQ,CAAC;MAC5F,IAAI,CAACK,UAAU,EAAE,OAAO,IAAI;;MAE5B;MACA,OAAO;QACLlE,IAAI,EAAEkE,UAAU,CAAClE,IAAI,IAAI,KAAK;QAC9Bd,UAAU,EAAEgF,UAAU,CAAChF,UAAU,IAAI,KAAK;QAC1C6E,KAAK,EAAEG,UAAU,CAACH,KAAK,IAAI;MAC7B,CAAC;IACH,CAAC,CAAC,OAAOnG,KAAK,EAAE;MACdiC,OAAO,CAACjC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,OAAO,IAAI;IACb;EACF,CAAC;EAED,MAAMuG,SAAS,GAAG;IAChBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEE,UAAU,EAAE;QAAEC,eAAe,EAAE;MAAI;IAAE;EAC3D,CAAC;EAED,MAAMC,IAAI,GAAG;IACXL,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAG,CAAC;IAC7BJ,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAE;EAC3B,CAAC;EAED,IAAIhH,OAAO,EAAE,oBAAOrB,OAAA,CAACT,MAAM;IAAA+I,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAE9B,oBACEzI,OAAA;IAAK0I,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvC3I,OAAA,CAACV,YAAY;MAACsJ,MAAM,EAAE/H,WAAY;MAACgI,SAAS,EAAE/H;IAAe;MAAAwH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChEzI,OAAA;MAAK0I,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnD3I,OAAA,CAACX,MAAM;QAACyJ,aAAa,EAAEA,CAAA,KAAMhI,cAAc,CAAC,CAACD,WAAW;MAAE;QAAAyH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7DzI,OAAA;QAAM0I,SAAS,EAAC,oEAAoE;QAAAC,QAAA,eAClF3I,OAAA;UAAK0I,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAC/BpH,KAAK,iBACJvB,OAAA,CAACR,MAAM,CAACuJ,GAAG;YACTC,OAAO,EAAE;cAAEhB,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCY,OAAO,EAAE;cAAEjB,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE;YAAE,CAAE;YAC9BK,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAE7E3I,OAAA;cAAK0I,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC3I,OAAA;gBAAK0I,SAAS,EAAC,2BAA2B;gBAACQ,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAR,QAAA,eAChF3I,OAAA;kBAAMoJ,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,yNAAyN;kBAACC,QAAQ,EAAC;gBAAS;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvQ,CAAC,eACNzI,OAAA;gBAAG0I,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAEpH;cAAK;gBAAA+G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eACDzI,OAAA,CAACR,MAAM,CAACuJ,GAAG;YAACC,OAAO,EAAE;cAAEhB,OAAO,EAAE;YAAE,CAAE;YAACiB,OAAO,EAAE;cAAEjB,OAAO,EAAE;YAAE,CAAE;YAACE,UAAU,EAAE;cAAEqB,QAAQ,EAAE;YAAI,CAAE;YAAAZ,QAAA,gBAC1F3I,OAAA;cAAK0I,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB3I,OAAA;gBAAI0I,SAAS,EAAE,wCAAwCvI,mBAAmB,CAACC,OAAO,QAAS;gBAAAuI,QAAA,EAAC;cAAO;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxGzI,OAAA;gBAAG0I,SAAS,EAAE,SAASvI,mBAAmB,CAACI,IAAI,GAAI;gBAAAoI,QAAA,EAAC;cAAkC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF,CAAC,eAENzI,OAAA,CAACR,MAAM,CAACuJ,GAAG;cACTS,QAAQ,EAAE1B,SAAU;cACpBkB,OAAO,EAAC,QAAQ;cAChBS,WAAW,EAAC,MAAM;cAClBC,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBjB,SAAS,EAAC,+DAA+D;cAAAC,QAAA,gBAEzE3I,OAAA;gBAAK0I,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,gBACtF3I,OAAA;kBAAK0I,SAAS,EAAC,QAAQ;kBAAAC,QAAA,eACrB3I,OAAA;oBAAK0I,SAAS,EAAC,UAAU;oBAAAC,QAAA,gBACvB3I,OAAA;sBAAK0I,SAAS,EAAC,sEAAsE;sBAAAC,QAAA,eACnF3I,OAAA,CAACN,QAAQ;wBAACgJ,SAAS,EAAC;sBAAuB;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CAAC,eACNzI,OAAA;sBACE4J,IAAI,EAAC,MAAM;sBACXlB,SAAS,EAAE,yIAAyIvI,mBAAmB,CAACC,OAAO,mBAAmBD,mBAAmB,CAACC,OAAO,cAAe;sBAC5OyJ,WAAW,EAAC,yDAAyD;sBACrEC,KAAK,EAAErI,UAAW;sBAClBsI,QAAQ,EAAGC,CAAC,IAAKtI,aAAa,CAACsI,CAAC,CAACC,MAAM,CAACH,KAAK;oBAAE;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNzI,OAAA;kBAAK0I,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnC3I,OAAA;oBACE0I,SAAS,EAAE,wHAAwHvI,mBAAmB,CAACC,OAAO,mBAAmBD,mBAAmB,CAACC,OAAO,cAAe;oBAC3N0J,KAAK,EAAEnI,YAAa;oBACpBoI,QAAQ,EAAGC,CAAC,IAAKpI,eAAe,CAACoI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAAAnB,QAAA,gBAEjD3I,OAAA;sBAAQ8J,KAAK,EAAC,KAAK;sBAAAnB,QAAA,EAAC;oBAAY;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACzCzI,OAAA;sBAAQ8J,KAAK,EAAC,SAAS;sBAAAnB,QAAA,EAAC;oBAAO;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxCzI,OAAA;sBAAQ8J,KAAK,EAAC,UAAU;sBAAAnB,QAAA,EAAC;oBAAQ;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC1CzI,OAAA;sBAAQ8J,KAAK,EAAC,UAAU;sBAAAnB,QAAA,EAAC;oBAAQ;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC,eACTzI,OAAA;oBACE0I,SAAS,EAAE,wHAAwHvI,mBAAmB,CAACC,OAAO,mBAAmBD,mBAAmB,CAACC,OAAO,cAAe;oBAC3N0J,KAAK,EAAEjI,eAAgB;oBACvBkI,QAAQ,EAAGC,CAAC,IAAKlI,kBAAkB,CAACkI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAAAnB,QAAA,gBAEpD3I,OAAA;sBAAQ8J,KAAK,EAAC,KAAK;sBAAAnB,QAAA,EAAC;oBAAc;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC1ClG,cAAc,CAACyB,GAAG,CAAE4F,IAAI,iBACvB5J,OAAA;sBAAmB8J,KAAK,EAAEF,IAAK;sBAAAjB,QAAA,EAC5BiB;oBAAI,GADMA,IAAI;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAET,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzI,OAAA;gBAAK0I,SAAS,EAAC,oEAAoE;gBAAAC,QAAA,gBACjF3I,OAAA;kBAAK0I,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,gBACpC3I,OAAA;oBAAM0I,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAE1H,eAAe,CAACyC;kBAAM;oBAAA4E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,kBAC/D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNzI,OAAA;kBAAK0I,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtC3I,OAAA;oBAAM0I,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvDzI,OAAA;oBACE0I,SAAS,EAAE,wHAAwHvI,mBAAmB,CAACC,OAAO,mBAAmBD,mBAAmB,CAACC,OAAO,cAAe;oBAC3N0J,KAAK,EAAE/H,MAAO;oBACdgI,QAAQ,EAAGC,CAAC,IAAKhI,SAAS,CAACgI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAAAnB,QAAA,gBAE3C3I,OAAA;sBAAQ8J,KAAK,EAAC,MAAM;sBAAAnB,QAAA,EAAC;oBAAI;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAClCzI,OAAA;sBAAQ8J,KAAK,EAAC,SAAS;sBAAAnB,QAAA,EAAC;oBAAO;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxCzI,OAAA;sBAAQ8J,KAAK,EAAC,WAAW;sBAAAnB,QAAA,EAAC;oBAAS;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5CzI,OAAA;sBAAQ8J,KAAK,EAAC,QAAQ;sBAAAnB,QAAA,EAAC;oBAAM;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,eACTzI,OAAA;oBACE0I,SAAS,EAAE,iFAAiFvI,mBAAmB,CAACC,OAAO,mBAAmBD,mBAAmB,CAACC,OAAO,GAAI;oBACzK8J,OAAO,EAAEA,CAAA,KAAMhI,YAAY,CAACD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAE;oBAAA0G,QAAA,EAEjE1G,SAAS,KAAK,KAAK,GAAG,GAAG,GAAG;kBAAG;oBAAAqG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGbzI,OAAA,CAACR,MAAM,CAACuJ,GAAG;cACTS,QAAQ,EAAE1B,SAAU;cACpBkB,OAAO,EAAC,QAAQ;cAChBS,WAAW,EAAC,MAAM;cAClBC,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBjB,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBAEtD3I,OAAA,CAACR,MAAM,CAACuJ,GAAG;gBAACS,QAAQ,EAAEpB,IAAK;gBAACM,SAAS,EAAC,0DAA0D;gBAAAC,QAAA,eAC9F3I,OAAA;kBAAK0I,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC3I,OAAA;oBAAK0I,SAAS,EAAE,wCAAwCvI,mBAAmB,CAACC,OAAO,GAAI;oBAAAuI,QAAA,eACrF3I,OAAA,CAACF,eAAe;sBAAC4I,SAAS,EAAC;oBAAS;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC,eACNzI,OAAA;oBAAK0I,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB3I,OAAA;sBAAG0I,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAe;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACpEzI,OAAA;sBAAG0I,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC5C5H,OAAO,CAACsE,MAAM,CAAC8E,CAAC,IAAIA,CAAC,CAAC/D,MAAM,KAAK,SAAS,CAAC,CAAC1C;oBAAM;sBAAA4E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACbzI,OAAA,CAACR,MAAM,CAACuJ,GAAG;gBAACS,QAAQ,EAAEpB,IAAK;gBAACM,SAAS,EAAC,0DAA0D;gBAAAC,QAAA,eAC9F3I,OAAA;kBAAK0I,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC3I,OAAA;oBAAK0I,SAAS,EAAE,uCAAuCvI,mBAAmB,CAACK,MAAM,GAAI;oBAAAmI,QAAA,eACnF3I,OAAA,CAACP,MAAM;sBAACiJ,SAAS,EAAC;oBAAS;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,eACNzI,OAAA;oBAAK0I,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB3I,OAAA;sBAAG0I,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACrEzI,OAAA;sBAAG0I,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC5C5H,OAAO,CAACsE,MAAM,CAAC8E,CAAC,IAAIA,CAAC,CAAC/D,MAAM,KAAK,UAAU,CAAC,CAAC1C;oBAAM;sBAAA4E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACbzI,OAAA,CAACR,MAAM,CAACuJ,GAAG;gBAACS,QAAQ,EAAEpB,IAAK;gBAACM,SAAS,EAAC,0DAA0D;gBAAAC,QAAA,eAC9F3I,OAAA;kBAAK0I,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC3I,OAAA;oBAAK0I,SAAS,EAAE,0CAA2C;oBAAAC,QAAA,eACzD3I,OAAA,CAACF,eAAe;sBAAC4I,SAAS,EAAC;oBAAS;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC,eACNzI,OAAA;oBAAK0I,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB3I,OAAA;sBAAG0I,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACrEzI,OAAA;sBAAG0I,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC5C5H,OAAO,CAACsE,MAAM,CAAC8E,CAAC,IAAIA,CAAC,CAAC/D,MAAM,KAAK,UAAU,IAAI+D,CAAC,CAAC/D,MAAM,KAAK,QAAQ,CAAC,CAAC1C;oBAAM;sBAAA4E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5E,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGbzI,OAAA,CAACR,MAAM,CAACuJ,GAAG;cACTS,QAAQ,EAAE1B,SAAU;cACpBkB,OAAO,EAAC,QAAQ;cAChBS,WAAW,EAAC,MAAM;cAClBC,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBjB,SAAS,EAAC,kHAAkH;cAAAC,QAAA,eAE5H3I,OAAA;gBAAK0I,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClB3I,OAAA;kBAAI0I,SAAS,EAAE,2BAA2BvI,mBAAmB,CAACC,OAAO,0BAA2B;kBAAAuI,QAAA,gBAC9F3I,OAAA,CAACP,MAAM;oBAACiJ,SAAS,EAAE,sBAAsBvI,mBAAmB,CAACC,OAAO;kBAAI;oBAAAkI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAE7E;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLzI,OAAA;kBAAK0I,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAC9B3I,OAAA;oBAAO0I,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,gBACpD3I,OAAA;sBAAO0I,SAAS,EAAC,YAAY;sBAAAC,QAAA,eAC3B3I,OAAA;wBAAA2I,QAAA,gBACE3I,OAAA;0BAAI0I,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAO;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC3GzI,OAAA;0BAAI0I,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAO;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC3GzI,OAAA;0BAAI0I,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAS;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC7GzI,OAAA;0BAAI0I,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAM;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC1GzI,OAAA;0BAAI0I,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAU;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC9GzI,OAAA;0BAAI0I,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAAC;wBAAS;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACRzI,OAAA;sBAAO0I,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EACjD1H,eAAe,CAACyC,MAAM,KAAK,CAAC,gBAC3B1D,OAAA;wBAAA2I,QAAA,eACE3I,OAAA;0BAAIoK,OAAO,EAAC,GAAG;0BAAC1B,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,eAC/C3I,OAAA;4BAAK0I,SAAS,EAAC,2CAA2C;4BAAAC,QAAA,gBACxD3I,OAAA;8BACEqK,KAAK,EAAC,4BAA4B;8BAClC3B,SAAS,EAAC,8BAA8B;8BACxCQ,IAAI,EAAC,MAAM;8BACXC,OAAO,EAAC,WAAW;8BACnBmB,MAAM,EAAC,cAAc;8BAAA3B,QAAA,eAErB3I,OAAA;gCACEuK,aAAa,EAAC,OAAO;gCACrBC,cAAc,EAAC,OAAO;gCACtBC,WAAW,EAAE,CAAE;gCACfpB,CAAC,EAAC;8BAAiI;gCAAAf,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACpI;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC,CAAC,eACNzI,OAAA;8BAAI0I,SAAS,EAAC,mCAAmC;8BAAAC,QAAA,EAAC;4BAAgB;8BAAAL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACvEzI,OAAA;8BAAG0I,SAAS,EAAC,oBAAoB;8BAAAC,QAAA,EAC9B5H,OAAO,CAAC2C,MAAM,GAAG,CAAC,GACf,wCAAwC,GACxC;4BAA2C;8BAAA4E,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC9C,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,GAELxH,eAAe,CAAC+C,GAAG,CAAEmB,MAAM;wBAAA,IAAAuF,kBAAA;wBAAA,oBACzB1K,OAAA,CAACR,MAAM,CAACmL,EAAE;0BAERnB,QAAQ,EAAEpB,IAAK;0BACfM,SAAS,EAAC,iCAAiC;0BAC3CwB,OAAO,EAAEA,CAAA,KAAM;4BACb;4BACA,MAAMU,UAAU,GAAG;8BACjB,GAAGzF,MAAM;8BACT;8BACAtB,SAAS,EAAEsB,MAAM,CAACtB,SAAS,GACtB,OAAOsB,MAAM,CAACtB,SAAS,KAAK,QAAQ,GAClCsB,MAAM,CAACtB,SAAS,CAACA,SAAS,IAAIsB,MAAM,CAACtB,SAAS,CAACD,GAAG,IAAI,YAAY,GACnEuB,MAAM,CAACtB,SAAS,GAClB,YAAY;8BAChB;8BACA+D,YAAY,EAAEzC,MAAM,CAACyC,YAAY,GAC5B,OAAOzC,MAAM,CAACyC,YAAY,KAAK,QAAQ,GACrCzC,MAAM,CAACyC,YAAY,CAAChE,GAAG,IAAI,YAAY,GACxCuB,MAAM,CAACyC,YAAY,GACrB,IAAI;8BACR;8BACAtC,SAAS,EAAEH,MAAM,CAACG,SAAS,GACtB,OAAOH,MAAM,CAACG,SAAS,KAAK,QAAQ,GACnC;gCACE,GAAGH,MAAM,CAACG,SAAS;gCACnB;gCACAyB,QAAQ,EAAE5B,MAAM,CAACG,SAAS,CAACyB,QAAQ,IAAI,iBAAiB;gCACxD;gCACAxB,UAAU,EAAEJ,MAAM,CAACG,SAAS,CAACC,UAAU,IAAI;8BAC7C,CAAC,GACDJ,MAAM,CAACG,SAAS,GAClB,iBAAiB;8BACrB;8BACAuF,KAAK,EAAE/F,KAAK,CAACC,OAAO,CAACI,MAAM,CAAC0F,KAAK,CAAC,GAC9B1F,MAAM,CAAC0F,KAAK,CAAC7G,GAAG,CAAC,CAAC8G,IAAI,EAAEC,KAAK,KAAK;gCAChC,IAAI,CAACD,IAAI,EAAE,OAAO;kCAAEnH,IAAI,EAAE,QAAQoH,KAAK,GAAG,CAAC,EAAE;kCAAEC,SAAS,EAAE,KAAK;kCAAEC,WAAW,EAAE;gCAAG,CAAC;gCAClF,OAAO;kCACL,GAAGH,IAAI;kCACPnH,IAAI,EAAEmH,IAAI,CAACnH,IAAI,IAAImH,IAAI,CAACG,WAAW,IAAI,QAAQF,KAAK,GAAG,CAAC,EAAE;kCAC1DC,SAAS,EAAE,CAAC,CAACF,IAAI,CAACE,SAAS;kCAC3BC,WAAW,EAAEH,IAAI,CAACG,WAAW,IAAI;gCACnC,CAAC;8BACH,CAAC,CAAC,GACF,EAAE;8BACN;8BACAC,WAAW,EAAEpG,KAAK,CAACC,OAAO,CAACI,MAAM,CAAC+F,WAAW,CAAC,GAC1C/F,MAAM,CAAC+F,WAAW,CAAClH,GAAG,CAAC,CAAC8G,IAAI,EAAEC,KAAK,KAAK;gCACtC,IAAI,CAACD,IAAI,EAAE,OAAO;kCAAEG,WAAW,EAAE,QAAQF,KAAK,GAAG,CAAC,EAAE;kCAAEC,SAAS,EAAE;gCAAM,CAAC;gCACxE,OAAO;kCACL,GAAGF,IAAI;kCACPG,WAAW,EAAEH,IAAI,CAACG,WAAW,IAAI,QAAQF,KAAK,GAAG,CAAC,EAAE;kCACpDC,SAAS,EAAE,CAAC,CAACF,IAAI,CAACE;gCACpB,CAAC;8BACH,CAAC,CAAC,GACF,EAAE;8BACN;8BACAG,mBAAmB,EAAEhG,MAAM,CAACgG,mBAAmB,IAAI;4BACrD,CAAC;4BAED/J,iBAAiB,CAACwJ,UAAU,CAAC;0BAC/B,CAAE;0BAAAjC,QAAA,gBAEF3I,OAAA;4BAAI0I,SAAS,EAAC,+DAA+D;4BAAAC,QAAA,eAC3E3I,OAAA;8BAAK0I,SAAS,EAAC,mBAAmB;8BAAAC,QAAA,gBAChC3I,OAAA,CAACL,cAAc;gCAAC+I,SAAS,EAAE,iBAAiBvI,mBAAmB,CAACC,OAAO;8BAAS;gCAAAkI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,EAClFtD,MAAM,CAACyB,WAAW;4BAAA;8BAAA0B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChB;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC,eACLzI,OAAA;4BAAI0I,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAC9D,OAAOxD,MAAM,CAACG,SAAS,KAAK,QAAQ,KAAAoF,kBAAA,GAAIvF,MAAM,CAACG,SAAS,cAAAoF,kBAAA,eAAhBA,kBAAA,CAAkB3D,QAAQ,GAC/D5B,MAAM,CAACG,SAAS,CAACyB,QAAQ,GACxB,OAAO5B,MAAM,CAACG,SAAS,KAAK,QAAQ,GACjCH,MAAM,CAACG,SAAS,GAChB;0BAAkB;4BAAAgD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxB,CAAC,eACLzI,OAAA;4BAAI0I,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAAExD,MAAM,CAACO;0BAAa;4BAAA4C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC7FzI,OAAA;4BAAI0I,SAAS,EAAC,6BAA6B;4BAAAC,QAAA,eACzC3I,OAAA;8BACE0I,SAAS,EAAE,sEACTvD,MAAM,CAACiB,MAAM,KAAK,UAAU,GACxB,sBAAsBjG,mBAAmB,CAACK,MAAM,GAAG,GACnD2E,MAAM,CAACiB,MAAM,KAAK,QAAQ,IAAIjB,MAAM,CAACiB,MAAM,KAAK,UAAU,GAC1D,yBAAyB,GACzB,+BAA+B,EAClC;8BAAAuC,QAAA,EAEFxD,MAAM,CAACiB,MAAM,KAAK,QAAQ,GAAG,UAAU,GAAGjB,MAAM,CAACiB;4BAAM;8BAAAkC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACpD;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL,CAAC,eACLzI,OAAA;4BAAI0I,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAC9DxD,MAAM,CAAC2B,cAAc,gBACpB9G,OAAA;8BAAK0I,SAAS,EAAC,mBAAmB;8BAAAC,QAAA,gBAChC3I,OAAA,CAACJ,QAAQ;gCAAC8I,SAAS,EAAE,iBAAiBvI,mBAAmB,CAACE,SAAS;8BAAS;gCAAAiI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,EAC9EtD,MAAM,CAAC2B,cAAc;4BAAA;8BAAAwB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACnB,CAAC,gBAENzI,OAAA;8BAAM0I,SAAS,EAAC,eAAe;8BAAAC,QAAA,EAAC;4BAAY;8BAAAL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM;0BACnD;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC,eACLzI,OAAA;4BAAI0I,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,eAC/D3I,OAAA;8BAAK0I,SAAS,EAAC,mBAAmB;8BAAAC,QAAA,gBAChC3I,OAAA,CAACH,aAAa;gCAAC6I,SAAS,EAAE,iBAAiBvI,mBAAmB,CAACC,OAAO;8BAAS;gCAAAkI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,EACjF,IAAIrB,IAAI,CAACjC,MAAM,CAACkC,aAAa,CAAC,CAAC+D,kBAAkB,CAAC,CAAC;4BAAA;8BAAA9C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjD;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC;wBAAA,GAvGAtD,MAAM,CAACvB,GAAG;0BAAA0E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAwGN,CAAC;sBAAA,CACb;oBACF;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACZtH,cAAc,iBACbnB,OAAA,CAACR,MAAM,CAACuJ,GAAG;YACTC,OAAO,EAAE;cAAEhB,OAAO,EAAE;YAAE,CAAE;YACxBiB,OAAO,EAAE;cAAEjB,OAAO,EAAE;YAAE,CAAE;YACxBqD,IAAI,EAAE;cAAErD,OAAO,EAAE;YAAE,CAAE;YACrBE,UAAU,EAAE;cAAEqB,QAAQ,EAAE;YAAI,CAAE;YAC9Bb,SAAS,EAAC,iGAAiG;YAC3GwB,OAAO,EAAGF,CAAC,IAAK;cACd,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACsB,aAAa,EAAElK,iBAAiB,CAAC,IAAI,CAAC;YAC3D,CAAE;YAAAuH,QAAA,eAEF3I,OAAA,CAACR,MAAM,CAACuJ,GAAG;cACTC,OAAO,EAAE;gBAAEuC,KAAK,EAAE,GAAG;gBAAEvD,OAAO,EAAE,CAAC;gBAAEK,CAAC,EAAE;cAAG,CAAE;cAC3CY,OAAO,EAAE;gBAAEsC,KAAK,EAAE,CAAC;gBAAEvD,OAAO,EAAE,CAAC;gBAAEK,CAAC,EAAE;cAAE,CAAE;cACxCgD,IAAI,EAAE;gBAAEE,KAAK,EAAE,GAAG;gBAAEvD,OAAO,EAAE,CAAC;gBAAEK,CAAC,EAAE;cAAG,CAAE;cACxCH,UAAU,EAAE;gBAAE0B,IAAI,EAAE,QAAQ;gBAAE4B,SAAS,EAAE,GAAG;gBAAEC,OAAO,EAAE;cAAG,CAAE;cAC5D/C,SAAS,EAAC,gGAAgG;cAC1GwB,OAAO,EAAGF,CAAC,IAAKA,CAAC,CAAC0B,eAAe,CAAC,CAAE;cAAA/C,QAAA,gBAEpC3I,OAAA;gBAAK0I,SAAS,EAAE,0BAA0BvI,mBAAmB,CAACC,OAAO,SAASD,mBAAmB,CAACE,SAAS,kBAAmB;gBAAAsI,QAAA,eAC5H3I,OAAA;kBAAK0I,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD3I,OAAA;oBAAK0I,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChC3I,OAAA,CAACP,MAAM;sBAACiJ,SAAS,EAAC;oBAA8B;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACnDzI,OAAA;sBAAI0I,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAC;oBAAc;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACNzI,OAAA;oBAAK0I,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C3I,OAAA;sBAAM0I,SAAS,EAAE,8CACfvH,cAAc,CAACiF,MAAM,KAAK,UAAU,GAChC,6BAA6B,GAC7BjF,cAAc,CAACiF,MAAM,KAAK,QAAQ,IAAIjF,cAAc,CAACiF,MAAM,KAAK,UAAU,GAC1E,yBAAyB,GACzB,+BAA+B,EAClC;sBAAAuC,QAAA,EACAxH,cAAc,CAACiF,MAAM,KAAK,UAAU,GAAG,UAAU,GACjDjF,cAAc,CAACiF,MAAM,KAAK,QAAQ,GAAG,UAAU,GAC/CjF,cAAc,CAACiF,MAAM,KAAK,UAAU,GAAG,UAAU,GACjDjF,cAAc,CAACiF,MAAM,IAAI;oBAAS;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC,eACPzI,OAAA;sBACEkK,OAAO,EAAEA,CAAA,KAAM9I,iBAAiB,CAAC,IAAI,CAAE;sBACvCsH,SAAS,EAAE,wDAAwDvI,mBAAmB,CAACC,OAAO,gHAAiH;sBAAAuI,QAAA,eAE/M3I,OAAA;wBAAKqK,KAAK,EAAC,4BAA4B;wBAAC3B,SAAS,EAAC,SAAS;wBAACS,OAAO,EAAC,WAAW;wBAACD,IAAI,EAAC,cAAc;wBAAAP,QAAA,eACjG3I,OAAA;0BAAMoJ,QAAQ,EAAC,SAAS;0BAACC,CAAC,EAAC,oMAAoM;0BAACC,QAAQ,EAAC;wBAAS;0BAAAhB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClP;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENzI,OAAA;gBAAK0I,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/C3I,OAAA;kBAAK0I,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,gBACzD3I,OAAA;oBAAA2I,QAAA,gBACE3I,OAAA;sBAAI0I,SAAS,EAAE,+BAA+BvI,mBAAmB,CAACC,OAAO,0BAA2B;sBAAAuI,QAAA,gBAClG3I,OAAA,CAACL,cAAc;wBAAC+I,SAAS,EAAE,cAAcvI,mBAAmB,CAACC,OAAO;sBAAI;wBAAAkI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,wBAC7E;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLzI,OAAA;sBAAK0I,SAAS,EAAE,sEAAuE;sBAAAC,QAAA,gBACrF3I,OAAA;wBAAK0I,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBAChC3I,OAAA;0BAAM0I,SAAS,EAAC,wCAAwC;0BAAAC,QAAA,EAAC;wBAAK;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACrEzI,OAAA;0BAAM0I,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,EAAExH,cAAc,CAACyF;wBAAW;0BAAA0B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5E,CAAC,eACNzI,OAAA;wBAAK0I,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBAChC3I,OAAA;0BAAM0I,SAAS,EAAC,wCAAwC;0BAAAC,QAAA,EAAC;wBAAG;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACnEzI,OAAA;0BAAM0I,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAC5B,OAAOxH,cAAc,CAAC0C,SAAS,KAAK,QAAQ,GACxC1C,cAAc,CAAC0C,SAAS,CAACA,SAAS,IAAI1C,cAAc,CAAC0C,SAAS,CAACD,GAAG,IAAI,KAAK,GAC3EzC,cAAc,CAAC0C,SAAS,IAAI;wBAAM;0BAAAyE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,EACL,CAAC,MAAM;wBACN,MAAMkD,cAAc,GAAGpE,iBAAiB,CAACpG,cAAc,CAAC0C,SAAS,CAAC;wBAClE,OAAO8H,cAAc,gBACnB3L,OAAA;0BAAK0I,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,gBAChC3I,OAAA;4BAAM0I,SAAS,EAAC,wCAAwC;4BAAAC,QAAA,EAAC;0BAAW;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC3EzI,OAAA;4BAAM0I,SAAS,EAAC,eAAe;4BAAAC,QAAA,EAAEgD,cAAc,CAAC9I,UAAU,IAAI;0BAAK;4BAAAyF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxE,CAAC,GACJ,IAAI;sBACV,CAAC,EAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENzI,OAAA;oBAAA2I,QAAA,gBACE3I,OAAA;sBAAI0I,SAAS,EAAE,+BAA+BvI,mBAAmB,CAACC,OAAO,0BAA2B;sBAAAuI,QAAA,gBAClG3I,OAAA,CAACJ,QAAQ;wBAAC8I,SAAS,EAAE,cAAcvI,mBAAmB,CAACC,OAAO;sBAAI;wBAAAkI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,2BACvE;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLzI,OAAA;sBAAK0I,SAAS,EAAE,sEAAuE;sBAAAC,QAAA,EACpFxH,cAAc,CAAC2F,cAAc,gBAC5B9G,OAAA,CAAAE,SAAA;wBAAAyI,QAAA,gBACE3I,OAAA;0BAAK0I,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,gBAChC3I,OAAA;4BAAM0I,SAAS,EAAC,wCAAwC;4BAAAC,QAAA,EAAC;0BAAK;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACrEzI,OAAA;4BAAM0I,SAAS,EAAC,2BAA2B;4BAAAC,QAAA,EAAExH,cAAc,CAAC2F;0BAAc;4BAAAwB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/E,CAAC,eACNzI,OAAA;0BAAK0I,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,gBAChC3I,OAAA;4BAAM0I,SAAS,EAAC,wCAAwC;4BAAAC,QAAA,EAAC;0BAAS;4BAAAL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACzEzI,OAAA;4BAAM0I,SAAS,EAAC,eAAe;4BAAAC,QAAA,EAAExH,cAAc,CAACyK,YAAY,GAC1D,IAAIxE,IAAI,CAACjG,cAAc,CAACyK,YAAY,CAAC,CAACC,cAAc,CAAC,CAAC,GAAG;0BAAkB;4BAAAvD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClF,CAAC,EACL,CAAC,MAAM;0BACN,MAAMqD,iBAAiB,GAAGnE,oBAAoB,CAACxG,cAAc,CAACyG,YAAY,CAAC;0BAC3E,OAAOkE,iBAAiB,gBACtB9L,OAAA;4BAAK0I,SAAS,EAAC,mBAAmB;4BAAAC,QAAA,gBAChC3I,OAAA;8BAAM0I,SAAS,EAAC,wCAAwC;8BAAAC,QAAA,EAAC;4BAAW;8BAAAL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eAC3EzI,OAAA;8BAAM0I,SAAS,EAAC,eAAe;8BAAAC,QAAA,EAAEmD,iBAAiB,CAACjJ,UAAU,IAAI;4BAAK;8BAAAyF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3E,CAAC,GACJ,IAAI;wBACV,CAAC,EAAE,CAAC;sBAAA,eACJ,CAAC,gBAEHzI,OAAA;wBAAG0I,SAAS,EAAC,sBAAsB;wBAAAC,QAAA,EAAC;sBAA0B;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAClE;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNzI,OAAA;kBAAK0I,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnB3I,OAAA;oBAAI0I,SAAS,EAAE,+BAA+BvI,mBAAmB,CAACC,OAAO,0BAA2B;oBAAAuI,QAAA,gBAClG3I,OAAA,CAACJ,QAAQ;sBAAC8I,SAAS,EAAE,cAAcvI,mBAAmB,CAACC,OAAO;oBAAI;sBAAAkI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,wBACvE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLzI,OAAA;oBAAK0I,SAAS,EAAE,4DAA6D;oBAAAC,QAAA,eAC3E3I,OAAA;sBAAK0I,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,gBACpD3I,OAAA;wBAAA2I,QAAA,gBACE3I,OAAA;0BAAI0I,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAC;wBAAI;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC3DzI,OAAA;0BAAG0I,SAAS,EAAC,4BAA4B;0BAAAC,QAAA,EACtC,OAAOxH,cAAc,CAACmE,SAAS,KAAK,QAAQ,KAAA3E,qBAAA,GAAIQ,cAAc,CAACmE,SAAS,cAAA3E,qBAAA,eAAxBA,qBAAA,CAA0BoG,QAAQ,GAC/E5F,cAAc,CAACmE,SAAS,CAACyB,QAAQ,GAChC,OAAO5F,cAAc,CAACmE,SAAS,KAAK,QAAQ,GACzCnE,cAAc,CAACmE,SAAS,GACxB;wBAAU;0BAAAgD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC,eACNzI,OAAA;wBAAA2I,QAAA,gBACE3I,OAAA;0BAAI0I,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAC;wBAAW;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAClEzI,OAAA;0BAAG0I,SAAS,EAAC,4BAA4B;0BAAAC,QAAA,EACtC,OAAOxH,cAAc,CAACmE,SAAS,KAAK,QAAQ,KAAA1E,sBAAA,GAAIO,cAAc,CAACmE,SAAS,cAAA1E,sBAAA,eAAxBA,sBAAA,CAA0B2E,UAAU,GACjFpE,cAAc,CAACmE,SAAS,CAACC,UAAU,GACnC;wBAAK;0BAAA+C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACR,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNzI,OAAA;kBAAK0I,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnB3I,OAAA;oBAAI0I,SAAS,EAAE,+BAA+BvI,mBAAmB,CAACC,OAAO,0BAA2B;oBAAAuI,QAAA,gBAClG3I,OAAA,CAACF,eAAe;sBAAC4I,SAAS,EAAE,cAAcvI,mBAAmB,CAACC,OAAO;oBAAI;sBAAAkI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,sBAC9E;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLzI,OAAA;oBAAK0I,SAAS,EAAE,4DAA6D;oBAAAC,QAAA,eAC3E3I,OAAA;sBAAK0I,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,gBACpD3I,OAAA;wBAAA2I,QAAA,gBACE3I,OAAA;0BAAI0I,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAC;wBAAc;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACrEzI,OAAA;0BAAG0I,SAAS,EAAC,wCAAwC;0BAAAC,QAAA,EAAExH,cAAc,CAACuE;wBAAa;0BAAA4C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrF,CAAC,eACNzI,OAAA;wBAAA2I,QAAA,gBACE3I,OAAA;0BAAI0I,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAC;wBAAM;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC7DzI,OAAA;0BAAG0I,SAAS,EAAC,cAAc;0BAAAC,QAAA,eACzB3I,OAAA;4BAAM0I,SAAS,EAAE,sEACfvH,cAAc,CAACiF,MAAM,KAAK,UAAU,GAChC,6BAA6B,GAC7BjF,cAAc,CAACiF,MAAM,KAAK,QAAQ,IAAIjF,cAAc,CAACiF,MAAM,KAAK,UAAU,GAC1E,yBAAyB,GACzB,+BAA+B,EAClC;4BAAAuC,QAAA,EACAxH,cAAc,CAACiF,MAAM,KAAK,QAAQ,GAAG,UAAU,GAC/CjF,cAAc,CAACiF,MAAM,KAAK,UAAU,GAAG,UAAU,GACjDjF,cAAc,CAACiF,MAAM,KAAK,SAAS,GAAG,SAAS,GAC/CjF,cAAc,CAACiF;0BAAM;4BAAAkC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENzI,OAAA;kBAAK0I,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnB3I,OAAA;oBAAI0I,SAAS,EAAE,+BAA+BvI,mBAAmB,CAACC,OAAO,0BAA2B;oBAAAuI,QAAA,gBAClG3I,OAAA,CAACP,MAAM;sBAACiJ,SAAS,EAAE,cAAcvI,mBAAmB,CAACC,OAAO;oBAAI;sBAAAkI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,YACrE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLzI,OAAA;oBAAK0I,SAAS,EAAE,4DAA6D;oBAAAC,QAAA,eAC3E3I,OAAA;sBAAK0I,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,gBACpD3I,OAAA;wBAAK0I,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChD3I,OAAA;0BAAG0I,SAAS,EAAC,gCAAgC;0BAAAC,QAAA,EAAC;wBAAiB;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACnEzI,OAAA;0BAAK0I,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,GAC/B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC3E,GAAG,CAAE+H,IAAI,iBACxB/L,OAAA,CAACP,MAAM;4BAELiJ,SAAS,EAAE,WACTqD,IAAI,IAAI5K,cAAc,CAAC6K,gBAAgB,GACnC,iBAAiB,GACjB,eAAe;0BAClB,GALED,IAAI;4BAAAzD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAMV,CACF,CAAC,eACFzI,OAAA;4BAAM0I,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,GAAExH,cAAc,CAAC6K,gBAAgB,EAAC,IAAE;0BAAA;4BAAA1D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtF,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNzI,OAAA;wBAAK0I,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChD3I,OAAA;0BAAG0I,SAAS,EAAC,gCAAgC;0BAAAC,QAAA,EAAC;wBAAmB;0BAAAL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACrEzI,OAAA;0BAAK0I,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,GAC/B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC3E,GAAG,CAAE+H,IAAI,iBACxB/L,OAAA,CAACP,MAAM;4BAELiJ,SAAS,EAAE,WACTqD,IAAI,IAAI5K,cAAc,CAAC8K,kBAAkB,GACrC,iBAAiB,GACjB,eAAe;0BAClB,GALEF,IAAI;4BAAAzD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAMV,CACF,CAAC,eACFzI,OAAA;4BAAM0I,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,GAAExH,cAAc,CAAC8K,kBAAkB,EAAC,IAAE;0BAAA;4BAAA3D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxF,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNzI,OAAA;kBAAK0I,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnB3I,OAAA;oBAAI0I,SAAS,EAAE,+BAA+BvI,mBAAmB,CAACC,OAAO,0BAA2B;oBAAAuI,QAAA,gBAClG3I,OAAA;sBAAKqK,KAAK,EAAC,4BAA4B;sBAAC3B,SAAS,EAAE,sBAAsBvI,mBAAmB,CAACC,OAAO,GAAI;sBAAC+I,OAAO,EAAC,WAAW;sBAACD,IAAI,EAAC,cAAc;sBAAAP,QAAA,eAC9I3I,OAAA;wBAAMoJ,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,4JAA4J;wBAACC,QAAQ,EAAC;sBAAS;wBAAAhB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1M,CAAC,YAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLzI,OAAA;oBAAK0I,SAAS,EAAE,sEAAuE;oBAAAC,QAAA,gBACrF3I,OAAA;sBAAK0I,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,gBAChD3I,OAAA;wBAAG0I,SAAS,EAAC,kDAAkD;wBAAAC,QAAA,gBAC7D3I,OAAA,CAACL,cAAc;0BAAC+I,SAAS,EAAE,sBAAsBvI,mBAAmB,CAACC,OAAO;wBAAI;0BAAAkI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,oBAErF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACJzI,OAAA;wBAAG0I,SAAS,EAAC,qEAAqE;wBAAAC,QAAA,EAC/ExH,cAAc,CAAC+K,OAAO,IAAI;sBAAqB;wBAAA5D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC,eACNzI,OAAA;sBAAK0I,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,gBAChD3I,OAAA;wBAAG0I,SAAS,EAAC,kDAAkD;wBAAAC,QAAA,gBAC7D3I,OAAA,CAACJ,QAAQ;0BAAC8I,SAAS,EAAE,sBAAsBvI,mBAAmB,CAACC,OAAO;wBAAI;0BAAAkI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,oBAE/E;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACJzI,OAAA;wBAAG0I,SAAS,EAAC,qEAAqE;wBAAAC,QAAA,EAC/ExH,cAAc,CAACgL,IAAI,IAAI;sBAAkB;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC,EAGLtH,cAAc,CAACgK,mBAAmB,iBACjCnL,OAAA;sBAAK0I,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,gBAChD3I,OAAA;wBAAG0I,SAAS,EAAC,kDAAkD;wBAAAC,QAAA,gBAC7D3I,OAAA,CAACJ,QAAQ;0BAAC8I,SAAS,EAAE,sBAAsBvI,mBAAmB,CAACC,OAAO;wBAAI;0BAAAkI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,yBAE/E;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACJzI,OAAA;wBAAK0I,SAAS,EAAC,2EAA2E;wBAAAC,QAAA,EACvFxH,cAAc,CAACgK,mBAAmB,CAACiB,UAAU,CAAC,YAAY,CAAC,gBAC1DpM,OAAA;0BACEqM,GAAG,EAAElL,cAAc,CAACgK,mBAAoB;0BACxCmB,GAAG,EAAC,sBAAsB;0BAC1B5D,SAAS,EAAC;wBAAU;0BAAAJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrB,CAAC,gBAEFzI,OAAA;0BAAG0I,SAAS,EAAC,sCAAsC;0BAAAC,QAAA,EAChDxH,cAAc,CAACgK;wBAAmB;0BAAA7C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC;sBACJ;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNzI,OAAA;kBAAK0I,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnB3I,OAAA;oBAAI0I,SAAS,EAAE,+BAA+BvI,mBAAmB,CAACC,OAAO,0BAA2B;oBAAAuI,QAAA,gBAClG3I,OAAA,CAACF,eAAe;sBAAC4I,SAAS,EAAE,cAAcvI,mBAAmB,CAACC,OAAO;oBAAI;sBAAAkI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,oBAC9E;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLzI,OAAA;oBAAK0I,SAAS,EAAE,4DAA6D;oBAAAC,QAAA,EAC1ExH,cAAc,CAAC+J,WAAW,IAAIpG,KAAK,CAACC,OAAO,CAAC5D,cAAc,CAAC+J,WAAW,CAAC,IAAI/J,cAAc,CAAC+J,WAAW,CAACxH,MAAM,GAAG,CAAC,gBAC/G1D,OAAA;sBAAK0I,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,eAClD3I,OAAA;wBAAK0I,SAAS,EAAC,0BAA0B;wBAAAC,QAAA,EACtCxH,cAAc,CAAC+J,WAAW,CAAClH,GAAG,CAAC,CAAC8G,IAAI,EAAEC,KAAK,KAAK;0BAC/C;0BACA,MAAMwB,QAAQ,GAAGzB,IAAI,IAAI;4BAAEE,SAAS,EAAE;0BAAM,CAAC;0BAC7C,oBACEhL,OAAA;4BAEE0I,SAAS,EAAE,yCACT6D,QAAQ,CAACvB,SAAS,GAAG,aAAa,GAAG,UAAU,EAC9C;4BAAArC,QAAA,gBAEH3I,OAAA;8BAAK0I,SAAS,EAAC,mBAAmB;8BAAAC,QAAA,gBAChC3I,OAAA;gCAAM0I,SAAS,EAAE,2CAA2CvI,mBAAmB,CAACC,OAAO,aAAc;gCAAAuI,QAAA,GAAEoC,KAAK,GAAG,CAAC,EAAC,GAAC;8BAAA;gCAAAzC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CAAC,eACzHzI,OAAA;gCAAM0I,SAAS,EAAC,mCAAmC;gCAAAC,QAAA,EAChD4D,QAAQ,CAACtB,WAAW,IAAIsB,QAAQ,CAAC5I,IAAI,IAAI,QAAQoH,KAAK,GAAG,CAAC;8BAAE;gCAAAzC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACzD,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACNzI,OAAA;8BAAA2I,QAAA,EACG4D,QAAQ,CAACvB,SAAS,gBACjBhL,OAAA;gCAAM0I,SAAS,EAAC,qGAAqG;gCAAAC,QAAA,gBACnH3I,OAAA;kCAAK0I,SAAS,EAAC,6BAA6B;kCAACQ,IAAI,EAAC,cAAc;kCAACC,OAAO,EAAC,WAAW;kCAAAR,QAAA,eAClF3I,OAAA;oCAAMoJ,QAAQ,EAAC,SAAS;oCAACC,CAAC,EAAC,uIAAuI;oCAACC,QAAQ,EAAC;kCAAS;oCAAAhB,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACrL,CAAC,aAER;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CAAC,gBAEPzI,OAAA;gCAAM0I,SAAS,EAAC,mGAAmG;gCAAAC,QAAA,gBACjH3I,OAAA;kCAAK0I,SAAS,EAAC,4BAA4B;kCAACQ,IAAI,EAAC,cAAc;kCAACC,OAAO,EAAC,WAAW;kCAAAR,QAAA,eACjF3I,OAAA;oCAAMoJ,QAAQ,EAAC,SAAS;oCAACC,CAAC,EAAC,iFAAiF;oCAACC,QAAQ,EAAC;kCAAS;oCAAAhB,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC/H,CAAC,iBAER;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM;4BACP;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE,CAAC;0BAAA,GA3BDsC,KAAK;4BAAAzC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OA4BP,CAAC;wBAEV,CAAC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,GACJtH,cAAc,CAAC0J,KAAK,IAAI/F,KAAK,CAACC,OAAO,CAAC5D,cAAc,CAAC0J,KAAK,CAAC,IAAI1J,cAAc,CAAC0J,KAAK,CAACnH,MAAM,GAAG,CAAC,gBAChG1D,OAAA;sBAAK0I,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,eAClD3I,OAAA;wBAAK0I,SAAS,EAAC,0BAA0B;wBAAAC,QAAA,EACtCxH,cAAc,CAAC0J,KAAK,CAAC7G,GAAG,CAAC,CAAC8G,IAAI,EAAEC,KAAK,KAAK;0BACzC;0BACA,MAAMwB,QAAQ,GAAGzB,IAAI,IAAI;4BAAEE,SAAS,EAAE;0BAAM,CAAC;0BAC7C,oBACEhL,OAAA;4BAEE0I,SAAS,EAAE,yCACT6D,QAAQ,CAACvB,SAAS,GAAG,aAAa,GAAG,UAAU,EAC9C;4BAAArC,QAAA,gBAEH3I,OAAA;8BAAK0I,SAAS,EAAC,mBAAmB;8BAAAC,QAAA,gBAChC3I,OAAA;gCAAM0I,SAAS,EAAE,2CAA2CvI,mBAAmB,CAACC,OAAO,aAAc;gCAAAuI,QAAA,GAAEoC,KAAK,GAAG,CAAC,EAAC,GAAC;8BAAA;gCAAAzC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CAAC,eACzHzI,OAAA;gCAAM0I,SAAS,EAAC,mCAAmC;gCAAAC,QAAA,EAChD4D,QAAQ,CAACtB,WAAW,IAAIsB,QAAQ,CAAC5I,IAAI,IAAI,QAAQoH,KAAK,GAAG,CAAC;8BAAE;gCAAAzC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACzD,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACNzI,OAAA;8BAAA2I,QAAA,EACG4D,QAAQ,CAACvB,SAAS,gBACjBhL,OAAA;gCAAM0I,SAAS,EAAC,qGAAqG;gCAAAC,QAAA,gBACnH3I,OAAA;kCAAK0I,SAAS,EAAC,6BAA6B;kCAACQ,IAAI,EAAC,cAAc;kCAACC,OAAO,EAAC,WAAW;kCAAAR,QAAA,eAClF3I,OAAA;oCAAMoJ,QAAQ,EAAC,SAAS;oCAACC,CAAC,EAAC,uIAAuI;oCAACC,QAAQ,EAAC;kCAAS;oCAAAhB,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACrL,CAAC,aAER;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CAAC,gBAEPzI,OAAA;gCAAM0I,SAAS,EAAC,mGAAmG;gCAAAC,QAAA,gBACjH3I,OAAA;kCAAK0I,SAAS,EAAC,4BAA4B;kCAACQ,IAAI,EAAC,cAAc;kCAACC,OAAO,EAAC,WAAW;kCAAAR,QAAA,eACjF3I,OAAA;oCAAMoJ,QAAQ,EAAC,SAAS;oCAACC,CAAC,EAAC,iFAAiF;oCAACC,QAAQ,EAAC;kCAAS;oCAAAhB,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC/H,CAAC,iBAER;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM;4BACP;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE,CAAC;0BAAA,GA3BDsC,KAAK;4BAAAzC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OA4BP,CAAC;wBAEV,CAAC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,gBAENzI,OAAA;sBAAG0I,SAAS,EAAC,2DAA2D;sBAAAC,QAAA,EAAC;oBAA4B;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBACzG;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENzI,OAAA;gBAAK0I,SAAS,EAAC,2EAA2E;gBAAAC,QAAA,gBACxF3I,OAAA;kBAAK0I,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,gBACpC3I,OAAA;oBAAM0I,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAU;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,KAAC,EAAC,IAAIrB,IAAI,CAACjG,cAAc,CAACkG,aAAa,CAAC,CAACwE,cAAc,CAAC,CAAC,EACvG1K,cAAc,CAACyK,YAAY,iBAC1B5L,OAAA;oBAAM0I,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACpB3I,OAAA;sBAAM0I,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAS;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,IAAIrB,IAAI,CAACjG,cAAc,CAACyK,YAAY,CAAC,CAACC,cAAc,CAAC,CAAC;kBAAA;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClG,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNzI,OAAA,CAACR,MAAM,CAACgN,MAAM;kBACZC,UAAU,EAAE;oBAAElB,KAAK,EAAE;kBAAK,CAAE;kBAC5BmB,QAAQ,EAAE;oBAAEnB,KAAK,EAAE;kBAAK,CAAE;kBAC1BrB,OAAO,EAAEA,CAAA,KAAM9I,iBAAiB,CAAC,IAAI,CAAE;kBACvCsH,SAAS,EAAE,mBAAmBvI,mBAAmB,CAACC,OAAO,qGAAsG;kBAAAuI,QAAA,gBAE/J3I,OAAA;oBAAKqK,KAAK,EAAC,4BAA4B;oBAAC3B,SAAS,EAAC,cAAc;oBAACS,OAAO,EAAC,WAAW;oBAACD,IAAI,EAAC,cAAc;oBAAAP,QAAA,eACtG3I,OAAA;sBAAMoJ,QAAQ,EAAC,SAAS;sBAACC,CAAC,EAAC,oMAAoM;sBAACC,QAAQ,EAAC;oBAAS;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClP,CAAC,SAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/H,EAAA,CA79BID,OAAO;EAAA,QAeMvB,WAAW,EACJE,OAAO;AAAA;AAAAuN,EAAA,GAhB3BlM,OAAO;AA+9Bb,eAAeA,OAAO;AAAC,IAAAkM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}