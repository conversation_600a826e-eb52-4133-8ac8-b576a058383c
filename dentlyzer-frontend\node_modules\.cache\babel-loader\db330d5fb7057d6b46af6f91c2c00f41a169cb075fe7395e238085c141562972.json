{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\student\\\\Patients.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useRef } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\nimport Navbar from './Navbar';\nimport Sidebar from './Sidebar';\nimport Loader from '../components/Loader';\nimport { motion } from 'framer-motion';\nimport { FaUser, FaUserPlus, FaSearch, FaEdit, FaTrash, FaFileMedical, FaProcedures, FaPills, FaUpload, FaXRay, FaImages } from 'react-icons/fa';\nimport { RiAiGenerate } from 'react-icons/ri';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst chronicDiseases = ['Diabetes Mellitus', 'Hypertension', 'Cardiovascular Disease', 'Coagulopathy / Bleeding Disorders', 'Thyroid Disorders', 'Pregnancy', 'Lactation', 'Hepatic Diseases', 'Renal Diseases'];\nconst Patients = () => {\n  _s();\n  var _selectedPatient$medi, _selectedPatient$medi2, _selectedPatient$medi3, _selectedPatient$medi4, _selectedPatient$medi5;\n  const {\n    user,\n    token\n  } = useAuth();\n  const navigate = useNavigate();\n  const [patients, setPatients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedPatient, setSelectedPatient] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [uploadType, setUploadType] = useState('');\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [notification, setNotification] = useState({\n    show: false,\n    message: '',\n    type: 'success'\n  });\n  const fileInputRef = useRef(null);\n  const [formData, setFormData] = useState({\n    nationalId: '',\n    fullName: '',\n    phoneNumber: '',\n    gender: '',\n    age: '',\n    address: '',\n    occupation: '',\n    medicalInfo: {\n      chronicDiseases: [],\n      recentSurgicalProcedures: '',\n      currentMedications: '',\n      chiefComplaint: ''\n    }\n  });\n  useEffect(() => {\n    const fetchPatients = async () => {\n      if (!user || !token) {\n        setError('Please log in to view patients.');\n        setLoading(false);\n        return;\n      }\n      try {\n        const config = {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        };\n        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/patients`, config);\n        if (Array.isArray(response.data)) {\n          const validPatients = response.data.filter(patient => patient && typeof patient === 'object' && patient.nationalId && patient.fullName);\n          setPatients(validPatients);\n        } else {\n          setError('Invalid patient data received from server');\n          setPatients([]);\n        }\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response3$data;\n        console.error('Fetch error:', (_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status, (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data);\n        setError(((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || 'Failed to load patients');\n        setPatients([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchPatients();\n  }, [user, token]);\n  useEffect(() => {\n    // Only clear selectedPatient when all modals are closed\n    if (!showModal && !showDeleteModal && !showUploadModal) {\n      setSelectedPatient(null);\n    }\n  }, [showModal, showDeleteModal, showUploadModal]);\n  const handleUploadClick = (e, patient, type) => {\n    // Stop event propagation to prevent the row click handler from firing\n    e.stopPropagation();\n    setSelectedPatient(patient);\n    setUploadType(type);\n    setShowUploadModal(true);\n  };\n  const handleFileUpload = async e => {\n    const files = e.target.files;\n    if (!files || files.length === 0) return;\n    const formData = new FormData();\n    const fieldName = uploadType === 'xray' ? 'xrays' : 'galleryImages';\n    Array.from(files).forEach(file => formData.append(fieldName, file));\n    try {\n      const endpoint = uploadType === 'xray' ? `${process.env.REACT_APP_API_URL}/api/patients/${selectedPatient.nationalId}/xrays` : `${process.env.REACT_APP_API_URL}/api/patients/${selectedPatient.nationalId}/gallery`;\n      const response = await axios.post(endpoint, formData, {\n        headers: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      if (response.data) {\n        // Show success notification\n        setNotification({\n          show: true,\n          message: `${uploadType === 'xray' ? 'X-rays' : 'Gallery images'} uploaded successfully!`,\n          type: 'success'\n        });\n\n        // Hide notification after 3 seconds\n        setTimeout(() => {\n          setNotification({\n            show: false,\n            message: '',\n            type: 'success'\n          });\n        }, 3000);\n        setShowUploadModal(false);\n      }\n    } catch (err) {\n      var _err$response4, _err$response4$data;\n      console.error('Upload error:', err);\n      setError(((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.message) || `Failed to upload ${uploadType === 'xray' ? 'X-rays' : 'gallery images'}`);\n    }\n  };\n  const handleSearch = e => setSearchTerm(e.target.value);\n  const filteredPatients = patients.filter(patient => {\n    if (!patient || typeof patient !== 'object') return false;\n    const fullName = patient.fullName || '';\n    const nationalId = patient.nationalId || '';\n    const phoneNumber = patient.phoneNumber || '';\n    return fullName.toLowerCase().includes(searchTerm.toLowerCase()) || nationalId.includes(searchTerm) || phoneNumber.includes(searchTerm);\n  });\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    if (name === 'chiefComplaint') {\n      setFormData(prev => ({\n        ...prev,\n        medicalInfo: {\n          ...prev.medicalInfo,\n          chiefComplaint: value\n        }\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n  };\n  const handleMedicalInfoChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      medicalInfo: {\n        ...prev.medicalInfo,\n        [name]: value\n      }\n    }));\n  };\n  const handleChronicDiseaseChange = disease => {\n    setFormData(prev => {\n      const diseases = prev.medicalInfo.chronicDiseases.includes(disease) ? prev.medicalInfo.chronicDiseases.filter(d => d !== disease) : [...prev.medicalInfo.chronicDiseases, disease];\n      return {\n        ...prev,\n        medicalInfo: {\n          ...prev.medicalInfo,\n          chronicDiseases: diseases\n        }\n      };\n    });\n  };\n  const handleViewPatient = patient => {\n    // Go directly to patient history instead of showing the popup\n    navigate(`/patientprofile/${patient.nationalId}/history`);\n  };\n  const handleAddPatient = () => {\n    setFormData({\n      nationalId: '',\n      fullName: '',\n      phoneNumber: '',\n      gender: '',\n      age: '',\n      address: '',\n      occupation: '',\n      medicalInfo: {\n        chronicDiseases: [],\n        recentSurgicalProcedures: '',\n        currentMedications: '',\n        chiefComplaint: ''\n      }\n    });\n    setSelectedPatient(null);\n    setShowModal(true);\n  };\n  const handleEditPatient = patient => {\n    setFormData({\n      nationalId: patient.nationalId,\n      fullName: patient.fullName,\n      phoneNumber: patient.phoneNumber,\n      gender: patient.gender,\n      age: patient.age,\n      address: patient.address || '',\n      occupation: patient.occupation || '',\n      medicalInfo: patient.medicalInfo || {\n        chronicDiseases: [],\n        recentSurgicalProcedures: '',\n        currentMedications: '',\n        chiefComplaint: ''\n      }\n    });\n    setSelectedPatient(patient);\n    setShowModal(true);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n      const patientData = {\n        ...formData,\n        drId: user.studentId // Use studentId instead of id\n      };\n      console.log('Creating/updating patient with drId:', user.studentId);\n      if (selectedPatient) {\n        const response = await axios.put(`${process.env.REACT_APP_API_URL}/api/patients/${selectedPatient.nationalId}`, patientData, config);\n        if (response.data && response.data.patient) {\n          setPatients(patients.map(p => p.nationalId === selectedPatient.nationalId ? response.data.patient : p));\n        } else {\n          throw new Error('Invalid patient data received from server');\n        }\n        setShowModal(false);\n      } else {\n        const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/patients`, patientData, config);\n        if (response.data && response.data.patient) {\n          setPatients(prevPatients => [...prevPatients, response.data.patient]);\n          setShowModal(false);\n          setTimeout(() => {\n            navigate(`/patientprofile/${response.data.patient.nationalId}`);\n          }, 0);\n        } else {\n          throw new Error('Invalid patient data received from server');\n        }\n      }\n    } catch (err) {\n      var _err$response5, _err$response6, _err$response7, _err$response7$data;\n      console.error('Submit error:', (_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : _err$response5.status, (_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : _err$response6.data);\n      setError(((_err$response7 = err.response) === null || _err$response7 === void 0 ? void 0 : (_err$response7$data = _err$response7.data) === null || _err$response7$data === void 0 ? void 0 : _err$response7$data.message) || 'Failed to save patient');\n    }\n  };\n  const handleDeletePatient = async () => {\n    try {\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n      await axios.delete(`${process.env.REACT_APP_API_URL}/api/patients/${selectedPatient.nationalId}`, config);\n      setPatients(patients.filter(p => p.nationalId !== selectedPatient.nationalId));\n      setShowDeleteModal(false);\n      setSelectedPatient(null);\n    } catch (err) {\n      var _err$response8, _err$response9, _err$response10, _err$response10$data;\n      console.error('Delete error:', (_err$response8 = err.response) === null || _err$response8 === void 0 ? void 0 : _err$response8.status, (_err$response9 = err.response) === null || _err$response9 === void 0 ? void 0 : _err$response9.data);\n      setError(((_err$response10 = err.response) === null || _err$response10 === void 0 ? void 0 : (_err$response10$data = _err$response10.data) === null || _err$response10$data === void 0 ? void 0 : _err$response10$data.message) || 'Failed to delete patient');\n    }\n  };\n\n  // Animation variants\n  const container = {\n    hidden: {\n      opacity: 0\n    },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const item = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    show: {\n      opacity: 1,\n      y: 0\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 12\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n        isOpen: sidebarOpen,\n        setIsOpen: setSidebarOpen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex flex-col overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(Navbar, {\n          toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-blue-50 to-white flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              scale: 0.9,\n              opacity: 0\n            },\n            animate: {\n              scale: 1,\n              opacity: 1\n            },\n            className: \"text-center max-w-md bg-white p-8 rounded-xl shadow-sm border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-red-500 mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-12 w-12 mx-auto\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-bold text-gray-900 mb-2\",\n              children: \"Error\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mb-6\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => window.location.reload(),\n              className: \"px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-full hover:from-blue-600 hover:to-blue-800 font-medium shadow-md\",\n              children: \"Try Again\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\",\n                  children: \"Patient Management\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-[#333333]\",\n                  children: \"Manage your patient records\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col md:flex-row items-center gap-4 w-full md:w-auto\",\n                children: /*#__PURE__*/_jsxDEV(motion.div, {\n                  whileHover: {\n                    scale: 1.02\n                  },\n                  className: \"relative w-full md:w-64\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                    children: /*#__PURE__*/_jsxDEV(FaSearch, {\n                      className: \"h-5 w-5 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 381,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    placeholder: \"Search patients...\",\n                    value: searchTerm,\n                    onChange: handleSearch,\n                    className: \"block w-full pl-10 pr-3 py-2.5 border border-gray-200 rounded-full bg-white shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#20B2AA] focus:bg-white sm:text-sm transition-all duration-200\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: container,\n              initial: \"hidden\",\n              animate: \"show\",\n              className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-4 md:p-6\",\n              children: filteredPatients.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"overflow-x-auto\",\n                children: /*#__PURE__*/_jsxDEV(\"table\", {\n                  className: \"min-w-full divide-y divide-gray-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                    className: \"bg-[rgba(0,119,182,0.05)]\",\n                    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider\",\n                        children: \"Patient\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 406,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider\",\n                        children: \"National ID\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 407,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider\",\n                        children: \"Phone\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 408,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider\",\n                        children: \"Gender\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 409,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider\",\n                        children: \"Age\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 410,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                        className: \"px-6 py-3 text-right text-xs font-medium text-[#333333] uppercase tracking-wider\",\n                        children: \"Actions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 411,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 405,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                    className: \"bg-white divide-y divide-gray-200\",\n                    children: filteredPatients.map(patient => /*#__PURE__*/_jsxDEV(motion.tr, {\n                      variants: item,\n                      className: \"hover:bg-[rgba(0,119,182,0.05)] cursor-pointer\",\n                      onClick: () => handleViewPatient(patient),\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-6 py-4 whitespace-nowrap\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-shrink-0 h-10 w-10 rounded-full bg-[rgba(0,119,182,0.1)] flex items-center justify-center text-[#0077B6]\",\n                            children: /*#__PURE__*/_jsxDEV(FaUser, {\n                              className: \"h-5 w-5\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 425,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 424,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"ml-4\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-sm font-medium text-[#333333]\",\n                              children: patient.fullName\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 428,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 427,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 423,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 422,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                        children: patient.nationalId\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 432,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                        children: patient.phoneNumber\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 433,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                        children: patient.gender\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 434,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                        children: patient.age\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 435,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: e => {\n                            e.stopPropagation();\n                            handleEditPatient(patient);\n                          },\n                          className: \"text-[#0077B6] hover:text-[#20B2AA] mr-3\",\n                          title: \"Edit Patient\",\n                          children: /*#__PURE__*/_jsxDEV(FaEdit, {\n                            className: \"inline\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 445,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 437,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: e => handleUploadClick(e, patient, 'xray'),\n                          className: \"text-green-600 hover:text-green-800 mr-3\",\n                          title: \"Upload X-rays\",\n                          children: /*#__PURE__*/_jsxDEV(FaXRay, {\n                            className: \"inline\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 452,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 447,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: e => handleUploadClick(e, patient, 'gallery'),\n                          className: \"text-purple-600 hover:text-purple-800 mr-3\",\n                          title: \"Upload Gallery Images\",\n                          children: /*#__PURE__*/_jsxDEV(FaImages, {\n                            className: \"inline\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 459,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 454,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: e => {\n                            e.stopPropagation();\n                            setSelectedPatient(patient);\n                            setShowDeleteModal(true);\n                          },\n                          className: \"text-red-600 hover:text-red-700\",\n                          title: \"Delete Patient\",\n                          children: /*#__PURE__*/_jsxDEV(FaTrash, {\n                            className: \"inline\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 470,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 461,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 436,\n                        columnNumber: 29\n                      }, this)]\n                    }, patient.nationalId, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 416,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0\n                },\n                animate: {\n                  opacity: 1\n                },\n                className: \"text-center py-12\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mx-auto h-16 w-16 text-gray-400 mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 486,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"mt-2 text-lg font-bold text-gray-900\",\n                  children: \"No patients found\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-sm text-gray-500\",\n                  children: searchTerm ? 'Try a different search term' : 'No patients are currently assigned to you'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 358,\n      columnNumber: 7\n    }, this), selectedPatient && !showModal && !showDeleteModal && !showUploadModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        className: \"bg-white rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto shadow-2xl border border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-[#0077B6]\",\n            children: selectedPatient.fullName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedPatient(null),\n            className: \"text-gray-500 hover:text-gray-700\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-[#0077B6] mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaUser, {\n                className: \"h-5 w-5 mr-2 text-[#0077B6]\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 19\n              }, this), \"Personal Information\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"National ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 24\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-900 mt-1\",\n                  children: selectedPatient.nationalId\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 90\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"Phone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 24\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-900 mt-1\",\n                  children: selectedPatient.phoneNumber\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 84\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"Gender\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 24\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-900 mt-1\",\n                  children: selectedPatient.gender\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 85\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"Age\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 24\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-900 mt-1\",\n                  children: selectedPatient.age\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 82\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 24\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-900 mt-1\",\n                  children: selectedPatient.address || 'Not provided'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 86\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"Occupation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 24\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-900 mt-1\",\n                  children: selectedPatient.occupation || 'Not provided'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 89\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-[#0077B6] mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaFileMedical, {\n                className: \"h-5 w-5 mr-2 text-[#0077B6]\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 19\n              }, this), \"Medical History\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"Chronic Diseases\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 540,\n                  columnNumber: 24\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-900 mt-1\",\n                  children: ((_selectedPatient$medi = selectedPatient.medicalInfo) === null || _selectedPatient$medi === void 0 ? void 0 : (_selectedPatient$medi2 = _selectedPatient$medi.chronicDiseases) === null || _selectedPatient$medi2 === void 0 ? void 0 : _selectedPatient$medi2.join(', ')) || 'None'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 540,\n                  columnNumber: 95\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"Recent Surgical Procedures\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 24\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-900 mt-1\",\n                  children: ((_selectedPatient$medi3 = selectedPatient.medicalInfo) === null || _selectedPatient$medi3 === void 0 ? void 0 : _selectedPatient$medi3.recentSurgicalProcedures) || 'None'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 105\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"Current Medications\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 24\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-900 mt-1\",\n                  children: ((_selectedPatient$medi4 = selectedPatient.medicalInfo) === null || _selectedPatient$medi4 === void 0 ? void 0 : _selectedPatient$medi4.currentMedications) || 'None'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 98\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-[#0077B6] mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaProcedures, {\n                className: \"h-5 w-5 mr-2 text-[#0077B6]\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 19\n              }, this), \"Chief Complaint\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-900\",\n              children: ((_selectedPatient$medi5 = selectedPatient.medicalInfo) === null || _selectedPatient$medi5 === void 0 ? void 0 : _selectedPatient$medi5.chiefComplaint) || 'None'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => {\n                setSelectedPatient(null);\n                handleEditPatient(selectedPatient);\n              },\n              className: \"px-6 py-2 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full hover:bg-[rgba(0,119,182,0.2)] font-medium transition-all duration-300\",\n              children: \"Edit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => navigate(`/patientprofile/${selectedPatient.nationalId}`),\n              className: \"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg\",\n              children: \"Full History\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 502,\n      columnNumber: 9\n    }, this), showModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        className: \"bg-white rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto shadow-2xl border border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-[#0077B6]\",\n            children: selectedPatient ? 'Edit Patient' : 'New Patient'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowModal(false),\n            className: \"text-gray-500 hover:text-gray-700\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold text-[#0077B6] mb-4 border-b border-[rgba(0,119,182,0.2)] pb-2\",\n            children: \"Personal Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 sm:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1.5\",\n                children: \"Full Name*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"fullName\",\n                value: formData.fullName,\n                onChange: handleInputChange,\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 603,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1.5\",\n                children: \"National ID*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 613,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"nationalId\",\n                value: formData.nationalId,\n                onChange: handleInputChange,\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                required: true,\n                disabled: !!selectedPatient\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 614,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1.5\",\n                children: \"Phone Number*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"tel\",\n                name: \"phoneNumber\",\n                value: formData.phoneNumber,\n                onChange: handleInputChange,\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1.5\",\n                children: \"Gender*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 636,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"gender\",\n                value: formData.gender,\n                onChange: handleInputChange,\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 644,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"male\",\n                  children: \"Male\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 645,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"female\",\n                  children: \"Female\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"other\",\n                  children: \"Other\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1.5\",\n                children: \"Age*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 651,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"age\",\n                value: formData.age,\n                onChange: handleInputChange,\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 650,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1.5\",\n                children: \"Address*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 662,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"address\",\n                value: formData.address,\n                onChange: handleInputChange,\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 661,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1.5\",\n                children: \"Occupation*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"occupation\",\n                value: formData.occupation,\n                onChange: handleInputChange,\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 674,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold text-[#0077B6] mt-6 mb-4 border-b border-[rgba(0,119,182,0.2)] pb-2\",\n            children: \"Medical History\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-3\",\n              children: \"Chronic Diseases\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 sm:grid-cols-3 gap-3 bg-[rgba(0,119,182,0.05)] p-4 rounded-lg border border-[rgba(0,119,182,0.1)]\",\n              children: chronicDiseases.map(disease => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    id: `disease-${disease}`,\n                    checked: formData.medicalInfo.chronicDiseases.includes(disease),\n                    onChange: () => handleChronicDiseaseChange(disease),\n                    className: \"h-5 w-5 text-[#0077B6] focus:ring-2 focus:ring-[#20B2AA] focus:ring-offset-2 border-2 border-gray-300 rounded cursor-pointer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 692,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: `disease-${disease}`,\n                    className: \"ml-2 text-sm font-medium text-gray-700 cursor-pointer hover:text-[#0077B6] transition-all duration-300\",\n                    children: disease\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 699,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 691,\n                  columnNumber: 23\n                }, this)\n              }, disease, false, {\n                fileName: _jsxFileName,\n                lineNumber: 690,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 sm:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Recent Surgical Procedures (Optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 708,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"recentSurgicalProcedures\",\n                value: formData.medicalInfo.recentSurgicalProcedures,\n                onChange: handleMedicalInfoChange,\n                rows: \"2\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 709,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Current Medications (Optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 719,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"currentMedications\",\n                value: formData.medicalInfo.currentMedications,\n                onChange: handleMedicalInfoChange,\n                rows: \"2\",\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold text-[#0077B6] mt-6 mb-4 border-b border-[rgba(0,119,182,0.2)] pb-2\",\n            children: \"Chief Complaint\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 730,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n              name: \"chiefComplaint\",\n              value: formData.medicalInfo.chiefComplaint,\n              onChange: handleInputChange,\n              rows: \"3\",\n              placeholder: \"Describe the patient's main complaint or reason for visit\",\n              className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-4 pt-4\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              type: \"button\",\n              onClick: () => setShowModal(false),\n              className: \"px-6 py-2 border border-[#20B2AA] text-[#20B2AA] rounded-full hover:bg-[rgba(32,178,170,0.1)] font-medium transition-all duration-300\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              type: \"submit\",\n              className: \"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: selectedPatient ? 'Update Patient' : 'Add Patient'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 743,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 579,\n      columnNumber: 9\n    }, this), showDeleteModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        className: \"bg-white rounded-xl p-6 w-full max-w-md shadow-2xl border border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-[#0077B6]\",\n            children: \"Confirm Deletion\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 775,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowDeleteModal(false),\n            className: \"text-gray-500 hover:text-gray-700\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 780,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 776,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 774,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-700 mb-6\",\n          children: [\"Are you sure you want to delete \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold\",\n            children: selectedPatient === null || selectedPatient === void 0 ? void 0 : selectedPatient.fullName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 786,\n            columnNumber: 47\n          }, this), \"'s record? This action cannot be undone.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 785,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(motion.button, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            onClick: () => setShowDeleteModal(false),\n            className: \"px-6 py-2 border border-[#20B2AA] text-[#20B2AA] rounded-full hover:bg-[rgba(32,178,170,0.1)] font-medium transition-all duration-300\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 789,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            onClick: handleDeletePatient,\n            className: \"px-6 py-2 bg-gradient-to-r from-red-500 to-red-700 text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg\",\n            children: \"Delete Patient\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 797,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 788,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 769,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 768,\n      columnNumber: 9\n    }, this), notification.show && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${notification.type === 'success' ? 'bg-[rgba(40,167,69,0.1)] border-l-4 border-[#28A745]' : notification.type === 'error' ? 'bg-red-100 border-l-4 border-red-500' : 'bg-[rgba(0,119,182,0.1)] border-l-4 border-[#0077B6]'}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex-shrink-0 ${notification.type === 'success' ? 'text-[#28A745]' : notification.type === 'error' ? 'text-red-500' : 'text-[#0077B6]'}`,\n          children: notification.type === 'success' ? /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-5 w-5\",\n            viewBox: \"0 0 20 20\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 825,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 824,\n            columnNumber: 17\n          }, this) : notification.type === 'error' ? /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-5 w-5\",\n            viewBox: \"0 0 20 20\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 829,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 828,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-5 w-5\",\n            viewBox: \"0 0 20 20\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 833,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 832,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 818,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ml-3\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: `text-sm font-medium ${notification.type === 'success' ? 'text-[#28A745]' : notification.type === 'error' ? 'text-red-800' : 'text-[#0077B6]'}`,\n            children: notification.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 838,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 837,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ml-auto pl-3\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"-mx-1.5 -my-1.5\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setNotification({\n                show: false,\n                message: '',\n                type: 'success'\n              }),\n              className: `inline-flex rounded-md p-1.5 ${notification.type === 'success' ? 'text-[#28A745] hover:bg-[rgba(40,167,69,0.2)]' : notification.type === 'error' ? 'text-red-500 hover:bg-red-200' : 'text-[#0077B6] hover:bg-[rgba(0,119,182,0.2)]'} focus:outline-none transition-all duration-300`,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sr-only\",\n                children: \"Dismiss\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 856,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-4 w-4\",\n                viewBox: \"0 0 20 20\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 858,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 857,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 848,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 847,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 846,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 817,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 812,\n      columnNumber: 9\n    }, this), showUploadModal && selectedPatient && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        className: \"bg-white rounded-xl p-6 w-full max-w-md shadow-2xl border border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-blue-900\",\n            children: [\"Upload \", uploadType === 'xray' ? 'X-rays' : 'Gallery Images']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 876,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowUploadModal(false),\n            className: \"text-gray-500 hover:text-gray-700\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 884,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 883,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 879,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 875,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-700 mb-4\",\n            children: [\"Upload \", uploadType === 'xray' ? 'X-rays' : 'gallery images', \" for \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold\",\n              children: selectedPatient.fullName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 891,\n              columnNumber: 82\n            }, this), \".\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 890,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center justify-center p-6 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors cursor-pointer\",\n            onClick: () => fileInputRef.current.click(),\n            children: [uploadType === 'xray' ? /*#__PURE__*/_jsxDEV(FaXRay, {\n              className: \"h-12 w-12 text-blue-500 mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 896,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(FaImages, {\n              className: \"h-12 w-12 text-purple-500 mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 898,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 font-medium\",\n              children: \"Click to select files\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 900,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-500 text-sm mt-1\",\n              children: \"or drag and drop files here\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 901,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              ref: fileInputRef,\n              onChange: handleFileUpload,\n              className: \"hidden\",\n              accept: \"image/*\",\n              multiple: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 902,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 894,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 text-sm text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Supported formats: JPG, PNG\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 913,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Maximum file size: 5MB per file\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 914,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 912,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 889,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(motion.button, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            onClick: () => setShowUploadModal(false),\n            className: \"px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 font-medium transition-colors\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 919,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            onClick: () => fileInputRef.current.click(),\n            className: `px-6 py-2 bg-gradient-to-r ${uploadType === 'xray' ? 'from-green-500 to-green-700 hover:from-green-600 hover:to-green-800' : 'from-purple-500 to-purple-700 hover:from-purple-600 hover:to-purple-800'} text-white rounded-full font-medium transition-colors shadow-md`,\n            children: \"Select Files\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 927,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 918,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 870,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 869,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 355,\n    columnNumber: 5\n  }, this);\n};\n_s(Patients, \"Y1Qp8wlhKt9xAPS67AhuMxW0FkQ=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Patients;\nexport default Patients;\nvar _c;\n$RefreshReg$(_c, \"Patients\");", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "Link", "useNavigate", "axios", "useAuth", "<PERSON><PERSON><PERSON>", "Sidebar", "Loader", "motion", "FaUser", "FaUserPlus", "FaSearch", "FaEdit", "FaTrash", "FaFileMedical", "FaProcedures", "FaPills", "FaUpload", "FaXRay", "FaImages", "RiAiGenerate", "jsxDEV", "_jsxDEV", "chronicDiseases", "Patients", "_s", "_selectedPatient$medi", "_selectedPatient$medi2", "_selectedPatient$medi3", "_selectedPatient$medi4", "_selectedPatient$medi5", "user", "token", "navigate", "patients", "setPatients", "loading", "setLoading", "error", "setError", "selectedPatient", "setSelectedPatient", "showModal", "setShowModal", "showDeleteModal", "setShowDeleteModal", "showUploadModal", "setShowUploadModal", "uploadType", "setUploadType", "sidebarOpen", "setSidebarOpen", "searchTerm", "setSearchTerm", "notification", "setNotification", "show", "message", "type", "fileInputRef", "formData", "setFormData", "nationalId", "fullName", "phoneNumber", "gender", "age", "address", "occupation", "medicalInfo", "recentSurgicalProcedures", "currentMedications", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fetchPatients", "config", "headers", "Authorization", "response", "get", "process", "env", "REACT_APP_API_URL", "Array", "isArray", "data", "validPatients", "filter", "patient", "err", "_err$response", "_err$response2", "_err$response3", "_err$response3$data", "console", "status", "handleUploadClick", "e", "stopPropagation", "handleFileUpload", "files", "target", "length", "FormData", "fieldName", "from", "for<PERSON>ach", "file", "append", "endpoint", "post", "setTimeout", "_err$response4", "_err$response4$data", "handleSearch", "value", "filteredPatients", "toLowerCase", "includes", "handleInputChange", "name", "prev", "handleMedicalInfoChange", "handleChronicDiseaseChange", "disease", "diseases", "d", "handleViewPatient", "handleAddPatient", "handleEditPatient", "handleSubmit", "preventDefault", "patientData", "drId", "studentId", "log", "put", "map", "p", "Error", "prevPatients", "_err$response5", "_err$response6", "_err$response7", "_err$response7$data", "handleDeletePatient", "delete", "_err$response8", "_err$response9", "_err$response10", "_err$response10$data", "container", "hidden", "opacity", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "item", "y", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "isOpen", "setIsOpen", "toggleSidebar", "div", "initial", "scale", "animate", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "button", "whileHover", "whileTap", "onClick", "window", "location", "reload", "duration", "placeholder", "onChange", "variants", "tr", "title", "join", "onSubmit", "required", "disabled", "id", "checked", "htmlFor", "rows", "fillRule", "clipRule", "current", "click", "ref", "accept", "multiple", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/student/Patients.jsx"], "sourcesContent": ["import { useState, useEffect, useRef } from 'react';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport axios from 'axios';\r\nimport { useAuth } from '../context/AuthContext';\r\nimport Navbar from './Navbar';\r\nimport Sidebar from './Sidebar';\r\nimport Loader from '../components/Loader';\r\nimport { motion } from 'framer-motion';\r\nimport { FaUser, FaUserPlus, FaSearch, FaEdit, FaTrash, FaFileMedical, FaProcedures, FaPills, FaUpload, FaXRay, FaImages } from 'react-icons/fa';\r\nimport { RiAiGenerate } from 'react-icons/ri';\r\n\r\nconst chronicDiseases = [\r\n  'Diabetes Mellitus',\r\n  'Hypertension',\r\n  'Cardiovascular Disease',\r\n  'Coagulopathy / Bleeding Disorders',\r\n  'Thyroid Disorders',\r\n  'Pregnancy',\r\n  'Lactation',\r\n  'Hepatic Diseases',\r\n  'Renal Diseases'\r\n];\r\n\r\nconst Patients = () => {\r\n  const { user, token } = useAuth();\r\n  const navigate = useNavigate();\r\n  const [patients, setPatients] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [selectedPatient, setSelectedPatient] = useState(null);\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\r\n  const [showUploadModal, setShowUploadModal] = useState(false);\r\n  const [uploadType, setUploadType] = useState('');\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [notification, setNotification] = useState({ show: false, message: '', type: 'success' });\r\n  const fileInputRef = useRef(null);\r\n\r\n  const [formData, setFormData] = useState({\r\n    nationalId: '',\r\n    fullName: '',\r\n    phoneNumber: '',\r\n    gender: '',\r\n    age: '',\r\n    address: '',\r\n    occupation: '',\r\n    medicalInfo: {\r\n      chronicDiseases: [],\r\n      recentSurgicalProcedures: '',\r\n      currentMedications: '',\r\n      chiefComplaint: '',\r\n    },\r\n  });\r\n\r\n  useEffect(() => {\r\n    const fetchPatients = async () => {\r\n      if (!user || !token) {\r\n        setError('Please log in to view patients.');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      try {\r\n        const config = { headers: { Authorization: `Bearer ${token}` } };\r\n        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/patients`, config);\r\n        if (Array.isArray(response.data)) {\r\n          const validPatients = response.data.filter(patient =>\r\n            patient &&\r\n            typeof patient === 'object' &&\r\n            patient.nationalId &&\r\n            patient.fullName\r\n          );\r\n          setPatients(validPatients);\r\n        } else {\r\n          setError('Invalid patient data received from server');\r\n          setPatients([]);\r\n        }\r\n      } catch (err) {\r\n        console.error('Fetch error:', err.response?.status, err.response?.data);\r\n        setError(err.response?.data?.message || 'Failed to load patients');\r\n        setPatients([]);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    fetchPatients();\r\n  }, [user, token]);\r\n\r\n  useEffect(() => {\r\n    // Only clear selectedPatient when all modals are closed\r\n    if (!showModal && !showDeleteModal && !showUploadModal) {\r\n      setSelectedPatient(null);\r\n    }\r\n  }, [showModal, showDeleteModal, showUploadModal]);\r\n\r\n  const handleUploadClick = (e, patient, type) => {\r\n    // Stop event propagation to prevent the row click handler from firing\r\n    e.stopPropagation();\r\n\r\n    setSelectedPatient(patient);\r\n    setUploadType(type);\r\n    setShowUploadModal(true);\r\n  };\r\n\r\n  const handleFileUpload = async (e) => {\r\n    const files = e.target.files;\r\n    if (!files || files.length === 0) return;\r\n\r\n    const formData = new FormData();\r\n    const fieldName = uploadType === 'xray' ? 'xrays' : 'galleryImages';\r\n    Array.from(files).forEach(file => formData.append(fieldName, file));\r\n\r\n    try {\r\n      const endpoint = uploadType === 'xray'\r\n        ? `${process.env.REACT_APP_API_URL}/api/patients/${selectedPatient.nationalId}/xrays`\r\n        : `${process.env.REACT_APP_API_URL}/api/patients/${selectedPatient.nationalId}/gallery`;\r\n\r\n      const response = await axios.post(endpoint, formData, {\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n          'Content-Type': 'multipart/form-data'\r\n        },\r\n      });\r\n\r\n      if (response.data) {\r\n        // Show success notification\r\n        setNotification({\r\n          show: true,\r\n          message: `${uploadType === 'xray' ? 'X-rays' : 'Gallery images'} uploaded successfully!`,\r\n          type: 'success'\r\n        });\r\n\r\n        // Hide notification after 3 seconds\r\n        setTimeout(() => {\r\n          setNotification({ show: false, message: '', type: 'success' });\r\n        }, 3000);\r\n\r\n        setShowUploadModal(false);\r\n      }\r\n    } catch (err) {\r\n      console.error('Upload error:', err);\r\n      setError(err.response?.data?.message || `Failed to upload ${uploadType === 'xray' ? 'X-rays' : 'gallery images'}`);\r\n    }\r\n  };\r\n\r\n  const handleSearch = (e) => setSearchTerm(e.target.value);\r\n\r\n  const filteredPatients = patients.filter(patient => {\r\n    if (!patient || typeof patient !== 'object') return false;\r\n    const fullName = patient.fullName || '';\r\n    const nationalId = patient.nationalId || '';\r\n    const phoneNumber = patient.phoneNumber || '';\r\n    return (\r\n      fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n      nationalId.includes(searchTerm) ||\r\n      phoneNumber.includes(searchTerm)\r\n    );\r\n  });\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value } = e.target;\r\n    if (name === 'chiefComplaint') {\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        medicalInfo: { ...prev.medicalInfo, chiefComplaint: value },\r\n      }));\r\n    } else {\r\n      setFormData(prev => ({ ...prev, [name]: value }));\r\n    }\r\n  };\r\n\r\n  const handleMedicalInfoChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      medicalInfo: { ...prev.medicalInfo, [name]: value },\r\n    }));\r\n  };\r\n\r\n  const handleChronicDiseaseChange = (disease) => {\r\n    setFormData(prev => {\r\n      const diseases = prev.medicalInfo.chronicDiseases.includes(disease)\r\n        ? prev.medicalInfo.chronicDiseases.filter(d => d !== disease)\r\n        : [...prev.medicalInfo.chronicDiseases, disease];\r\n      return {\r\n        ...prev,\r\n        medicalInfo: { ...prev.medicalInfo, chronicDiseases: diseases },\r\n      };\r\n    });\r\n  };\r\n\r\n  const handleViewPatient = (patient) => {\r\n    // Go directly to patient history instead of showing the popup\r\n    navigate(`/patientprofile/${patient.nationalId}/history`);\r\n  };\r\n\r\n  const handleAddPatient = () => {\r\n    setFormData({\r\n      nationalId: '',\r\n      fullName: '',\r\n      phoneNumber: '',\r\n      gender: '',\r\n      age: '',\r\n      address: '',\r\n      occupation: '',\r\n      medicalInfo: {\r\n        chronicDiseases: [],\r\n        recentSurgicalProcedures: '',\r\n        currentMedications: '',\r\n        chiefComplaint: '',\r\n      },\r\n    });\r\n    setSelectedPatient(null);\r\n    setShowModal(true);\r\n  };\r\n\r\n  const handleEditPatient = (patient) => {\r\n    setFormData({\r\n      nationalId: patient.nationalId,\r\n      fullName: patient.fullName,\r\n      phoneNumber: patient.phoneNumber,\r\n      gender: patient.gender,\r\n      age: patient.age,\r\n      address: patient.address || '',\r\n      occupation: patient.occupation || '',\r\n      medicalInfo: patient.medicalInfo || {\r\n        chronicDiseases: [],\r\n        recentSurgicalProcedures: '',\r\n        currentMedications: '',\r\n        chiefComplaint: '',\r\n      },\r\n    });\r\n    setSelectedPatient(patient);\r\n    setShowModal(true);\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    try {\r\n      const config = { headers: { Authorization: `Bearer ${token}` } };\r\n      const patientData = {\r\n        ...formData,\r\n        drId: user.studentId, // Use studentId instead of id\r\n      };\r\n\r\n      console.log('Creating/updating patient with drId:', user.studentId);\r\n\r\n      if (selectedPatient) {\r\n        const response = await axios.put(\r\n          `${process.env.REACT_APP_API_URL}/api/patients/${selectedPatient.nationalId}`,\r\n          patientData,\r\n          config\r\n        );\r\n        if (response.data && response.data.patient) {\r\n          setPatients(patients.map(p =>\r\n            p.nationalId === selectedPatient.nationalId ? response.data.patient : p\r\n          ));\r\n        } else {\r\n          throw new Error('Invalid patient data received from server');\r\n        }\r\n        setShowModal(false);\r\n      } else {\r\n        const response = await axios.post(\r\n          `${process.env.REACT_APP_API_URL}/api/patients`,\r\n          patientData,\r\n          config\r\n        );\r\n        if (response.data && response.data.patient) {\r\n          setPatients(prevPatients => [...prevPatients, response.data.patient]);\r\n          setShowModal(false);\r\n          setTimeout(() => {\r\n            navigate(`/patientprofile/${response.data.patient.nationalId}`);\r\n          }, 0);\r\n        } else {\r\n          throw new Error('Invalid patient data received from server');\r\n        }\r\n      }\r\n    } catch (err) {\r\n      console.error('Submit error:', err.response?.status, err.response?.data);\r\n      setError(err.response?.data?.message || 'Failed to save patient');\r\n    }\r\n  };\r\n\r\n  const handleDeletePatient = async () => {\r\n    try {\r\n      const config = { headers: { Authorization: `Bearer ${token}` } };\r\n      await axios.delete(\r\n        `${process.env.REACT_APP_API_URL}/api/patients/${selectedPatient.nationalId}`,\r\n        config\r\n      );\r\n      setPatients(patients.filter(p => p.nationalId !== selectedPatient.nationalId));\r\n      setShowDeleteModal(false);\r\n      setSelectedPatient(null);\r\n    } catch (err) {\r\n      console.error('Delete error:', err.response?.status, err.response?.data);\r\n      setError(err.response?.data?.message || 'Failed to delete patient');\r\n    }\r\n  };\r\n\r\n  // Animation variants\r\n  const container = {\r\n    hidden: { opacity: 0 },\r\n    show: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1\r\n      }\r\n    }\r\n  };\r\n\r\n  const item = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    show: { opacity: 1, y: 0 }\r\n  };\r\n\r\n  if (loading) {\r\n    return <Loader />;\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"flex h-screen bg-gray-50\">\r\n        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n        <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\r\n          <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-blue-50 to-white flex items-center justify-center\">\r\n            <motion.div\r\n              initial={{ scale: 0.9, opacity: 0 }}\r\n              animate={{ scale: 1, opacity: 1 }}\r\n              className=\"text-center max-w-md bg-white p-8 rounded-xl shadow-sm border border-gray-100\"\r\n            >\r\n              <div className=\"text-red-500 mb-4\">\r\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-12 w-12 mx-auto\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\" />\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-lg font-bold text-gray-900 mb-2\">Error</h3>\r\n              <p className=\"text-gray-600 mb-6\">{error}</p>\r\n              <motion.button\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                onClick={() => window.location.reload()}\r\n                className=\"px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-full hover:from-blue-600 hover:to-blue-800 font-medium shadow-md\"\r\n              >\r\n                Try Again\r\n              </motion.button>\r\n            </motion.div>\r\n          </main>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex h-screen bg-gray-50\">\r\n      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />\r\n\r\n        <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\">\r\n          <div className=\"max-w-7xl mx-auto\">\r\n            <motion.div\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              transition={{ duration: 0.5 }}\r\n            >\r\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\">\r\n                <div>\r\n                  <h1 className=\"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\">\r\n                    Patient Management\r\n                  </h1>\r\n                  <p className=\"text-[#333333]\">Manage your patient records</p>\r\n                </div>\r\n                <div className=\"flex flex-col md:flex-row items-center gap-4 w-full md:w-auto\">\r\n                  <motion.div\r\n                    whileHover={{ scale: 1.02 }}\r\n                    className=\"relative w-full md:w-64\"\r\n                  >\r\n                    <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                      <FaSearch className=\"h-5 w-5 text-gray-400\" />\r\n                    </div>\r\n                    <input\r\n                      type=\"text\"\r\n                      placeholder=\"Search patients...\"\r\n                      value={searchTerm}\r\n                      onChange={handleSearch}\r\n                      className=\"block w-full pl-10 pr-3 py-2.5 border border-gray-200 rounded-full bg-white shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#20B2AA] focus:bg-white sm:text-sm transition-all duration-200\"\r\n                    />\r\n                  </motion.div>\r\n\r\n                </div>\r\n              </div>\r\n\r\n              <motion.div\r\n                variants={container}\r\n                initial=\"hidden\"\r\n                animate=\"show\"\r\n                className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-4 md:p-6\"\r\n              >\r\n                {filteredPatients.length > 0 ? (\r\n                  <div className=\"overflow-x-auto\">\r\n                    <table className=\"min-w-full divide-y divide-gray-200\">\r\n                      <thead className=\"bg-[rgba(0,119,182,0.05)]\">\r\n                        <tr>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider\">Patient</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider\">National ID</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider\">Phone</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider\">Gender</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider\">Age</th>\r\n                          <th className=\"px-6 py-3 text-right text-xs font-medium text-[#333333] uppercase tracking-wider\">Actions</th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                        {filteredPatients.map((patient) => (\r\n                          <motion.tr\r\n                            key={patient.nationalId}\r\n                            variants={item}\r\n                            className=\"hover:bg-[rgba(0,119,182,0.05)] cursor-pointer\"\r\n                            onClick={() => handleViewPatient(patient)}\r\n                          >\r\n                            <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                              <div className=\"flex items-center\">\r\n                                <div className=\"flex-shrink-0 h-10 w-10 rounded-full bg-[rgba(0,119,182,0.1)] flex items-center justify-center text-[#0077B6]\">\r\n                                  <FaUser className=\"h-5 w-5\" />\r\n                                </div>\r\n                                <div className=\"ml-4\">\r\n                                  <div className=\"text-sm font-medium text-[#333333]\">{patient.fullName}</div>\r\n                                </div>\r\n                              </div>\r\n                            </td>\r\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{patient.nationalId}</td>\r\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{patient.phoneNumber}</td>\r\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{patient.gender}</td>\r\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{patient.age}</td>\r\n                            <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\r\n                              <button\r\n                                onClick={(e) => {\r\n                                  e.stopPropagation();\r\n                                  handleEditPatient(patient);\r\n                                }}\r\n                                className=\"text-[#0077B6] hover:text-[#20B2AA] mr-3\"\r\n                                title=\"Edit Patient\"\r\n                              >\r\n                                <FaEdit className=\"inline\" />\r\n                              </button>\r\n                              <button\r\n                                onClick={(e) => handleUploadClick(e, patient, 'xray')}\r\n                                className=\"text-green-600 hover:text-green-800 mr-3\"\r\n                                title=\"Upload X-rays\"\r\n                              >\r\n                                <FaXRay className=\"inline\" />\r\n                              </button>\r\n                              <button\r\n                                onClick={(e) => handleUploadClick(e, patient, 'gallery')}\r\n                                className=\"text-purple-600 hover:text-purple-800 mr-3\"\r\n                                title=\"Upload Gallery Images\"\r\n                              >\r\n                                <FaImages className=\"inline\" />\r\n                              </button>\r\n                              <button\r\n                                onClick={(e) => {\r\n                                  e.stopPropagation();\r\n                                  setSelectedPatient(patient);\r\n                                  setShowDeleteModal(true);\r\n                                }}\r\n                                className=\"text-red-600 hover:text-red-700\"\r\n                                title=\"Delete Patient\"\r\n                              >\r\n                                <FaTrash className=\"inline\" />\r\n                              </button>\r\n                            </td>\r\n                          </motion.tr>\r\n                        ))}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                ) : (\r\n                  <motion.div\r\n                    initial={{ opacity: 0 }}\r\n                    animate={{ opacity: 1 }}\r\n                    className=\"text-center py-12\"\r\n                  >\r\n                    <div className=\"mx-auto h-16 w-16 text-gray-400 mb-4\">\r\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                      </svg>\r\n                    </div>\r\n                    <h3 className=\"mt-2 text-lg font-bold text-gray-900\">No patients found</h3>\r\n                    <p className=\"mt-1 text-sm text-gray-500\">\r\n                      {searchTerm ? 'Try a different search term' : 'No patients are currently assigned to you'}\r\n                    </p>\r\n                  </motion.div>\r\n                )}\r\n              </motion.div>\r\n            </motion.div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n\r\n      {selectedPatient && !showModal && !showDeleteModal && !showUploadModal && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n          <motion.div\r\n            initial={{ scale: 0.9, opacity: 0 }}\r\n            animate={{ scale: 1, opacity: 1 }}\r\n            className=\"bg-white rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto shadow-2xl border border-gray-100\"\r\n          >\r\n            <div className=\"flex justify-between items-center mb-6\">\r\n              <h2 className=\"text-2xl font-bold text-[#0077B6]\">{selectedPatient.fullName}</h2>\r\n              <button\r\n                onClick={() => setSelectedPatient(null)}\r\n                className=\"text-gray-500 hover:text-gray-700\"\r\n              >\r\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                </svg>\r\n              </button>\r\n            </div>\r\n            <div className=\"space-y-6\">\r\n              <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]\">\r\n                <h3 className=\"text-lg font-semibold text-[#0077B6] mb-4 flex items-center\">\r\n                  <FaUser className=\"h-5 w-5 mr-2 text-[#0077B6]\" />\r\n                  Personal Information\r\n                </h3>\r\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n                  <div><h4 className=\"text-sm font-medium text-gray-500\">National ID</h4><p className=\"text-sm text-gray-900 mt-1\">{selectedPatient.nationalId}</p></div>\r\n                  <div><h4 className=\"text-sm font-medium text-gray-500\">Phone</h4><p className=\"text-sm text-gray-900 mt-1\">{selectedPatient.phoneNumber}</p></div>\r\n                  <div><h4 className=\"text-sm font-medium text-gray-500\">Gender</h4><p className=\"text-sm text-gray-900 mt-1\">{selectedPatient.gender}</p></div>\r\n                  <div><h4 className=\"text-sm font-medium text-gray-500\">Age</h4><p className=\"text-sm text-gray-900 mt-1\">{selectedPatient.age}</p></div>\r\n                  <div><h4 className=\"text-sm font-medium text-gray-500\">Address</h4><p className=\"text-sm text-gray-900 mt-1\">{selectedPatient.address || 'Not provided'}</p></div>\r\n                  <div><h4 className=\"text-sm font-medium text-gray-500\">Occupation</h4><p className=\"text-sm text-gray-900 mt-1\">{selectedPatient.occupation || 'Not provided'}</p></div>\r\n                </div>\r\n              </div>\r\n              <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]\">\r\n                <h3 className=\"text-lg font-semibold text-[#0077B6] mb-4 flex items-center\">\r\n                  <FaFileMedical className=\"h-5 w-5 mr-2 text-[#0077B6]\" />\r\n                  Medical History\r\n                </h3>\r\n                <div className=\"space-y-4\">\r\n                  <div><h4 className=\"text-sm font-medium text-gray-500\">Chronic Diseases</h4><p className=\"text-sm text-gray-900 mt-1\">{selectedPatient.medicalInfo?.chronicDiseases?.join(', ') || 'None'}</p></div>\r\n                  <div><h4 className=\"text-sm font-medium text-gray-500\">Recent Surgical Procedures</h4><p className=\"text-sm text-gray-900 mt-1\">{selectedPatient.medicalInfo?.recentSurgicalProcedures || 'None'}</p></div>\r\n                  <div><h4 className=\"text-sm font-medium text-gray-500\">Current Medications</h4><p className=\"text-sm text-gray-900 mt-1\">{selectedPatient.medicalInfo?.currentMedications || 'None'}</p></div>\r\n                </div>\r\n              </div>\r\n              <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]\">\r\n                <h3 className=\"text-lg font-semibold text-[#0077B6] mb-4 flex items-center\">\r\n                  <FaProcedures className=\"h-5 w-5 mr-2 text-[#0077B6]\" />\r\n                  Chief Complaint\r\n                </h3>\r\n                <p className=\"text-sm text-gray-900\">{selectedPatient.medicalInfo?.chiefComplaint || 'None'}</p>\r\n              </div>\r\n              <div className=\"flex justify-end space-x-4\">\r\n                <motion.button\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                  onClick={() => {\r\n                    setSelectedPatient(null);\r\n                    handleEditPatient(selectedPatient);\r\n                  }}\r\n                  className=\"px-6 py-2 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full hover:bg-[rgba(0,119,182,0.2)] font-medium transition-all duration-300\"\r\n                >\r\n                  Edit\r\n                </motion.button>\r\n                <motion.button\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                  onClick={() => navigate(`/patientprofile/${selectedPatient.nationalId}`)}\r\n                  className=\"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg\"\r\n                >\r\n                  Full History\r\n                </motion.button>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      )}\r\n\r\n      {showModal && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n          <motion.div\r\n            initial={{ scale: 0.9, opacity: 0 }}\r\n            animate={{ scale: 1, opacity: 1 }}\r\n            className=\"bg-white rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto shadow-2xl border border-gray-100\"\r\n          >\r\n            <div className=\"flex justify-between items-center mb-6\">\r\n              <h2 className=\"text-2xl font-bold text-[#0077B6]\">\r\n                {selectedPatient ? 'Edit Patient' : 'New Patient'}\r\n              </h2>\r\n              <button\r\n                onClick={() => setShowModal(false)}\r\n                className=\"text-gray-500 hover:text-gray-700\"\r\n              >\r\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                </svg>\r\n              </button>\r\n            </div>\r\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n              <h3 className=\"text-xl font-semibold text-[#0077B6] mb-4 border-b border-[rgba(0,119,182,0.2)] pb-2\">Personal Information</h3>\r\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">Full Name*</label>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"fullName\"\r\n                    value={formData.fullName}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n                    required\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">National ID*</label>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"nationalId\"\r\n                    value={formData.nationalId}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n                    required\r\n                    disabled={!!selectedPatient}\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">Phone Number*</label>\r\n                  <input\r\n                    type=\"tel\"\r\n                    name=\"phoneNumber\"\r\n                    value={formData.phoneNumber}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n                    required\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">Gender*</label>\r\n                  <select\r\n                    name=\"gender\"\r\n                    value={formData.gender}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n                    required\r\n                  >\r\n                    <option value=\"\">Select</option>\r\n                    <option value=\"male\">Male</option>\r\n                    <option value=\"female\">Female</option>\r\n                    <option value=\"other\">Other</option>\r\n                  </select>\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">Age*</label>\r\n                  <input\r\n                    type=\"number\"\r\n                    name=\"age\"\r\n                    value={formData.age}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n                    required\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">Address*</label>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"address\"\r\n                    value={formData.address}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n                    required\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">Occupation*</label>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"occupation\"\r\n                    value={formData.occupation}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n                    required\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <h3 className=\"text-xl font-semibold text-[#0077B6] mt-6 mb-4 border-b border-[rgba(0,119,182,0.2)] pb-2\">Medical History</h3>\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-3\">Chronic Diseases</label>\r\n                <div className=\"grid grid-cols-2 sm:grid-cols-3 gap-3 bg-[rgba(0,119,182,0.05)] p-4 rounded-lg border border-[rgba(0,119,182,0.1)]\">\r\n                  {chronicDiseases.map((disease) => (\r\n                    <div key={disease} className=\"flex items-center\">\r\n                      <div className=\"relative flex items-center\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          id={`disease-${disease}`}\r\n                          checked={formData.medicalInfo.chronicDiseases.includes(disease)}\r\n                          onChange={() => handleChronicDiseaseChange(disease)}\r\n                          className=\"h-5 w-5 text-[#0077B6] focus:ring-2 focus:ring-[#20B2AA] focus:ring-offset-2 border-2 border-gray-300 rounded cursor-pointer\"\r\n                        />\r\n                        <label htmlFor={`disease-${disease}`} className=\"ml-2 text-sm font-medium text-gray-700 cursor-pointer hover:text-[#0077B6] transition-all duration-300\">{disease}</label>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Recent Surgical Procedures (Optional)</label>\r\n                  <textarea\r\n                    name=\"recentSurgicalProcedures\"\r\n                    value={formData.medicalInfo.recentSurgicalProcedures}\r\n                    onChange={handleMedicalInfoChange}\r\n                    rows=\"2\"\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Current Medications (Optional)</label>\r\n                  <textarea\r\n                    name=\"currentMedications\"\r\n                    value={formData.medicalInfo.currentMedications}\r\n                    onChange={handleMedicalInfoChange}\r\n                    rows=\"2\"\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <h3 className=\"text-xl font-semibold text-[#0077B6] mt-6 mb-4 border-b border-[rgba(0,119,182,0.2)] pb-2\">Chief Complaint</h3>\r\n              <div>\r\n                <textarea\r\n                  name=\"chiefComplaint\"\r\n                  value={formData.medicalInfo.chiefComplaint}\r\n                  onChange={handleInputChange}\r\n                  rows=\"3\"\r\n                  placeholder=\"Describe the patient's main complaint or reason for visit\"\r\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              <div className=\"flex justify-end space-x-4 pt-4\">\r\n                <motion.button\r\n                  type=\"button\"\r\n                  onClick={() => setShowModal(false)}\r\n                  className=\"px-6 py-2 border border-[#20B2AA] text-[#20B2AA] rounded-full hover:bg-[rgba(32,178,170,0.1)] font-medium transition-all duration-300\"\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                >\r\n                  Cancel\r\n                </motion.button>\r\n                <motion.button\r\n                  type=\"submit\"\r\n                  className=\"px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg\"\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                >\r\n                  {selectedPatient ? 'Update Patient' : 'Add Patient'}\r\n                </motion.button>\r\n              </div>\r\n            </form>\r\n          </motion.div>\r\n        </div>\r\n      )}\r\n\r\n      {showDeleteModal && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n          <motion.div\r\n            initial={{ scale: 0.9, opacity: 0 }}\r\n            animate={{ scale: 1, opacity: 1 }}\r\n            className=\"bg-white rounded-xl p-6 w-full max-w-md shadow-2xl border border-gray-100\"\r\n          >\r\n            <div className=\"flex justify-between items-center mb-6\">\r\n              <h2 className=\"text-2xl font-bold text-[#0077B6]\">Confirm Deletion</h2>\r\n              <button\r\n                onClick={() => setShowDeleteModal(false)}\r\n                className=\"text-gray-500 hover:text-gray-700\"\r\n              >\r\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                </svg>\r\n              </button>\r\n            </div>\r\n            <p className=\"text-gray-700 mb-6\">\r\n              Are you sure you want to delete <span className=\"font-semibold\">{selectedPatient?.fullName}</span>'s record? This action cannot be undone.\r\n            </p>\r\n            <div className=\"flex justify-end space-x-4\">\r\n              <motion.button\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                onClick={() => setShowDeleteModal(false)}\r\n                className=\"px-6 py-2 border border-[#20B2AA] text-[#20B2AA] rounded-full hover:bg-[rgba(32,178,170,0.1)] font-medium transition-all duration-300\"\r\n              >\r\n                Cancel\r\n              </motion.button>\r\n              <motion.button\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                onClick={handleDeletePatient}\r\n                className=\"px-6 py-2 bg-gradient-to-r from-red-500 to-red-700 text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg\"\r\n              >\r\n                Delete Patient\r\n              </motion.button>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Success notification */}\r\n      {notification.show && (\r\n        <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${\r\n          notification.type === 'success' ? 'bg-[rgba(40,167,69,0.1)] border-l-4 border-[#28A745]' :\r\n          notification.type === 'error' ? 'bg-red-100 border-l-4 border-red-500' :\r\n          'bg-[rgba(0,119,182,0.1)] border-l-4 border-[#0077B6]'\r\n        }`}>\r\n          <div className=\"flex items-center\">\r\n            <div className={`flex-shrink-0 ${\r\n              notification.type === 'success' ? 'text-[#28A745]' :\r\n              notification.type === 'error' ? 'text-red-500' :\r\n              'text-[#0077B6]'\r\n            }`}>\r\n              {notification.type === 'success' ? (\r\n                <svg className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\r\n                </svg>\r\n              ) : notification.type === 'error' ? (\r\n                <svg className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n                </svg>\r\n              ) : (\r\n                <svg className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                  <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\r\n                </svg>\r\n              )}\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <p className={`text-sm font-medium ${\r\n                notification.type === 'success' ? 'text-[#28A745]' :\r\n                notification.type === 'error' ? 'text-red-800' :\r\n                'text-[#0077B6]'\r\n              }`}>\r\n                {notification.message}\r\n              </p>\r\n            </div>\r\n            <div className=\"ml-auto pl-3\">\r\n              <div className=\"-mx-1.5 -my-1.5\">\r\n                <button\r\n                  onClick={() => setNotification({ show: false, message: '', type: 'success' })}\r\n                  className={`inline-flex rounded-md p-1.5 ${\r\n                    notification.type === 'success' ? 'text-[#28A745] hover:bg-[rgba(40,167,69,0.2)]' :\r\n                    notification.type === 'error' ? 'text-red-500 hover:bg-red-200' :\r\n                    'text-[#0077B6] hover:bg-[rgba(0,119,182,0.2)]'\r\n                  } focus:outline-none transition-all duration-300`}\r\n                >\r\n                  <span className=\"sr-only\">Dismiss</span>\r\n                  <svg className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                    <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Upload Modal */}\r\n      {showUploadModal && selectedPatient && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n          <motion.div\r\n            initial={{ scale: 0.9, opacity: 0 }}\r\n            animate={{ scale: 1, opacity: 1 }}\r\n            className=\"bg-white rounded-xl p-6 w-full max-w-md shadow-2xl border border-gray-100\"\r\n          >\r\n            <div className=\"flex justify-between items-center mb-6\">\r\n              <h2 className=\"text-2xl font-bold text-blue-900\">\r\n                Upload {uploadType === 'xray' ? 'X-rays' : 'Gallery Images'}\r\n              </h2>\r\n              <button\r\n                onClick={() => setShowUploadModal(false)}\r\n                className=\"text-gray-500 hover:text-gray-700\"\r\n              >\r\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                </svg>\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"mb-6\">\r\n              <p className=\"text-gray-700 mb-4\">\r\n                Upload {uploadType === 'xray' ? 'X-rays' : 'gallery images'} for <span className=\"font-semibold\">{selectedPatient.fullName}</span>.\r\n              </p>\r\n\r\n              <div className=\"flex flex-col items-center justify-center p-6 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors cursor-pointer\" onClick={() => fileInputRef.current.click()}>\r\n                {uploadType === 'xray' ? (\r\n                  <FaXRay className=\"h-12 w-12 text-blue-500 mb-4\" />\r\n                ) : (\r\n                  <FaImages className=\"h-12 w-12 text-purple-500 mb-4\" />\r\n                )}\r\n                <p className=\"text-gray-700 font-medium\">Click to select files</p>\r\n                <p className=\"text-gray-500 text-sm mt-1\">or drag and drop files here</p>\r\n                <input\r\n                  type=\"file\"\r\n                  ref={fileInputRef}\r\n                  onChange={handleFileUpload}\r\n                  className=\"hidden\"\r\n                  accept=\"image/*\"\r\n                  multiple\r\n                />\r\n              </div>\r\n\r\n              <div className=\"mt-4 text-sm text-gray-500\">\r\n                <p>Supported formats: JPG, PNG</p>\r\n                <p>Maximum file size: 5MB per file</p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex justify-end space-x-4\">\r\n              <motion.button\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                onClick={() => setShowUploadModal(false)}\r\n                className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 font-medium transition-colors\"\r\n              >\r\n                Cancel\r\n              </motion.button>\r\n              <motion.button\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                onClick={() => fileInputRef.current.click()}\r\n                className={`px-6 py-2 bg-gradient-to-r ${\r\n                  uploadType === 'xray'\r\n                    ? 'from-green-500 to-green-700 hover:from-green-600 hover:to-green-800'\r\n                    : 'from-purple-500 to-purple-700 hover:from-purple-600 hover:to-purple-800'\r\n                } text-white rounded-full font-medium transition-colors shadow-md`}\r\n              >\r\n                Select Files\r\n              </motion.button>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Patients;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,aAAa,EAAEC,YAAY,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,gBAAgB;AAChJ,SAASC,YAAY,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,eAAe,GAAG,CACtB,mBAAmB,EACnB,cAAc,EACd,wBAAwB,EACxB,mCAAmC,EACnC,mBAAmB,EACnB,WAAW,EACX,WAAW,EACX,kBAAkB,EAClB,gBAAgB,CACjB;AAED,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACrB,MAAM;IAAEC,IAAI;IAAEC;EAAM,CAAC,GAAG5B,OAAO,CAAC,CAAC;EACjC,MAAM6B,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwC,KAAK,EAAEC,QAAQ,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0C,eAAe,EAAEC,kBAAkB,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8C,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACgD,eAAe,EAAEC,kBAAkB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAC;IAAE0D,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAU,CAAC,CAAC;EAC/F,MAAMC,YAAY,GAAG3D,MAAM,CAAC,IAAI,CAAC;EAEjC,MAAM,CAAC4D,QAAQ,EAAEC,WAAW,CAAC,GAAG/D,QAAQ,CAAC;IACvCgE,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,EAAE;IACVC,GAAG,EAAE,EAAE;IACPC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE;MACX9C,eAAe,EAAE,EAAE;MACnB+C,wBAAwB,EAAE,EAAE;MAC5BC,kBAAkB,EAAE,EAAE;MACtBC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EAEFzE,SAAS,CAAC,MAAM;IACd,MAAM0E,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI,CAAC1C,IAAI,IAAI,CAACC,KAAK,EAAE;QACnBO,QAAQ,CAAC,iCAAiC,CAAC;QAC3CF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MACA,IAAI;QACF,MAAMqC,MAAM,GAAG;UAAEC,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAU5C,KAAK;UAAG;QAAE,CAAC;QAChE,MAAM6C,QAAQ,GAAG,MAAM1E,KAAK,CAAC2E,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,eAAe,EAAEP,MAAM,CAAC;QACzF,IAAIQ,KAAK,CAACC,OAAO,CAACN,QAAQ,CAACO,IAAI,CAAC,EAAE;UAChC,MAAMC,aAAa,GAAGR,QAAQ,CAACO,IAAI,CAACE,MAAM,CAACC,OAAO,IAChDA,OAAO,IACP,OAAOA,OAAO,KAAK,QAAQ,IAC3BA,OAAO,CAACzB,UAAU,IAClByB,OAAO,CAACxB,QACV,CAAC;UACD5B,WAAW,CAACkD,aAAa,CAAC;QAC5B,CAAC,MAAM;UACL9C,QAAQ,CAAC,2CAA2C,CAAC;UACrDJ,WAAW,CAAC,EAAE,CAAC;QACjB;MACF,CAAC,CAAC,OAAOqD,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZC,OAAO,CAACvD,KAAK,CAAC,cAAc,GAAAmD,aAAA,GAAED,GAAG,CAACX,QAAQ,cAAAY,aAAA,uBAAZA,aAAA,CAAcK,MAAM,GAAAJ,cAAA,GAAEF,GAAG,CAACX,QAAQ,cAAAa,cAAA,uBAAZA,cAAA,CAAcN,IAAI,CAAC;QACvE7C,QAAQ,CAAC,EAAAoD,cAAA,GAAAH,GAAG,CAACX,QAAQ,cAAAc,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcP,IAAI,cAAAQ,mBAAA,uBAAlBA,mBAAA,CAAoBnC,OAAO,KAAI,yBAAyB,CAAC;QAClEtB,WAAW,CAAC,EAAE,CAAC;MACjB,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDoC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAAC1C,IAAI,EAAEC,KAAK,CAAC,CAAC;EAEjBjC,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAAC2C,SAAS,IAAI,CAACE,eAAe,IAAI,CAACE,eAAe,EAAE;MACtDL,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC,EAAE,CAACC,SAAS,EAAEE,eAAe,EAAEE,eAAe,CAAC,CAAC;EAEjD,MAAMiD,iBAAiB,GAAGA,CAACC,CAAC,EAAET,OAAO,EAAE7B,IAAI,KAAK;IAC9C;IACAsC,CAAC,CAACC,eAAe,CAAC,CAAC;IAEnBxD,kBAAkB,CAAC8C,OAAO,CAAC;IAC3BtC,aAAa,CAACS,IAAI,CAAC;IACnBX,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMmD,gBAAgB,GAAG,MAAOF,CAAC,IAAK;IACpC,MAAMG,KAAK,GAAGH,CAAC,CAACI,MAAM,CAACD,KAAK;IAC5B,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;IAElC,MAAMzC,QAAQ,GAAG,IAAI0C,QAAQ,CAAC,CAAC;IAC/B,MAAMC,SAAS,GAAGvD,UAAU,KAAK,MAAM,GAAG,OAAO,GAAG,eAAe;IACnEkC,KAAK,CAACsB,IAAI,CAACL,KAAK,CAAC,CAACM,OAAO,CAACC,IAAI,IAAI9C,QAAQ,CAAC+C,MAAM,CAACJ,SAAS,EAAEG,IAAI,CAAC,CAAC;IAEnE,IAAI;MACF,MAAME,QAAQ,GAAG5D,UAAU,KAAK,MAAM,GAClC,GAAG+B,OAAO,CAACC,GAAG,CAACC,iBAAiB,iBAAiBzC,eAAe,CAACsB,UAAU,QAAQ,GACnF,GAAGiB,OAAO,CAACC,GAAG,CAACC,iBAAiB,iBAAiBzC,eAAe,CAACsB,UAAU,UAAU;MAEzF,MAAMe,QAAQ,GAAG,MAAM1E,KAAK,CAAC0G,IAAI,CAACD,QAAQ,EAAEhD,QAAQ,EAAE;QACpDe,OAAO,EAAE;UACPC,aAAa,EAAE,UAAU5C,KAAK,EAAE;UAChC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI6C,QAAQ,CAACO,IAAI,EAAE;QACjB;QACA7B,eAAe,CAAC;UACdC,IAAI,EAAE,IAAI;UACVC,OAAO,EAAE,GAAGT,UAAU,KAAK,MAAM,GAAG,QAAQ,GAAG,gBAAgB,yBAAyB;UACxFU,IAAI,EAAE;QACR,CAAC,CAAC;;QAEF;QACAoD,UAAU,CAAC,MAAM;UACfvD,eAAe,CAAC;YAAEC,IAAI,EAAE,KAAK;YAAEC,OAAO,EAAE,EAAE;YAAEC,IAAI,EAAE;UAAU,CAAC,CAAC;QAChE,CAAC,EAAE,IAAI,CAAC;QAERX,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOyC,GAAG,EAAE;MAAA,IAAAuB,cAAA,EAAAC,mBAAA;MACZnB,OAAO,CAACvD,KAAK,CAAC,eAAe,EAAEkD,GAAG,CAAC;MACnCjD,QAAQ,CAAC,EAAAwE,cAAA,GAAAvB,GAAG,CAACX,QAAQ,cAAAkC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc3B,IAAI,cAAA4B,mBAAA,uBAAlBA,mBAAA,CAAoBvD,OAAO,KAAI,oBAAoBT,UAAU,KAAK,MAAM,GAAG,QAAQ,GAAG,gBAAgB,EAAE,CAAC;IACpH;EACF,CAAC;EAED,MAAMiE,YAAY,GAAIjB,CAAC,IAAK3C,aAAa,CAAC2C,CAAC,CAACI,MAAM,CAACc,KAAK,CAAC;EAEzD,MAAMC,gBAAgB,GAAGjF,QAAQ,CAACoD,MAAM,CAACC,OAAO,IAAI;IAClD,IAAI,CAACA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE,OAAO,KAAK;IACzD,MAAMxB,QAAQ,GAAGwB,OAAO,CAACxB,QAAQ,IAAI,EAAE;IACvC,MAAMD,UAAU,GAAGyB,OAAO,CAACzB,UAAU,IAAI,EAAE;IAC3C,MAAME,WAAW,GAAGuB,OAAO,CAACvB,WAAW,IAAI,EAAE;IAC7C,OACED,QAAQ,CAACqD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjE,UAAU,CAACgE,WAAW,CAAC,CAAC,CAAC,IACzDtD,UAAU,CAACuD,QAAQ,CAACjE,UAAU,CAAC,IAC/BY,WAAW,CAACqD,QAAQ,CAACjE,UAAU,CAAC;EAEpC,CAAC,CAAC;EAEF,MAAMkE,iBAAiB,GAAItB,CAAC,IAAK;IAC/B,MAAM;MAAEuB,IAAI;MAAEL;IAAM,CAAC,GAAGlB,CAAC,CAACI,MAAM;IAChC,IAAImB,IAAI,KAAK,gBAAgB,EAAE;MAC7B1D,WAAW,CAAC2D,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPnD,WAAW,EAAE;UAAE,GAAGmD,IAAI,CAACnD,WAAW;UAAEG,cAAc,EAAE0C;QAAM;MAC5D,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLrD,WAAW,CAAC2D,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACD,IAAI,GAAGL;MAAM,CAAC,CAAC,CAAC;IACnD;EACF,CAAC;EAED,MAAMO,uBAAuB,GAAIzB,CAAC,IAAK;IACrC,MAAM;MAAEuB,IAAI;MAAEL;IAAM,CAAC,GAAGlB,CAAC,CAACI,MAAM;IAChCvC,WAAW,CAAC2D,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPnD,WAAW,EAAE;QAAE,GAAGmD,IAAI,CAACnD,WAAW;QAAE,CAACkD,IAAI,GAAGL;MAAM;IACpD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMQ,0BAA0B,GAAIC,OAAO,IAAK;IAC9C9D,WAAW,CAAC2D,IAAI,IAAI;MAClB,MAAMI,QAAQ,GAAGJ,IAAI,CAACnD,WAAW,CAAC9C,eAAe,CAAC8F,QAAQ,CAACM,OAAO,CAAC,GAC/DH,IAAI,CAACnD,WAAW,CAAC9C,eAAe,CAAC+D,MAAM,CAACuC,CAAC,IAAIA,CAAC,KAAKF,OAAO,CAAC,GAC3D,CAAC,GAAGH,IAAI,CAACnD,WAAW,CAAC9C,eAAe,EAAEoG,OAAO,CAAC;MAClD,OAAO;QACL,GAAGH,IAAI;QACPnD,WAAW,EAAE;UAAE,GAAGmD,IAAI,CAACnD,WAAW;UAAE9C,eAAe,EAAEqG;QAAS;MAChE,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,iBAAiB,GAAIvC,OAAO,IAAK;IACrC;IACAtD,QAAQ,CAAC,mBAAmBsD,OAAO,CAACzB,UAAU,UAAU,CAAC;EAC3D,CAAC;EAED,MAAMiE,gBAAgB,GAAGA,CAAA,KAAM;IAC7BlE,WAAW,CAAC;MACVC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,EAAE;MACfC,MAAM,EAAE,EAAE;MACVC,GAAG,EAAE,EAAE;MACPC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE;QACX9C,eAAe,EAAE,EAAE;QACnB+C,wBAAwB,EAAE,EAAE;QAC5BC,kBAAkB,EAAE,EAAE;QACtBC,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF/B,kBAAkB,CAAC,IAAI,CAAC;IACxBE,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMqF,iBAAiB,GAAIzC,OAAO,IAAK;IACrC1B,WAAW,CAAC;MACVC,UAAU,EAAEyB,OAAO,CAACzB,UAAU;MAC9BC,QAAQ,EAAEwB,OAAO,CAACxB,QAAQ;MAC1BC,WAAW,EAAEuB,OAAO,CAACvB,WAAW;MAChCC,MAAM,EAAEsB,OAAO,CAACtB,MAAM;MACtBC,GAAG,EAAEqB,OAAO,CAACrB,GAAG;MAChBC,OAAO,EAAEoB,OAAO,CAACpB,OAAO,IAAI,EAAE;MAC9BC,UAAU,EAAEmB,OAAO,CAACnB,UAAU,IAAI,EAAE;MACpCC,WAAW,EAAEkB,OAAO,CAAClB,WAAW,IAAI;QAClC9C,eAAe,EAAE,EAAE;QACnB+C,wBAAwB,EAAE,EAAE;QAC5BC,kBAAkB,EAAE,EAAE;QACtBC,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF/B,kBAAkB,CAAC8C,OAAO,CAAC;IAC3B5C,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMsF,YAAY,GAAG,MAAOjC,CAAC,IAAK;IAChCA,CAAC,CAACkC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMxD,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAU5C,KAAK;QAAG;MAAE,CAAC;MAChE,MAAMmG,WAAW,GAAG;QAClB,GAAGvE,QAAQ;QACXwE,IAAI,EAAErG,IAAI,CAACsG,SAAS,CAAE;MACxB,CAAC;MAEDxC,OAAO,CAACyC,GAAG,CAAC,sCAAsC,EAAEvG,IAAI,CAACsG,SAAS,CAAC;MAEnE,IAAI7F,eAAe,EAAE;QACnB,MAAMqC,QAAQ,GAAG,MAAM1E,KAAK,CAACoI,GAAG,CAC9B,GAAGxD,OAAO,CAACC,GAAG,CAACC,iBAAiB,iBAAiBzC,eAAe,CAACsB,UAAU,EAAE,EAC7EqE,WAAW,EACXzD,MACF,CAAC;QACD,IAAIG,QAAQ,CAACO,IAAI,IAAIP,QAAQ,CAACO,IAAI,CAACG,OAAO,EAAE;UAC1CpD,WAAW,CAACD,QAAQ,CAACsG,GAAG,CAACC,CAAC,IACxBA,CAAC,CAAC3E,UAAU,KAAKtB,eAAe,CAACsB,UAAU,GAAGe,QAAQ,CAACO,IAAI,CAACG,OAAO,GAAGkD,CACxE,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,MAAM,IAAIC,KAAK,CAAC,2CAA2C,CAAC;QAC9D;QACA/F,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,MAAM;QACL,MAAMkC,QAAQ,GAAG,MAAM1E,KAAK,CAAC0G,IAAI,CAC/B,GAAG9B,OAAO,CAACC,GAAG,CAACC,iBAAiB,eAAe,EAC/CkD,WAAW,EACXzD,MACF,CAAC;QACD,IAAIG,QAAQ,CAACO,IAAI,IAAIP,QAAQ,CAACO,IAAI,CAACG,OAAO,EAAE;UAC1CpD,WAAW,CAACwG,YAAY,IAAI,CAAC,GAAGA,YAAY,EAAE9D,QAAQ,CAACO,IAAI,CAACG,OAAO,CAAC,CAAC;UACrE5C,YAAY,CAAC,KAAK,CAAC;UACnBmE,UAAU,CAAC,MAAM;YACf7E,QAAQ,CAAC,mBAAmB4C,QAAQ,CAACO,IAAI,CAACG,OAAO,CAACzB,UAAU,EAAE,CAAC;UACjE,CAAC,EAAE,CAAC,CAAC;QACP,CAAC,MAAM;UACL,MAAM,IAAI4E,KAAK,CAAC,2CAA2C,CAAC;QAC9D;MACF;IACF,CAAC,CAAC,OAAOlD,GAAG,EAAE;MAAA,IAAAoD,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;MACZlD,OAAO,CAACvD,KAAK,CAAC,eAAe,GAAAsG,cAAA,GAAEpD,GAAG,CAACX,QAAQ,cAAA+D,cAAA,uBAAZA,cAAA,CAAc9C,MAAM,GAAA+C,cAAA,GAAErD,GAAG,CAACX,QAAQ,cAAAgE,cAAA,uBAAZA,cAAA,CAAczD,IAAI,CAAC;MACxE7C,QAAQ,CAAC,EAAAuG,cAAA,GAAAtD,GAAG,CAACX,QAAQ,cAAAiE,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc1D,IAAI,cAAA2D,mBAAA,uBAAlBA,mBAAA,CAAoBtF,OAAO,KAAI,wBAAwB,CAAC;IACnE;EACF,CAAC;EAED,MAAMuF,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMtE,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAU5C,KAAK;QAAG;MAAE,CAAC;MAChE,MAAM7B,KAAK,CAAC8I,MAAM,CAChB,GAAGlE,OAAO,CAACC,GAAG,CAACC,iBAAiB,iBAAiBzC,eAAe,CAACsB,UAAU,EAAE,EAC7EY,MACF,CAAC;MACDvC,WAAW,CAACD,QAAQ,CAACoD,MAAM,CAACmD,CAAC,IAAIA,CAAC,CAAC3E,UAAU,KAAKtB,eAAe,CAACsB,UAAU,CAAC,CAAC;MAC9EjB,kBAAkB,CAAC,KAAK,CAAC;MACzBJ,kBAAkB,CAAC,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAO+C,GAAG,EAAE;MAAA,IAAA0D,cAAA,EAAAC,cAAA,EAAAC,eAAA,EAAAC,oBAAA;MACZxD,OAAO,CAACvD,KAAK,CAAC,eAAe,GAAA4G,cAAA,GAAE1D,GAAG,CAACX,QAAQ,cAAAqE,cAAA,uBAAZA,cAAA,CAAcpD,MAAM,GAAAqD,cAAA,GAAE3D,GAAG,CAACX,QAAQ,cAAAsE,cAAA,uBAAZA,cAAA,CAAc/D,IAAI,CAAC;MACxE7C,QAAQ,CAAC,EAAA6G,eAAA,GAAA5D,GAAG,CAACX,QAAQ,cAAAuE,eAAA,wBAAAC,oBAAA,GAAZD,eAAA,CAAchE,IAAI,cAAAiE,oBAAA,uBAAlBA,oBAAA,CAAoB5F,OAAO,KAAI,0BAA0B,CAAC;IACrE;EACF,CAAC;;EAED;EACA,MAAM6F,SAAS,GAAG;IAChBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBhG,IAAI,EAAE;MACJgG,OAAO,EAAE,CAAC;MACVC,UAAU,EAAE;QACVC,eAAe,EAAE;MACnB;IACF;EACF,CAAC;EAED,MAAMC,IAAI,GAAG;IACXJ,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEI,CAAC,EAAE;IAAG,CAAC;IAC7BpG,IAAI,EAAE;MAAEgG,OAAO,EAAE,CAAC;MAAEI,CAAC,EAAE;IAAE;EAC3B,CAAC;EAED,IAAIxH,OAAO,EAAE;IACX,oBAAOd,OAAA,CAACf,MAAM;MAAAsJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnB;EAEA,IAAI1H,KAAK,EAAE;IACT,oBACEhB,OAAA;MAAK2I,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvC5I,OAAA,CAAChB,OAAO;QAAC6J,MAAM,EAAEjH,WAAY;QAACkH,SAAS,EAAEjH;MAAe;QAAA0G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3D1I,OAAA;QAAK2I,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnD5I,OAAA,CAACjB,MAAM;UAACgK,aAAa,EAAEA,CAAA,KAAMlH,cAAc,CAAC,CAACD,WAAW;QAAE;UAAA2G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7D1I,OAAA;UAAM2I,SAAS,EAAC,4GAA4G;UAAAC,QAAA,eAC1H5I,OAAA,CAACd,MAAM,CAAC8J,GAAG;YACTC,OAAO,EAAE;cAAEC,KAAK,EAAE,GAAG;cAAEhB,OAAO,EAAE;YAAE,CAAE;YACpCiB,OAAO,EAAE;cAAED,KAAK,EAAE,CAAC;cAAEhB,OAAO,EAAE;YAAE,CAAE;YAClCS,SAAS,EAAC,+EAA+E;YAAAC,QAAA,gBAEzF5I,OAAA;cAAK2I,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChC5I,OAAA;gBAAKoJ,KAAK,EAAC,4BAA4B;gBAACT,SAAS,EAAC,mBAAmB;gBAACU,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAAX,QAAA,eACzH5I,OAAA;kBAAMwJ,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACnD,CAAC,EAAC;gBAAsI;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3M;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN1I,OAAA;cAAI2I,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAC;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/D1I,OAAA;cAAG2I,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAE5H;YAAK;cAAAuH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7C1I,OAAA,CAACd,MAAM,CAACyK,MAAM;cACZC,UAAU,EAAE;gBAAEV,KAAK,EAAE;cAAK,CAAE;cAC5BW,QAAQ,EAAE;gBAAEX,KAAK,EAAE;cAAK,CAAE;cAC1BY,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;cACxCtB,SAAS,EAAC,0IAA0I;cAAAC,QAAA,EACrJ;YAED;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE1I,OAAA;IAAK2I,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvC5I,OAAA,CAAChB,OAAO;MAAC6J,MAAM,EAAEjH,WAAY;MAACkH,SAAS,EAAEjH;IAAe;MAAA0G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE3D1I,OAAA;MAAK2I,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnD5I,OAAA,CAACjB,MAAM;QAACgK,aAAa,EAAEA,CAAA,KAAMlH,cAAc,CAAC,CAACD,WAAW;MAAE;QAAA2G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE7D1I,OAAA;QAAM2I,SAAS,EAAC,0FAA0F;QAAAC,QAAA,eACxG5I,OAAA;UAAK2I,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChC5I,OAAA,CAACd,MAAM,CAAC8J,GAAG;YACTC,OAAO,EAAE;cAAEf,OAAO,EAAE;YAAE,CAAE;YACxBiB,OAAO,EAAE;cAAEjB,OAAO,EAAE;YAAE,CAAE;YACxBC,UAAU,EAAE;cAAE+B,QAAQ,EAAE;YAAI,CAAE;YAAAtB,QAAA,gBAE9B5I,OAAA;cAAK2I,SAAS,EAAC,kFAAkF;cAAAC,QAAA,gBAC/F5I,OAAA;gBAAA4I,QAAA,gBACE5I,OAAA;kBAAI2I,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAAC;gBAEnE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1I,OAAA;kBAAG2I,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAA2B;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACN1I,OAAA;gBAAK2I,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,eAC5E5I,OAAA,CAACd,MAAM,CAAC8J,GAAG;kBACTY,UAAU,EAAE;oBAAEV,KAAK,EAAE;kBAAK,CAAE;kBAC5BP,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBAEnC5I,OAAA;oBAAK2I,SAAS,EAAC,sEAAsE;oBAAAC,QAAA,eACnF5I,OAAA,CAACX,QAAQ;sBAACsJ,SAAS,EAAC;oBAAuB;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,eACN1I,OAAA;oBACEoC,IAAI,EAAC,MAAM;oBACX+H,WAAW,EAAC,oBAAoB;oBAChCvE,KAAK,EAAE9D,UAAW;oBAClBsI,QAAQ,EAAEzE,YAAa;oBACvBgD,SAAS,EAAC;kBAAuN;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1I,OAAA,CAACd,MAAM,CAAC8J,GAAG;cACTqB,QAAQ,EAAErC,SAAU;cACpBiB,OAAO,EAAC,QAAQ;cAChBE,OAAO,EAAC,MAAM;cACdR,SAAS,EAAC,0HAA0H;cAAAC,QAAA,EAEnI/C,gBAAgB,CAACd,MAAM,GAAG,CAAC,gBAC1B/E,OAAA;gBAAK2I,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,eAC9B5I,OAAA;kBAAO2I,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBACpD5I,OAAA;oBAAO2I,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,eAC1C5I,OAAA;sBAAA4I,QAAA,gBACE5I,OAAA;wBAAI2I,SAAS,EAAC,iFAAiF;wBAAAC,QAAA,EAAC;sBAAO;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC5G1I,OAAA;wBAAI2I,SAAS,EAAC,iFAAiF;wBAAAC,QAAA,EAAC;sBAAW;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAChH1I,OAAA;wBAAI2I,SAAS,EAAC,iFAAiF;wBAAAC,QAAA,EAAC;sBAAK;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC1G1I,OAAA;wBAAI2I,SAAS,EAAC,iFAAiF;wBAAAC,QAAA,EAAC;sBAAM;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC3G1I,OAAA;wBAAI2I,SAAS,EAAC,iFAAiF;wBAAAC,QAAA,EAAC;sBAAG;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACxG1I,OAAA;wBAAI2I,SAAS,EAAC,kFAAkF;wBAAAC,QAAA,EAAC;sBAAO;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3G;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACR1I,OAAA;oBAAO2I,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EACjD/C,gBAAgB,CAACqB,GAAG,CAAEjD,OAAO,iBAC5BjE,OAAA,CAACd,MAAM,CAACoL,EAAE;sBAERD,QAAQ,EAAEhC,IAAK;sBACfM,SAAS,EAAC,gDAAgD;sBAC1DmB,OAAO,EAAEA,CAAA,KAAMtD,iBAAiB,CAACvC,OAAO,CAAE;sBAAA2E,QAAA,gBAE1C5I,OAAA;wBAAI2I,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,eACzC5I,OAAA;0BAAK2I,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,gBAChC5I,OAAA;4BAAK2I,SAAS,EAAC,+GAA+G;4BAAAC,QAAA,eAC5H5I,OAAA,CAACb,MAAM;8BAACwJ,SAAS,EAAC;4BAAS;8BAAAJ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3B,CAAC,eACN1I,OAAA;4BAAK2I,SAAS,EAAC,MAAM;4BAAAC,QAAA,eACnB5I,OAAA;8BAAK2I,SAAS,EAAC,oCAAoC;8BAAAC,QAAA,EAAE3E,OAAO,CAACxB;4BAAQ;8BAAA8F,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACL1I,OAAA;wBAAI2I,SAAS,EAAC,mDAAmD;wBAAAC,QAAA,EAAE3E,OAAO,CAACzB;sBAAU;wBAAA+F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC3F1I,OAAA;wBAAI2I,SAAS,EAAC,mDAAmD;wBAAAC,QAAA,EAAE3E,OAAO,CAACvB;sBAAW;wBAAA6F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC5F1I,OAAA;wBAAI2I,SAAS,EAAC,mDAAmD;wBAAAC,QAAA,EAAE3E,OAAO,CAACtB;sBAAM;wBAAA4F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACvF1I,OAAA;wBAAI2I,SAAS,EAAC,mDAAmD;wBAAAC,QAAA,EAAE3E,OAAO,CAACrB;sBAAG;wBAAA2F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACpF1I,OAAA;wBAAI2I,SAAS,EAAC,4DAA4D;wBAAAC,QAAA,gBACxE5I,OAAA;0BACE8J,OAAO,EAAGpF,CAAC,IAAK;4BACdA,CAAC,CAACC,eAAe,CAAC,CAAC;4BACnB+B,iBAAiB,CAACzC,OAAO,CAAC;0BAC5B,CAAE;0BACF0E,SAAS,EAAC,0CAA0C;0BACpD4B,KAAK,EAAC,cAAc;0BAAA3B,QAAA,eAEpB5I,OAAA,CAACV,MAAM;4BAACqJ,SAAS,EAAC;0BAAQ;4BAAAJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvB,CAAC,eACT1I,OAAA;0BACE8J,OAAO,EAAGpF,CAAC,IAAKD,iBAAiB,CAACC,CAAC,EAAET,OAAO,EAAE,MAAM,CAAE;0BACtD0E,SAAS,EAAC,0CAA0C;0BACpD4B,KAAK,EAAC,eAAe;0BAAA3B,QAAA,eAErB5I,OAAA,CAACJ,MAAM;4BAAC+I,SAAS,EAAC;0BAAQ;4BAAAJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvB,CAAC,eACT1I,OAAA;0BACE8J,OAAO,EAAGpF,CAAC,IAAKD,iBAAiB,CAACC,CAAC,EAAET,OAAO,EAAE,SAAS,CAAE;0BACzD0E,SAAS,EAAC,4CAA4C;0BACtD4B,KAAK,EAAC,uBAAuB;0BAAA3B,QAAA,eAE7B5I,OAAA,CAACH,QAAQ;4BAAC8I,SAAS,EAAC;0BAAQ;4BAAAJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzB,CAAC,eACT1I,OAAA;0BACE8J,OAAO,EAAGpF,CAAC,IAAK;4BACdA,CAAC,CAACC,eAAe,CAAC,CAAC;4BACnBxD,kBAAkB,CAAC8C,OAAO,CAAC;4BAC3B1C,kBAAkB,CAAC,IAAI,CAAC;0BAC1B,CAAE;0BACFoH,SAAS,EAAC,iCAAiC;0BAC3C4B,KAAK,EAAC,gBAAgB;0BAAA3B,QAAA,eAEtB5I,OAAA,CAACT,OAAO;4BAACoJ,SAAS,EAAC;0BAAQ;4BAAAJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACP,CAAC;oBAAA,GAvDAzE,OAAO,CAACzB,UAAU;sBAAA+F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAwDd,CACZ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,gBAEN1I,OAAA,CAACd,MAAM,CAAC8J,GAAG;gBACTC,OAAO,EAAE;kBAAEf,OAAO,EAAE;gBAAE,CAAE;gBACxBiB,OAAO,EAAE;kBAAEjB,OAAO,EAAE;gBAAE,CAAE;gBACxBS,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAE7B5I,OAAA;kBAAK2I,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,eACnD5I,OAAA;oBAAKoJ,KAAK,EAAC,4BAA4B;oBAACC,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,MAAM,EAAC,cAAc;oBAAAX,QAAA,eAC3F5I,OAAA;sBAAMwJ,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACnD,CAAC,EAAC;oBAAoF;sBAAAgC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN1I,OAAA;kBAAI2I,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3E1I,OAAA;kBAAG2I,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EACtC9G,UAAU,GAAG,6BAA6B,GAAG;gBAA2C;kBAAAyG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM;YACb;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAELxH,eAAe,IAAI,CAACE,SAAS,IAAI,CAACE,eAAe,IAAI,CAACE,eAAe,iBACpExB,OAAA;MAAK2I,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F5I,OAAA,CAACd,MAAM,CAAC8J,GAAG;QACTC,OAAO,EAAE;UAAEC,KAAK,EAAE,GAAG;UAAEhB,OAAO,EAAE;QAAE,CAAE;QACpCiB,OAAO,EAAE;UAAED,KAAK,EAAE,CAAC;UAAEhB,OAAO,EAAE;QAAE,CAAE;QAClCS,SAAS,EAAC,yGAAyG;QAAAC,QAAA,gBAEnH5I,OAAA;UAAK2I,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD5I,OAAA;YAAI2I,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAE1H,eAAe,CAACuB;UAAQ;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACjF1I,OAAA;YACE8J,OAAO,EAAEA,CAAA,KAAM3I,kBAAkB,CAAC,IAAI,CAAE;YACxCwH,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAE7C5I,OAAA;cAAKoJ,KAAK,EAAC,4BAA4B;cAACT,SAAS,EAAC,SAAS;cAACU,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAX,QAAA,eAC/G5I,OAAA;gBAAMwJ,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACnD,CAAC,EAAC;cAAsB;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN1I,OAAA;UAAK2I,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB5I,OAAA;YAAK2I,SAAS,EAAC,8EAA8E;YAAAC,QAAA,gBAC3F5I,OAAA;cAAI2I,SAAS,EAAC,6DAA6D;cAAAC,QAAA,gBACzE5I,OAAA,CAACb,MAAM;gBAACwJ,SAAS,EAAC;cAA6B;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wBAEpD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1I,OAAA;cAAK2I,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD5I,OAAA;gBAAA4I,QAAA,gBAAK5I,OAAA;kBAAI2I,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAW;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAAA1I,OAAA;kBAAG2I,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAE1H,eAAe,CAACsB;gBAAU;kBAAA+F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvJ1I,OAAA;gBAAA4I,QAAA,gBAAK5I,OAAA;kBAAI2I,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAK;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAAA1I,OAAA;kBAAG2I,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAE1H,eAAe,CAACwB;gBAAW;kBAAA6F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClJ1I,OAAA;gBAAA4I,QAAA,gBAAK5I,OAAA;kBAAI2I,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAM;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAAA1I,OAAA;kBAAG2I,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAE1H,eAAe,CAACyB;gBAAM;kBAAA4F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9I1I,OAAA;gBAAA4I,QAAA,gBAAK5I,OAAA;kBAAI2I,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAG;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAAA1I,OAAA;kBAAG2I,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAE1H,eAAe,CAAC0B;gBAAG;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxI1I,OAAA;gBAAA4I,QAAA,gBAAK5I,OAAA;kBAAI2I,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAO;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAAA1I,OAAA;kBAAG2I,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAE1H,eAAe,CAAC2B,OAAO,IAAI;gBAAc;kBAAA0F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClK1I,OAAA;gBAAA4I,QAAA,gBAAK5I,OAAA;kBAAI2I,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAU;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAAA1I,OAAA;kBAAG2I,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAE1H,eAAe,CAAC4B,UAAU,IAAI;gBAAc;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN1I,OAAA;YAAK2I,SAAS,EAAC,8EAA8E;YAAAC,QAAA,gBAC3F5I,OAAA;cAAI2I,SAAS,EAAC,6DAA6D;cAAAC,QAAA,gBACzE5I,OAAA,CAACR,aAAa;gBAACmJ,SAAS,EAAC;cAA6B;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAE3D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1I,OAAA;cAAK2I,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB5I,OAAA;gBAAA4I,QAAA,gBAAK5I,OAAA;kBAAI2I,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAAA1I,OAAA;kBAAG2I,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAE,EAAAxI,qBAAA,GAAAc,eAAe,CAAC6B,WAAW,cAAA3C,qBAAA,wBAAAC,sBAAA,GAA3BD,qBAAA,CAA6BH,eAAe,cAAAI,sBAAA,uBAA5CA,sBAAA,CAA8CmK,IAAI,CAAC,IAAI,CAAC,KAAI;gBAAM;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpM1I,OAAA;gBAAA4I,QAAA,gBAAK5I,OAAA;kBAAI2I,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAA0B;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAAA1I,OAAA;kBAAG2I,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAE,EAAAtI,sBAAA,GAAAY,eAAe,CAAC6B,WAAW,cAAAzC,sBAAA,uBAA3BA,sBAAA,CAA6B0C,wBAAwB,KAAI;gBAAM;kBAAAuF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3M1I,OAAA;gBAAA4I,QAAA,gBAAK5I,OAAA;kBAAI2I,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAmB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAAA1I,OAAA;kBAAG2I,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAE,EAAArI,sBAAA,GAAAW,eAAe,CAAC6B,WAAW,cAAAxC,sBAAA,uBAA3BA,sBAAA,CAA6B0C,kBAAkB,KAAI;gBAAM;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3L,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN1I,OAAA;YAAK2I,SAAS,EAAC,8EAA8E;YAAAC,QAAA,gBAC3F5I,OAAA;cAAI2I,SAAS,EAAC,6DAA6D;cAAAC,QAAA,gBACzE5I,OAAA,CAACP,YAAY;gBAACkJ,SAAS,EAAC;cAA6B;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAE1D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1I,OAAA;cAAG2I,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAE,EAAApI,sBAAA,GAAAU,eAAe,CAAC6B,WAAW,cAAAvC,sBAAA,uBAA3BA,sBAAA,CAA6B0C,cAAc,KAAI;YAAM;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F,CAAC,eACN1I,OAAA;YAAK2I,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzC5I,OAAA,CAACd,MAAM,CAACyK,MAAM;cACZC,UAAU,EAAE;gBAAEV,KAAK,EAAE;cAAK,CAAE;cAC5BW,QAAQ,EAAE;gBAAEX,KAAK,EAAE;cAAK,CAAE;cAC1BY,OAAO,EAAEA,CAAA,KAAM;gBACb3I,kBAAkB,CAAC,IAAI,CAAC;gBACxBuF,iBAAiB,CAACxF,eAAe,CAAC;cACpC,CAAE;cACFyH,SAAS,EAAC,uIAAuI;cAAAC,QAAA,EAClJ;YAED;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChB1I,OAAA,CAACd,MAAM,CAACyK,MAAM;cACZC,UAAU,EAAE;gBAAEV,KAAK,EAAE;cAAK,CAAE;cAC5BW,QAAQ,EAAE;gBAAEX,KAAK,EAAE;cAAK,CAAE;cAC1BY,OAAO,EAAEA,CAAA,KAAMnJ,QAAQ,CAAC,mBAAmBO,eAAe,CAACsB,UAAU,EAAE,CAAE;cACzEmG,SAAS,EAAC,kJAAkJ;cAAAC,QAAA,EAC7J;YAED;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,EAEAtH,SAAS,iBACRpB,OAAA;MAAK2I,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F5I,OAAA,CAACd,MAAM,CAAC8J,GAAG;QACTC,OAAO,EAAE;UAAEC,KAAK,EAAE,GAAG;UAAEhB,OAAO,EAAE;QAAE,CAAE;QACpCiB,OAAO,EAAE;UAAED,KAAK,EAAE,CAAC;UAAEhB,OAAO,EAAE;QAAE,CAAE;QAClCS,SAAS,EAAC,yGAAyG;QAAAC,QAAA,gBAEnH5I,OAAA;UAAK2I,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD5I,OAAA;YAAI2I,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC9C1H,eAAe,GAAG,cAAc,GAAG;UAAa;YAAAqH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACL1I,OAAA;YACE8J,OAAO,EAAEA,CAAA,KAAMzI,YAAY,CAAC,KAAK,CAAE;YACnCsH,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAE7C5I,OAAA;cAAKoJ,KAAK,EAAC,4BAA4B;cAACT,SAAS,EAAC,SAAS;cAACU,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAX,QAAA,eAC/G5I,OAAA;gBAAMwJ,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACnD,CAAC,EAAC;cAAsB;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN1I,OAAA;UAAMyK,QAAQ,EAAE9D,YAAa;UAACgC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACjD5I,OAAA;YAAI2I,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EAAC;UAAoB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9H1I,OAAA;YAAK2I,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD5I,OAAA;cAAA4I,QAAA,gBACE5I,OAAA;gBAAO2I,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAAU;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpF1I,OAAA;gBACEoC,IAAI,EAAC,MAAM;gBACX6D,IAAI,EAAC,UAAU;gBACfL,KAAK,EAAEtD,QAAQ,CAACG,QAAS;gBACzB2H,QAAQ,EAAEpE,iBAAkB;gBAC5B2C,SAAS,EAAC,6GAA6G;gBACvH+B,QAAQ;cAAA;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN1I,OAAA;cAAA4I,QAAA,gBACE5I,OAAA;gBAAO2I,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAAY;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtF1I,OAAA;gBACEoC,IAAI,EAAC,MAAM;gBACX6D,IAAI,EAAC,YAAY;gBACjBL,KAAK,EAAEtD,QAAQ,CAACE,UAAW;gBAC3B4H,QAAQ,EAAEpE,iBAAkB;gBAC5B2C,SAAS,EAAC,6GAA6G;gBACvH+B,QAAQ;gBACRC,QAAQ,EAAE,CAAC,CAACzJ;cAAgB;gBAAAqH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN1I,OAAA;cAAA4I,QAAA,gBACE5I,OAAA;gBAAO2I,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAAa;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvF1I,OAAA;gBACEoC,IAAI,EAAC,KAAK;gBACV6D,IAAI,EAAC,aAAa;gBAClBL,KAAK,EAAEtD,QAAQ,CAACI,WAAY;gBAC5B0H,QAAQ,EAAEpE,iBAAkB;gBAC5B2C,SAAS,EAAC,6GAA6G;gBACvH+B,QAAQ;cAAA;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN1I,OAAA;cAAA4I,QAAA,gBACE5I,OAAA;gBAAO2I,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAAO;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjF1I,OAAA;gBACEiG,IAAI,EAAC,QAAQ;gBACbL,KAAK,EAAEtD,QAAQ,CAACK,MAAO;gBACvByH,QAAQ,EAAEpE,iBAAkB;gBAC5B2C,SAAS,EAAC,6GAA6G;gBACvH+B,QAAQ;gBAAA9B,QAAA,gBAER5I,OAAA;kBAAQ4F,KAAK,EAAC,EAAE;kBAAAgD,QAAA,EAAC;gBAAM;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChC1I,OAAA;kBAAQ4F,KAAK,EAAC,MAAM;kBAAAgD,QAAA,EAAC;gBAAI;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC1I,OAAA;kBAAQ4F,KAAK,EAAC,QAAQ;kBAAAgD,QAAA,EAAC;gBAAM;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC1I,OAAA;kBAAQ4F,KAAK,EAAC,OAAO;kBAAAgD,QAAA,EAAC;gBAAK;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN1I,OAAA;cAAA4I,QAAA,gBACE5I,OAAA;gBAAO2I,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAAI;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9E1I,OAAA;gBACEoC,IAAI,EAAC,QAAQ;gBACb6D,IAAI,EAAC,KAAK;gBACVL,KAAK,EAAEtD,QAAQ,CAACM,GAAI;gBACpBwH,QAAQ,EAAEpE,iBAAkB;gBAC5B2C,SAAS,EAAC,6GAA6G;gBACvH+B,QAAQ;cAAA;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN1I,OAAA;cAAA4I,QAAA,gBACE5I,OAAA;gBAAO2I,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAAQ;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClF1I,OAAA;gBACEoC,IAAI,EAAC,MAAM;gBACX6D,IAAI,EAAC,SAAS;gBACdL,KAAK,EAAEtD,QAAQ,CAACO,OAAQ;gBACxBuH,QAAQ,EAAEpE,iBAAkB;gBAC5B2C,SAAS,EAAC,6GAA6G;gBACvH+B,QAAQ;cAAA;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN1I,OAAA;cAAA4I,QAAA,gBACE5I,OAAA;gBAAO2I,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAAW;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrF1I,OAAA;gBACEoC,IAAI,EAAC,MAAM;gBACX6D,IAAI,EAAC,YAAY;gBACjBL,KAAK,EAAEtD,QAAQ,CAACQ,UAAW;gBAC3BsH,QAAQ,EAAEpE,iBAAkB;gBAC5B2C,SAAS,EAAC,6GAA6G;gBACvH+B,QAAQ;cAAA;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1I,OAAA;YAAI2I,SAAS,EAAC,2FAA2F;YAAAC,QAAA,EAAC;UAAe;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9H1I,OAAA;YAAA4I,QAAA,gBACE5I,OAAA;cAAO2I,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAgB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxF1I,OAAA;cAAK2I,SAAS,EAAC,oHAAoH;cAAAC,QAAA,EAChI3I,eAAe,CAACiH,GAAG,CAAEb,OAAO,iBAC3BrG,OAAA;gBAAmB2I,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,eAC9C5I,OAAA;kBAAK2I,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzC5I,OAAA;oBACEoC,IAAI,EAAC,UAAU;oBACfwI,EAAE,EAAE,WAAWvE,OAAO,EAAG;oBACzBwE,OAAO,EAAEvI,QAAQ,CAACS,WAAW,CAAC9C,eAAe,CAAC8F,QAAQ,CAACM,OAAO,CAAE;oBAChE+D,QAAQ,EAAEA,CAAA,KAAMhE,0BAA0B,CAACC,OAAO,CAAE;oBACpDsC,SAAS,EAAC;kBAA8H;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzI,CAAC,eACF1I,OAAA;oBAAO8K,OAAO,EAAE,WAAWzE,OAAO,EAAG;oBAACsC,SAAS,EAAC,wGAAwG;oBAAAC,QAAA,EAAEvC;kBAAO;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvK;cAAC,GAVErC,OAAO;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWZ,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1I,OAAA;YAAK2I,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD5I,OAAA;cAAA4I,QAAA,gBACE5I,OAAA;gBAAO2I,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAqC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7G1I,OAAA;gBACEiG,IAAI,EAAC,0BAA0B;gBAC/BL,KAAK,EAAEtD,QAAQ,CAACS,WAAW,CAACC,wBAAyB;gBACrDoH,QAAQ,EAAEjE,uBAAwB;gBAClC4E,IAAI,EAAC,GAAG;gBACRpC,SAAS,EAAC;cAA6G;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN1I,OAAA;cAAA4I,QAAA,gBACE5I,OAAA;gBAAO2I,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAA8B;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtG1I,OAAA;gBACEiG,IAAI,EAAC,oBAAoB;gBACzBL,KAAK,EAAEtD,QAAQ,CAACS,WAAW,CAACE,kBAAmB;gBAC/CmH,QAAQ,EAAEjE,uBAAwB;gBAClC4E,IAAI,EAAC,GAAG;gBACRpC,SAAS,EAAC;cAA6G;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1I,OAAA;YAAI2I,SAAS,EAAC,2FAA2F;YAAAC,QAAA,EAAC;UAAe;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9H1I,OAAA;YAAA4I,QAAA,eACE5I,OAAA;cACEiG,IAAI,EAAC,gBAAgB;cACrBL,KAAK,EAAEtD,QAAQ,CAACS,WAAW,CAACG,cAAe;cAC3CkH,QAAQ,EAAEpE,iBAAkB;cAC5B+E,IAAI,EAAC,GAAG;cACRZ,WAAW,EAAC,2DAA2D;cACvExB,SAAS,EAAC,6GAA6G;cACvH+B,QAAQ;YAAA;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN1I,OAAA;YAAK2I,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9C5I,OAAA,CAACd,MAAM,CAACyK,MAAM;cACZvH,IAAI,EAAC,QAAQ;cACb0H,OAAO,EAAEA,CAAA,KAAMzI,YAAY,CAAC,KAAK,CAAE;cACnCsH,SAAS,EAAC,uIAAuI;cACjJiB,UAAU,EAAE;gBAAEV,KAAK,EAAE;cAAK,CAAE;cAC5BW,QAAQ,EAAE;gBAAEX,KAAK,EAAE;cAAK,CAAE;cAAAN,QAAA,EAC3B;YAED;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChB1I,OAAA,CAACd,MAAM,CAACyK,MAAM;cACZvH,IAAI,EAAC,QAAQ;cACbuG,SAAS,EAAC,kJAAkJ;cAC5JiB,UAAU,EAAE;gBAAEV,KAAK,EAAE;cAAK,CAAE;cAC5BW,QAAQ,EAAE;gBAAEX,KAAK,EAAE;cAAK,CAAE;cAAAN,QAAA,EAEzB1H,eAAe,GAAG,gBAAgB,GAAG;YAAa;cAAAqH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,EAEApH,eAAe,iBACdtB,OAAA;MAAK2I,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F5I,OAAA,CAACd,MAAM,CAAC8J,GAAG;QACTC,OAAO,EAAE;UAAEC,KAAK,EAAE,GAAG;UAAEhB,OAAO,EAAE;QAAE,CAAE;QACpCiB,OAAO,EAAE;UAAED,KAAK,EAAE,CAAC;UAAEhB,OAAO,EAAE;QAAE,CAAE;QAClCS,SAAS,EAAC,2EAA2E;QAAAC,QAAA,gBAErF5I,OAAA;UAAK2I,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD5I,OAAA;YAAI2I,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAgB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvE1I,OAAA;YACE8J,OAAO,EAAEA,CAAA,KAAMvI,kBAAkB,CAAC,KAAK,CAAE;YACzCoH,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAE7C5I,OAAA;cAAKoJ,KAAK,EAAC,4BAA4B;cAACT,SAAS,EAAC,SAAS;cAACU,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAX,QAAA,eAC/G5I,OAAA;gBAAMwJ,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACnD,CAAC,EAAC;cAAsB;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN1I,OAAA;UAAG2I,SAAS,EAAC,oBAAoB;UAAAC,QAAA,GAAC,kCACA,eAAA5I,OAAA;YAAM2I,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE1H,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEuB;UAAQ;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,4CACpG;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ1I,OAAA;UAAK2I,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzC5I,OAAA,CAACd,MAAM,CAACyK,MAAM;YACZC,UAAU,EAAE;cAAEV,KAAK,EAAE;YAAK,CAAE;YAC5BW,QAAQ,EAAE;cAAEX,KAAK,EAAE;YAAK,CAAE;YAC1BY,OAAO,EAAEA,CAAA,KAAMvI,kBAAkB,CAAC,KAAK,CAAE;YACzCoH,SAAS,EAAC,uIAAuI;YAAAC,QAAA,EAClJ;UAED;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,eAChB1I,OAAA,CAACd,MAAM,CAACyK,MAAM;YACZC,UAAU,EAAE;cAAEV,KAAK,EAAE;YAAK,CAAE;YAC5BW,QAAQ,EAAE;cAAEX,KAAK,EAAE;YAAK,CAAE;YAC1BY,OAAO,EAAEpC,mBAAoB;YAC7BiB,SAAS,EAAC,8IAA8I;YAAAC,QAAA,EACzJ;UAED;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,EAGA1G,YAAY,CAACE,IAAI,iBAChBlC,OAAA;MAAK2I,SAAS,EAAE,qDACd3G,YAAY,CAACI,IAAI,KAAK,SAAS,GAAG,sDAAsD,GACxFJ,YAAY,CAACI,IAAI,KAAK,OAAO,GAAG,sCAAsC,GACtE,sDAAsD,EACrD;MAAAwG,QAAA,eACD5I,OAAA;QAAK2I,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5I,OAAA;UAAK2I,SAAS,EAAE,iBACd3G,YAAY,CAACI,IAAI,KAAK,SAAS,GAAG,gBAAgB,GAClDJ,YAAY,CAACI,IAAI,KAAK,OAAO,GAAG,cAAc,GAC9C,gBAAgB,EACf;UAAAwG,QAAA,EACA5G,YAAY,CAACI,IAAI,KAAK,SAAS,gBAC9BpC,OAAA;YAAK2I,SAAS,EAAC,SAAS;YAACW,OAAO,EAAC,WAAW;YAACD,IAAI,EAAC,cAAc;YAAAT,QAAA,eAC9D5I,OAAA;cAAMgL,QAAQ,EAAC,SAAS;cAACzE,CAAC,EAAC,uIAAuI;cAAC0E,QAAQ,EAAC;YAAS;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrL,CAAC,GACJ1G,YAAY,CAACI,IAAI,KAAK,OAAO,gBAC/BpC,OAAA;YAAK2I,SAAS,EAAC,SAAS;YAACW,OAAO,EAAC,WAAW;YAACD,IAAI,EAAC,cAAc;YAAAT,QAAA,eAC9D5I,OAAA;cAAMgL,QAAQ,EAAC,SAAS;cAACzE,CAAC,EAAC,yNAAyN;cAAC0E,QAAQ,EAAC;YAAS;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvQ,CAAC,gBAEN1I,OAAA;YAAK2I,SAAS,EAAC,SAAS;YAACW,OAAO,EAAC,WAAW;YAACD,IAAI,EAAC,cAAc;YAAAT,QAAA,eAC9D5I,OAAA;cAAMgL,QAAQ,EAAC,SAAS;cAACzE,CAAC,EAAC,kIAAkI;cAAC0E,QAAQ,EAAC;YAAS;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChL;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN1I,OAAA;UAAK2I,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB5I,OAAA;YAAG2I,SAAS,EAAE,uBACZ3G,YAAY,CAACI,IAAI,KAAK,SAAS,GAAG,gBAAgB,GAClDJ,YAAY,CAACI,IAAI,KAAK,OAAO,GAAG,cAAc,GAC9C,gBAAgB,EACf;YAAAwG,QAAA,EACA5G,YAAY,CAACG;UAAO;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN1I,OAAA;UAAK2I,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B5I,OAAA;YAAK2I,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9B5I,OAAA;cACE8J,OAAO,EAAEA,CAAA,KAAM7H,eAAe,CAAC;gBAAEC,IAAI,EAAE,KAAK;gBAAEC,OAAO,EAAE,EAAE;gBAAEC,IAAI,EAAE;cAAU,CAAC,CAAE;cAC9EuG,SAAS,EAAE,gCACT3G,YAAY,CAACI,IAAI,KAAK,SAAS,GAAG,+CAA+C,GACjFJ,YAAY,CAACI,IAAI,KAAK,OAAO,GAAG,+BAA+B,GAC/D,+CAA+C,iDACC;cAAAwG,QAAA,gBAElD5I,OAAA;gBAAM2I,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAO;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxC1I,OAAA;gBAAK2I,SAAS,EAAC,SAAS;gBAACW,OAAO,EAAC,WAAW;gBAACD,IAAI,EAAC,cAAc;gBAAAT,QAAA,eAC9D5I,OAAA;kBAAMgL,QAAQ,EAAC,SAAS;kBAACzE,CAAC,EAAC,oMAAoM;kBAAC0E,QAAQ,EAAC;gBAAS;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAlH,eAAe,IAAIN,eAAe,iBACjClB,OAAA;MAAK2I,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F5I,OAAA,CAACd,MAAM,CAAC8J,GAAG;QACTC,OAAO,EAAE;UAAEC,KAAK,EAAE,GAAG;UAAEhB,OAAO,EAAE;QAAE,CAAE;QACpCiB,OAAO,EAAE;UAAED,KAAK,EAAE,CAAC;UAAEhB,OAAO,EAAE;QAAE,CAAE;QAClCS,SAAS,EAAC,2EAA2E;QAAAC,QAAA,gBAErF5I,OAAA;UAAK2I,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD5I,OAAA;YAAI2I,SAAS,EAAC,kCAAkC;YAAAC,QAAA,GAAC,SACxC,EAAClH,UAAU,KAAK,MAAM,GAAG,QAAQ,GAAG,gBAAgB;UAAA;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACL1I,OAAA;YACE8J,OAAO,EAAEA,CAAA,KAAMrI,kBAAkB,CAAC,KAAK,CAAE;YACzCkH,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAE7C5I,OAAA;cAAKoJ,KAAK,EAAC,4BAA4B;cAACT,SAAS,EAAC,SAAS;cAACU,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAX,QAAA,eAC/G5I,OAAA;gBAAMwJ,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACnD,CAAC,EAAC;cAAsB;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN1I,OAAA;UAAK2I,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB5I,OAAA;YAAG2I,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAAC,SACzB,EAAClH,UAAU,KAAK,MAAM,GAAG,QAAQ,GAAG,gBAAgB,EAAC,OAAK,eAAA1B,OAAA;cAAM2I,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAE1H,eAAe,CAACuB;YAAQ;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,KACpI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJ1I,OAAA;YAAK2I,SAAS,EAAC,+JAA+J;YAACmB,OAAO,EAAEA,CAAA,KAAMzH,YAAY,CAAC6I,OAAO,CAACC,KAAK,CAAC,CAAE;YAAAvC,QAAA,GACxNlH,UAAU,KAAK,MAAM,gBACpB1B,OAAA,CAACJ,MAAM;cAAC+I,SAAS,EAAC;YAA8B;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEnD1I,OAAA,CAACH,QAAQ;cAAC8I,SAAS,EAAC;YAAgC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACvD,eACD1I,OAAA;cAAG2I,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAqB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClE1I,OAAA;cAAG2I,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAA2B;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzE1I,OAAA;cACEoC,IAAI,EAAC,MAAM;cACXgJ,GAAG,EAAE/I,YAAa;cAClB+H,QAAQ,EAAExF,gBAAiB;cAC3B+D,SAAS,EAAC,QAAQ;cAClB0C,MAAM,EAAC,SAAS;cAChBC,QAAQ;YAAA;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN1I,OAAA;YAAK2I,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzC5I,OAAA;cAAA4I,QAAA,EAAG;YAA2B;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClC1I,OAAA;cAAA4I,QAAA,EAAG;YAA+B;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1I,OAAA;UAAK2I,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzC5I,OAAA,CAACd,MAAM,CAACyK,MAAM;YACZC,UAAU,EAAE;cAAEV,KAAK,EAAE;YAAK,CAAE;YAC5BW,QAAQ,EAAE;cAAEX,KAAK,EAAE;YAAK,CAAE;YAC1BY,OAAO,EAAEA,CAAA,KAAMrI,kBAAkB,CAAC,KAAK,CAAE;YACzCkH,SAAS,EAAC,4GAA4G;YAAAC,QAAA,EACvH;UAED;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,eAChB1I,OAAA,CAACd,MAAM,CAACyK,MAAM;YACZC,UAAU,EAAE;cAAEV,KAAK,EAAE;YAAK,CAAE;YAC5BW,QAAQ,EAAE;cAAEX,KAAK,EAAE;YAAK,CAAE;YAC1BY,OAAO,EAAEA,CAAA,KAAMzH,YAAY,CAAC6I,OAAO,CAACC,KAAK,CAAC,CAAE;YAC5CxC,SAAS,EAAE,8BACTjH,UAAU,KAAK,MAAM,GACjB,qEAAqE,GACrE,yEAAyE,kEACZ;YAAAkH,QAAA,EACpE;UAED;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACvI,EAAA,CAz5BID,QAAQ;EAAA,QACYpB,OAAO,EACdF,WAAW;AAAA;AAAA2M,EAAA,GAFxBrL,QAAQ;AA25Bd,eAAeA,QAAQ;AAAC,IAAAqL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}